# SmartAdmin Vue3 项目开发规范 - Cursor Rules

## 项目概述
- **技术栈**: Vue3 + JavaScript + Ant Design Vue + Vite
- **项目类型**: 中后台管理系统
- **官方文档**: https://smartadmin.vip/
- **架构模式**: 组合式 Composition API + 模块化编写

## 目录结构规范

```
src/                              # 源码目录
├── api/                          # 所有API接口，按business、system、support拆分
│   ├── business/                 # 业务接口
│   │   ├── order/               # 订单相关接口
│   │   ├── complaint/           # 投诉相关接口
│   │   └── finance/             # 财务相关接口
│   ├── system/                  # 系统接口
│   │   ├── user-api.js          # 用户管理接口
│   │   ├── role-api.js          # 角色管理接口
│   │   └── menu-api.js          # 菜单管理接口
│   └── support/                 # 支撑接口
│       ├── file-api.js          # 文件管理接口
│       ├── dict-api.js          # 字典管理接口
│       └── cache-api.js         # 缓存管理接口
├── assets/                       # 静态资源：images、icons、styles
├── components/                   # 公用组件，按business、system、support拆分
│   ├── business/                # 业务组件
│   ├── system/                  # 系统组件
│   │   ├── user-select/         # 用户选择器
│   │   └── department-tree-select/ # 部门树选择器
│   ├── support/                 # 支撑组件
│   │   ├── file-upload/         # 文件上传
│   │   ├── table-operator/      # 表格操作
│   │   └── dict-select/         # 字典选择
│   └── framework/               # 框架组件
│       ├── smart-loading/       # 加载组件
│       └── text-ellipsis/       # 文本省略
├── config/                       # 配置信息（项目配置）
├── constants/                    # 常量信息，项目所有Enum、全局常量等
│   ├── business/                # 业务常量
│   ├── system/                  # 系统常量
│   ├── support/                 # 支撑常量
│   └── common-const.js          # 通用常量
├── directives/                   # 自定义指令
├── i18n/                         # 国际化
├── lib/                          # 外部引用的插件存放及修改文件
├── plugins/                      # 插件，全局使用
├── router/                       # 路由，统一管理
├── store/                        # pinia状态，按business、system、support拆分
├── theme/                        # 自定义样式主题
├── utils/                        # 工具类
└── views/                        # 视图目录，按business、system、support拆分
    ├── business/                 # 业务目录
    │   ├── order/               # 订单管理
    │   │   ├── order-list.vue   # 订单列表
    │   │   ├── order-detail.vue # 订单详情
    │   │   └── components/      # 订单子组件
    │   ├── complaint/           # 投诉管理
    │   │   ├── complaint-list.vue # 投诉列表
    │   │   └── components/      # 投诉子组件
    │   │       ├── complaint-detail-modal.vue # 投诉详情弹窗
    │   │       └── complaint-handle-modal.vue # 投诉处理弹窗
    │   └── finance/             # 财务管理
    ├── system/                  # 系统目录
    │   ├── user/                # 用户管理
    │   ├── role/                # 角色管理
    │   ├── menu/                # 菜单管理
    │   └── login/               # 登录相关
    └── support/                 # 支撑目录
        ├── dict/                # 字典管理
        ├── file/                # 文件管理
        └── cache/               # 缓存管理
```

## 代码分类与目录规范

### 1. Business（业务目录）
**放置业务相关的代码，包含核心业务功能**

**适用场景：**
- 订单管理、支付管理、商品管理
- 客户管理、供应商管理、投诉管理
- 财务管理、库存管理
- 营销活动、会员管理

### 2. System（系统目录）
**放置系统管理相关的代码，包含权限、用户、配置等**

**适用场景：**
- 用户管理、角色管理、权限管理
- 菜单管理、字典管理、部门管理
- 系统配置、参数设置
- 组织架构、员工管理
- 登录日志、操作日志

### 3. Support（支撑目录）
**放置支撑性功能代码，包含通用工具、第三方集成等**

**适用场景：**
- 文件管理、图片上传、字典管理
- 缓存管理、配置管理
- 代码生成、数据导入导出
- 监控大屏、统计报表
- 第三方服务集成

## 环境配置规范

### 五个环境配置文件：
- `.env.localhost` - 本地环境，执行 `npm run localhost`
- `.env.development` - 开发环境，执行 `npm run dev`
- `.env.test` - 测试环境打包，执行 `npm run build:test`
- `.env.pre` - 预发布环境打包，执行 `npm run build:pre` 
- `.env.production` - 生产环境打包，执行 `npm run build:prod`

### 环境变量格式：
```bash
NODE_ENV=development
VITE_APP_TITLE='项目标题'
VITE_APP_API_URL='API地址'
```

## 代码编写规范

### 1. 文件命名规范
- **API文件**: 以 `-api.js` 结尾，如 `complaint-api.js`
- **常量文件**: 以 `-const.js` 结尾，如 `complaint-const.js`
- **Vue组件**: 使用 kebab-case，如 `complaint-list.vue`
- **父子组件**: 子组件文件名包含父组件名称
- **弹窗组件**: 以 `-modal.vue` 结尾，如 `complaint-detail-modal.vue`
- **表单组件**: 以 `-form.vue` 结尾，如 `complaint-form.vue`

### 2. Vue3 组合式API编写要求
```javascript
// 推荐的组合式API结构
<template>
  <!-- 查询表单 -->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="字段名" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.fieldName" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon><SearchOutlined /></template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon><ReloadOutlined /></template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>

  <!-- 表格卡片 -->
  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 表格操作行 -->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button type="primary" @click="handleAdd">
          <template #icon><PlusOutlined /></template>
          新增
        </a-button>
        <a-button @click="handleExport">
          <template #icon><ExportOutlined /></template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator 
          v-model="columns" 
          :tableId="TABLE_ID_CONST.BUSINESS.XXX.LIST" 
          :refresh="queryData" 
        />
      </div>
    </a-row>

    <!-- 数据表格 -->
    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      :scroll="{ x: 1800, y: 600 }"
      bordered
      :pagination="false"
      :loading="tableLoading"
      @resizeColumn="handleResizeColumn"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getStatusColor(record.status)">
            {{ getStatusText(record.status) }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button @click="handleEdit(record)" type="link">编辑</a-button>
            <a-button @click="handleView(record)" type="link">查看</a-button>
            <a-popconfirm title="确定删除吗？" @confirm="handleDelete(record)">
              <a-button type="link" danger>删除</a-button>
            </a-popconfirm>
          </div>
        </template>
      </template>
    </a-table>

    <!-- 分页组件 -->
    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共 ${total} 条数据`"
      />
    </div>
  </a-card>

  <!-- 子组件弹窗 -->
  <XxxFormModal ref="formModal" @reloadList="queryData" />
  <XxxDetailModal ref="detailModal" />
</template>

<script setup>
// 必需的导入
import { ref, reactive, onMounted } from 'vue'
import { message } from 'ant-design-vue'
import { 
  SearchOutlined, 
  ReloadOutlined, 
  PlusOutlined,
  ExportOutlined 
} from '@ant-design/icons-vue'
import { SmartLoading } from '/@/components/framework/smart-loading'
import { xxxApi } from '/@/api/business/xxx/xxx-api'
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const'
import { TABLE_ID_CONST } from '/@/constants/support/table-id-const'
import { XXX_STATUS_ENUM } from '/@/constants/business/xxx/xxx-const'
import { smartSentry } from '/@/lib/smart-sentry'
import TableOperator from '/@/components/support/table-operator/index.vue'
import XxxFormModal from './components/xxx-form-modal.vue'
import XxxDetailModal from './components/xxx-detail-modal.vue'
import _ from 'lodash'

// ---------------------------- 表格列配置 ----------------------------
const columns = ref([
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
    resizable: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    resizable: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    resizable: true,
    fixed: 'right'
  }
])

// ---------------------------- 查询表单 ----------------------------
const queryFormState = {
  fieldName: '',
  status: '',
  pageNum: 1,
  pageSize: 10
}

const queryForm = reactive(_.cloneDeep(queryFormState))
const tableLoading = ref(false)
const tableData = ref([])
const total = ref(0)

// ---------------------------- 查询方法 ----------------------------
async function queryData() {
  tableLoading.value = true
  try {
    const result = await xxxApi.query(queryForm)
    tableData.value = result.data.list
    total.value = result.data.total
  } catch (e) {
    smartSentry.captureError(e)
  } finally {
    tableLoading.value = false
  }
}

function resetQuery() {
  let pageSize = queryForm.pageSize
  Object.assign(queryForm, _.cloneDeep(queryFormState))
  queryForm.pageSize = pageSize
  queryData()
}

function onSearch() {
  queryForm.pageNum = 1
  queryData()
}

function handleResizeColumn(w, col) {
  columns.value.forEach((item) => {
    if (item.dataIndex === col.dataIndex) {
      item.width = Math.floor(w)
    }
  })
}

// ---------------------------- 操作方法 ----------------------------
const formModal = ref()
const detailModal = ref()

function handleAdd() {
  formModal.value.showModal()
}

function handleEdit(record) {
  formModal.value.showModal(record)
}

function handleView(record) {
  detailModal.value.showModal(record)
}

async function handleDelete(record) {
  try {
    await xxxApi.delete(record.id)
    message.success('删除成功')
    queryData()
  } catch (e) {
    smartSentry.captureError(e)
  }
}

async function handleExport() {
  try {
    SmartLoading.show()
    await xxxApi.export(queryForm)
    message.success('导出成功')
  } catch (e) {
    smartSentry.captureError(e)
  } finally {
    SmartLoading.hide()
  }
}

// ---------------------------- 状态处理 ----------------------------
function getStatusColor(status) {
  const statusEnum = Object.values(XXX_STATUS_ENUM).find(item => item.value === status)
  return statusEnum ? statusEnum.color : 'default'
}

function getStatusText(status) {
  const statusEnum = Object.values(XXX_STATUS_ENUM).find(item => item.value === status)
  return statusEnum ? statusEnum.label : '未知'
}

onMounted(queryData)
</script>

<style scoped>
.smart-table-operate {
  display: flex;
  gap: 8px;
}

.text-gray {
  color: #8c8c8c;
  font-size: 12px;
}
</style>
```

### 3. API接口编写规范
```javascript
// xxx-api.js
import { postRequest, getRequest } from '/@/lib/axios'

export const xxxApi = {
  // 查询列表
  query: (params) => {
    return postRequest('/xxx/query', params)
  },
  
  // 获取详情
  getDetail: (id) => {
    return getRequest(`/xxx/detail/${id}`)
  },
  
  // 添加
  add: (data) => {
    return postRequest('/xxx/add', data)
  },
  
  // 更新
  update: (data) => {
    return postRequest('/xxx/update', data)
  },
  
  // 删除
  delete: (id) => {
    return getRequest(`/xxx/delete/${id}`)
  },

  // 批量删除
  batchDelete: (ids) => {
    return postRequest('/xxx/batch/delete', ids)
  },

  // 导出
  export: (params) => {
    return postRequest('/xxx/export', params)
  }
}
```

### 4. 常量定义规范
```javascript
// xxx-const.js
// 状态枚举
export const XXX_STATUS_ENUM = {
  PENDING: { value: 1, label: '待处理', color: 'orange' },
  PROCESSING: { value: 2, label: '处理中', color: 'blue' },
  COMPLETED: { value: 3, label: '已完成', color: 'green' },
  REJECTED: { value: 4, label: '已拒绝', color: 'red' }
}

// 表单验证规则
export const XXX_FORM_RULES = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  status: [
    { required: true, message: '请选择状态', trigger: 'change' }
  ]
}
```

## 弹窗组件规范

### 1. 详情弹窗组件
```javascript
// xxx-detail-modal.vue
<template>
  <a-modal
    v-model:open="modalVisible"
    title="详情"
    :width="800"
    @cancel="handleCancel"
    @ok="handleOk"
  >
    <a-spin :spinning="loading">
      <div v-if="detailData" style="padding: 20px 0;">
        <!-- 基础信息 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">字段名</div>
          <div>{{ detailData.fieldName || '-' }}</div>
        </div>

        <!-- 状态信息 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">状态</div>
          <a-tag :color="getStatusColor(detailData.status)">
            {{ getStatusText(detailData.status) }}
          </a-tag>
        </div>

        <!-- 图片信息 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">图片</div>
          <div v-if="detailData.images && detailData.images.length > 0" style="display: flex; gap: 12px;">
            <div 
              v-for="(image, index) in detailData.images" 
              :key="index"
              style="width: 120px; height: 120px; background-color: #f5f5f5; border-radius: 4px;"
            ></div>
          </div>
          <div v-else style="color: #999;">暂无图片</div>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue'
import { xxxApi } from '/@/api/business/xxx/xxx-api'
import { XXX_STATUS_ENUM } from '/@/constants/business/xxx/xxx-const'
import { smartSentry } from '/@/lib/smart-sentry'

const modalVisible = ref(false)
const loading = ref(false)
const detailData = ref(null)

async function showModal(record) {
  modalVisible.value = true
  loading.value = true
  
  try {
    const result = await xxxApi.getDetail(record.id)
    detailData.value = result.data
  } catch (error) {
    smartSentry.captureError(error)
  } finally {
    loading.value = false
  }
}

function handleCancel() {
  modalVisible.value = false
  detailData.value = null
}

function handleOk() {
  modalVisible.value = false
  detailData.value = null
}

function getStatusColor(status) {
  const statusEnum = Object.values(XXX_STATUS_ENUM).find(item => item.value === status)
  return statusEnum ? statusEnum.color : 'default'
}

function getStatusText(status) {
  const statusEnum = Object.values(XXX_STATUS_ENUM).find(item => item.value === status)
  return statusEnum ? statusEnum.label : '未知'
}

defineExpose({
  showModal
})
</script>
```

### 2. 表单弹窗组件
```javascript
// xxx-form-modal.vue
<template>
  <a-modal
    v-model:open="modalVisible"
    :title="modalTitle"
    :width="600"
    @cancel="handleCancel"
    @ok="handleOk"
    :confirmLoading="submitLoading"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="名称" name="name">
        <a-input v-model:value="formData.name" placeholder="请输入名称" />
      </a-form-item>
      
      <a-form-item label="状态" name="status">
        <a-select v-model:value="formData.status" placeholder="请选择状态">
          <a-select-option 
            v-for="item in Object.values(XXX_STATUS_ENUM)" 
            :key="item.value" 
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="描述" name="description">
        <a-textarea 
          v-model:value="formData.description" 
          placeholder="请输入描述" 
          :rows="4"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { message } from 'ant-design-vue'
import { xxxApi } from '/@/api/business/xxx/xxx-api'
import { XXX_STATUS_ENUM, XXX_FORM_RULES } from '/@/constants/business/xxx/xxx-const'
import { smartSentry } from '/@/lib/smart-sentry'

const modalVisible = ref(false)
const modalTitle = ref('')
const submitLoading = ref(false)
const formRef = ref()

const formData = reactive({
  id: null,
  name: '',
  status: '',
  description: ''
})

const formRules = XXX_FORM_RULES

function showModal(record = null) {
  modalVisible.value = true
  modalTitle.value = record ? '编辑' : '新增'
  
  if (record) {
    Object.assign(formData, record)
  } else {
    resetForm()
  }
}

async function handleOk() {
  try {
    await formRef.value.validate()
    submitLoading.value = true
    
    if (formData.id) {
      await xxxApi.update(formData)
      message.success('更新成功')
    } else {
      await xxxApi.add(formData)
      message.success('新增成功')
    }
    
    modalVisible.value = false
    emit('reloadList')
  } catch (e) {
    if (e.errorFields) {
      // 表单验证错误
      return
    }
    smartSentry.captureError(e)
  } finally {
    submitLoading.value = false
  }
}

function handleCancel() {
  modalVisible.value = false
  resetForm()
}

function resetForm() {
  formRef.value?.resetFields()
  Object.assign(formData, {
    id: null,
    name: '',
    status: '',
    description: ''
  })
}

const emit = defineEmits(['reloadList'])

defineExpose({
  showModal
})
</script>
```

## 表格开发规范

### 1. 表格基础配置
```javascript
// 表格列配置
const columns = ref([
  {
    title: '序号',
    dataIndex: 'index',
    width: 80,
    customRender: ({ index }) => index + 1
  },
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,
    resizable: true,
    ellipsis: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 120,
    resizable: true
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 180,
    resizable: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    fixed: 'right'
  }
])
```

### 2. 复杂列渲染
```javascript
<template #bodyCell="{ text, record, column }">
  <!-- 复合信息显示 -->
  <template v-if="column.dataIndex === 'userInfo'">
    <div class="user-info-cell">
      <div>{{ record.userName }}</div>
      <div class="text-gray">{{ record.userPhone }}</div>
      <div class="text-gray">部门：{{ record.department }}</div>
    </div>
  </template>

  <!-- 订单信息显示 -->
  <template v-if="column.dataIndex === 'orderInfo'">
    <div class="order-info-cell">
      <div>{{ record.productName }}</div>
      <div class="text-gray">订单编号：{{ record.orderNo }}</div>
      <div class="text-gray">收款方式：{{ record.paymentMethod }}</div>
      <div class="text-gray">订单金额：{{ record.orderAmount }}</div>
      <div class="text-gray">创建人：{{ record.creator }}</div>
      <div class="text-gray">下单时间：{{ record.createTime }}</div>
    </div>
  </template>

  <!-- 状态标签 -->
  <template v-if="column.dataIndex === 'status'">
    <a-tag :color="getStatusColor(record.status)">
      {{ getStatusText(record.status) }}
    </a-tag>
  </template>

  <!-- 长文本省略 -->
  <template v-if="column.dataIndex === 'description'">
    <div style="max-width: 200px; word-break: break-all;">
      {{ text }}
    </div>
  </template>

  <!-- 操作按钮 -->
  <template v-if="column.dataIndex === 'action'">
    <div class="smart-table-operate">
      <a-button @click="handleEdit(record)" type="link">编辑</a-button>
      <a-button @click="handleView(record)" type="link">查看</a-button>
      <a-popconfirm title="确定删除吗？" @confirm="handleDelete(record)">
        <a-button type="link" danger>删除</a-button>
      </a-popconfirm>
    </div>
  </template>
</template>
```

## 查询表单规范

### 1. 标准查询表单
```javascript
<a-form class="smart-query-form">
  <a-row class="smart-query-form-row">
    <!-- 基础输入 -->
    <a-form-item label="订单号" class="smart-query-form-item">
      <a-input 
        style="width: 200px" 
        v-model:value="queryForm.orderNo" 
        placeholder="请输入" 
      />
    </a-form-item>

    <!-- 下拉选择 -->
    <a-form-item label="状态" class="smart-query-form-item">
      <a-select 
        style="width: 120px" 
        v-model:value="queryForm.status" 
        placeholder="全部" 
        allowClear
      >
        <a-select-option value="">全部</a-select-option>
        <a-select-option value="1">启用</a-select-option>
        <a-select-option value="0">禁用</a-select-option>
      </a-select>
    </a-form-item>

    <!-- 日期范围 -->
    <a-form-item label="创建日期" class="smart-query-form-item">
      <a-range-picker 
        style="width: 240px" 
        v-model:value="queryForm.createDateRange" 
        format="YYYY/MM/DD"
        :placeholder="['开始日期', '结束日期']"
      />
    </a-form-item>

    <!-- 数值范围 -->
    <a-form-item label="价格范围" class="smart-query-form-item">
      <a-input 
        style="width: 120px" 
        v-model:value="queryForm.priceFrom" 
        placeholder="最低价" 
      />
      <span style="margin: 0 10px;">至</span>
      <a-input 
        style="width: 120px" 
        v-model:value="queryForm.priceTo" 
        placeholder="最高价" 
      />
    </a-form-item>

    <!-- 操作按钮 -->
    <a-form-item class="smart-query-form-item">
      <a-button-group>
        <a-button type="primary" @click="onSearch">
          <template #icon><SearchOutlined /></template>
          查询
        </a-button>
        <a-button @click="resetQuery">
          <template #icon><ReloadOutlined /></template>
          重置
        </a-button>
      </a-button-group>
    </a-form-item>
  </a-row>
</a-form>
```

## 路由和权限规范

### 1. 路由配置
```javascript
// router/business/xxx.js
export default {
  path: '/business/xxx',
  name: 'BusinessXxx',
  component: () => import('/@/views/business/xxx/xxx-list.vue'),
  meta: {
    title: 'XXX管理',
    requireAuth: true
  }
}
```

### 2. 权限控制
```javascript
// 使用权限指令
<a-button v-privilege="'business:xxx:add'" @click="handleAdd">
  新增
</a-button>

<a-button v-privilege="'business:xxx:edit'" @click="handleEdit(record)">
  编辑
</a-button>

<a-button v-privilege="'business:xxx:delete'" @click="handleDelete(record)">
  删除
</a-button>
```

## 数据处理规范

### 1. 数据格式化
```javascript
// 使用项目内置的数据格式化工具
import { dataFormatter } from '/@/utils/data-formatter'

// 手机号脱敏
const maskedPhone = dataFormatter.maskPhone('***********') // 138****5678

// 金额格式化
const formattedMoney = dataFormatter.money(1234.56) // ¥1,234.56

// 日期格式化
const formattedDate = dataFormatter.dateTime(new Date()) // 2024-01-01 12:00:00
```

### 2. 枚举值处理
```javascript
// 获取枚举标签
function getStatusText(value) {
  const statusEnum = Object.values(XXX_STATUS_ENUM).find(item => item.value === value)
  return statusEnum ? statusEnum.label : '未知'
}

// 获取枚举颜色
function getStatusColor(value) {
  const statusEnum = Object.values(XXX_STATUS_ENUM).find(item => item.value === value)
  return statusEnum ? statusEnum.color : 'default'
}
```

## 错误处理规范

### 1. 统一错误处理
```javascript
import { smartSentry } from '/@/lib/smart-sentry'

async function handleOperation() {
  try {
    const result = await xxxApi.operation()
    message.success('操作成功')
    return result
  } catch (e) {
    smartSentry.captureError(e)
    // 错误会被全局处理，无需手动处理
  } finally {
    loading.value = false
  }
}
```

### 2. 加载状态处理
```javascript
// 表格加载状态
const tableLoading = ref(false)

// 按钮加载状态
const submitLoading = ref(false)

// 使用SmartLoading全局加载
import { SmartLoading } from '/@/components/framework/smart-loading'

async function handleExport() {
  try {
    SmartLoading.show()
    await xxxApi.export(queryForm)
    message.success('导出成功')
  } catch (e) {
    smartSentry.captureError(e)
  } finally {
    SmartLoading.hide()
  }
}
```

## 样式规范

### 1. 通用样式类
```css
/* 查询表单样式 */
.smart-query-form { }
.smart-query-form-row { }
.smart-query-form-item { }

/* 表格样式 */
.smart-table-btn-block { }
.smart-table-operate-block { }
.smart-table-setting-block { }
.smart-table-operate { display: flex; gap: 8px; }
.smart-query-table-page { }

/* 通用文本样式 */
.text-gray { color: #8c8c8c; font-size: 12px; }
.text-primary { color: #1890ff; }
.text-success { color: #52c41a; }
.text-error { color: #ff4d4f; }

/* 信息单元格样式 */
.user-info-cell { line-height: 1.4; }
.order-info-cell { line-height: 1.4; }
```

## 性能优化规范

### 1. 组件懒加载
```javascript
// 路由懒加载
const XxxList = () => import('/@/views/business/xxx/xxx-list.vue')

// 组件懒加载
const XxxModal = defineAsyncComponent(() => 
  import('./components/xxx-modal.vue')
)
```

### 2. 表格优化
```javascript
// 列宽优化
const columns = ref([
  {
    title: '名称',
    dataIndex: 'name',
    width: 200,        // 固定宽度
    minWidth: 150,     // 最小宽度
    resizable: true,   // 可调整
    ellipsis: true     // 超出省略
  }
])

// 虚拟滚动（大数据量时）
<a-table
  :scroll="{ x: 1200, y: 600 }"
  :virtual="true"
  :dataSource="tableData"
/>
```

## 测试规范

### 1. 功能测试检查点
- [ ] 列表查询功能正常
- [ ] 分页功能正常
- [ ] 增删改查功能正常
- [ ] 表单验证功能正常
- [ ] 权限控制功能正常
- [ ] 错误处理功能正常
- [ ] 加载状态显示正常
- [ ] 响应式布局正常

### 2. 性能测试检查点
- [ ] 首屏加载时间 < 3秒
- [ ] 列表查询响应时间 < 2秒
- [ ] 表格滚动流畅
- [ ] 大数据量渲染正常
- [ ] 内存占用合理

## 部署相关

### 构建命令
- 本地开发: `npm run localhost`
- 开发环境: `npm run dev`
- 测试环境: `npm run build:test`
- 预发布环境: `npm run build:pre`
- 生产环境: `npm run build:prod`

### 环境切换
通过修改对应的`.env.xxx`文件中的`VITE_APP_API_URL`来切换不同环境的API地址。

### 部署检查清单
- [ ] 环境变量配置正确
- [ ] API地址配置正确
- [ ] 静态资源路径正确
- [ ] 路由配置正确
- [ ] 权限配置正确
- [ ] 错误监控配置正确

---

**重要提醒**: 
1. 严格按照目录结构组织代码
2. 遵循文件命名规范
3. 使用组合式API编写Vue组件
4. 统一使用项目内置的工具类和组件
5. 在菜单管理中配置路由后才能访问页面
6. 查看在线代码示例: https://smartadmin.vip/
7. 所有API调用必须进行错误处理
8. 表格组件必须支持列宽调整和排序
9. 弹窗组件必须包含加载状态和错误处理
10. 查询表单必须支持重置和分页
11. 列表页面必须包含TableOperator组件
12. 使用smartSentry进行错误监控
13. 使用SmartLoading进行全局加载提示
14. 弹窗组件必须正确处理确定和取消按钮
15. 图片展示时无图片要显示"暂无图片"