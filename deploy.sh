#!/bin/bash

set -e

if [ "$#" -ne 1 ]; then
    echo "Usage: $0 [test|prod]"
    exit 1
fi

ENV=$1

git pull

if [ "$ENV" = "test" ]; then
    TARGET_IP="*************"
    BUILD_CMD="npm run build:test"
elif [ "$ENV" = "prod" ]; then
    TARGET_IP="*************"
    BUILD_CMD="npm run build:prod"
else
    echo "Invalid environment: $ENV"
    echo "Usage: $0 [test|prod]"
    exit 1
fi

echo "Building for $ENV environment..."
$BUILD_CMD

echo "Cleaning up old deployment on server..."
ssh -p 60022 root@$TARGET_IP "rm -rf /opt/ryf-admin-web/dist_old"
ssh -p 60022 root@$TARGET_IP "if [ -d /opt/ryf-admin-web/dist ]; then mv /opt/ryf-admin-web/dist /opt/ryf-admin-web/dist_old; fi"

echo "Uploading new build to server..."
scp -P 60022 -r dist root@$TARGET_IP:/opt/ryf-admin-web

echo "Deployment to $ENV ($TARGET_IP) completed."
