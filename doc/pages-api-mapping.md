# 页面路径与API接口映射文档

本文档列出了系统中各个业务模块的页面路径及其对应的API接口地址。

## 1. 订单管理模块

### 1.1 分段订单管理

**页面文件**: `src/views/business/order/segment-order-list.vue`  
**页面路径**: `/business/order/segment-order-list` (动态路由)  
**API文件**: `src/api/business/order/segment-order-api.js`

主要API接口：
- 查询分期订单分页列表: `POST /orderInstallment/queryPage`
- 查询分期订单统计数据: `POST /orderInstallment/queryPageTotal`
- 获取分段订单详情: `GET /orderInstallment/get/{orderId}`
- 查询收款计划: `POST /orderInstallmentDeduct/queryPage`
- 商家解约订单: `POST /orderInstallment/{orderId}/merchantTerminated`
- 添加订单备注: `POST /orderInstallment/{orderId}/remark`
- 延期扣款: `POST /orderInstallment/{orderId}/delayDeduct`
- 线下收款: `POST /orderInstallment/{orderId}/offlinePayment`

### 1.2 分段订单详情

**页面文件**: `src/views/business/order/segment-order-detail.vue`  
**页面路径**: `/business/order/segment-order-detail` (动态路由)  
**API文件**: `src/api/business/order/segment-order-api.js`

主要API接口：
- 获取分段订单详情: `GET /orderInstallment/get/{orderId}`
- 获取订单近期扣款日期: `GET /orderInstallment/{orderId}/deductDay`
- 获取扣款记录: `GET /admin-api/business/segment-order/deduction-records/{orderId}`

### 1.3 银行卡授权管理

**页面文件**: `src/views/business/order/bank-card-authorization-list.vue`  
**页面路径**: `/business/order/bank-card-authorization-list` (动态路由)  
**API文件**: `src/api/business/order/bank-card-authorization-api.js`

主要API接口：
- 银行卡授权分页查询: `POST /orderInstallment/bankcardPage`
- 解绑银行卡: `POST /orderInstallment/cancelBind/{customerBankcardId}`
- 获取统计信息: `GET /admin-api/business/bank-card-authorization/statistics`
- 导出银行卡授权: `POST /admin-api/business/bank-card-authorization/export`

### 1.4 银行卡授权详情

**页面文件**: `src/views/business/order/bank-card-authorization-detail.vue`  
**页面路径**: `/business/order/bank-card-authorization-detail` (动态路由)  
**API文件**: `src/api/business/order/bank-card-authorization-api.js`

主要API接口：
- 获取银行卡授权详情: `GET /admin-api/business/bank-card-authorization/detail/{authorizationId}`
- 获取用户订单列表: `GET /admin-api/business/bank-card-authorization/user-orders/{userId}`
- 获取授权历史: `GET /admin-api/business/bank-card-authorization/history/{userId}`

## 2. 解约申请管理

**页面文件**: `src/views/business/order/cancellation-request-list.vue`  
**页面路径**: `/business/order/cancellation-request-list` (动态路由)  
**API文件**: `src/api/business/order/cancellation-request-api.js`

主要API接口：
- 查询解约申请列表: `POST /afterSales/queryPage`
- 获取解约申请详情: `GET /afterSales/{afterSalesId}`
- 审核解约申请: `POST /afterSales/handle`
- 获取审核历史: `GET /admin-api/business/cancellation-request/approval-history/{requestId}`
- 导出解约申请: `POST /afterSales/export`
- 批量审核解约申请: `POST /admin-api/business/cancellation-request/batch-approve`

## 3. 投诉管理模块

### 3.1 投诉列表

**页面文件**: `src/views/business/complaint/complaint-list.vue`  
**页面路径**: `/business/complaint/complaint-list` (动态路由)  
**API文件**: `src/api/business/complaint/complaint-api.js`

主要API接口：
- 查询投诉列表: `POST /complaint/queryPage`
- 处理投诉: `POST /complaint/handle`
- 批量处理投诉: `POST /complaint/batchHandle`
- 获取投诉详情: `GET /complaint/{id}`
- 删除投诉记录: `GET /complaint/delete/{id}`
- 导出投诉数据: `GET /complaint/export`

### 3.2 投诉处理

投诉处理功能集成在投诉列表页面中，通过弹窗组件实现：
- 处理详情弹窗组件: `src/views/business/complaint/components/complaint-detail-modal.vue`
- 投诉处理弹窗组件: `src/views/business/complaint/components/complaint-handle-modal.vue`

## 4. 财务管理模块

### 4.1 收支明细

**页面文件**: `src/views/business/finance/finance-detail-list.vue`  
**页面路径**: `/business/finance/finance-detail-list` (动态路由)  
**API文件**: `src/api/business/finance/finance-detail-api.js`

主要API接口：
- 查询收支明细列表: `POST /orderInstallment/incomeDetailsPage`
- 获取收支统计信息: `POST /orderInstallmentDeduct/incomeDetailsPageTotal`
- 获取收支明细详情: `GET /finance/detail/{id}`
- 导出收支明细: `GET /finance/detail/export`
- 更新收支明细备注: `POST /finance/detail/updateRemark`

### 4.2 分段明细

**页面文件**: `src/views/business/finance/segment-detail-list.vue`  
**页面路径**: `/business/finance/segment-detail-list` (动态路由)  
**API文件**: `src/api/business/finance/segment-detail-api.js`

主要API接口：
- 查询分段明细列表: `POST /orderInstallment/installmentDetailsPage`
- 获取分段统计信息: `POST /orderInstallmentDeduct/installmentDetailsPageTotal`
- 导出分段明细: `GET /segmentDetail/exportExcel`
- 获取分段明细详情: `GET /segmentDetail/get/{segmentId}`
- 手动扣款: `POST /segmentDetail/manualDeduction`
- 重新扣款: `POST /segmentDetail/retryPayment`

### 4.3 逾期明细

**页面文件**: `src/views/business/finance/overdue-detail-list.vue`  
**页面路径**: `/business/finance/overdue-detail-list` (动态路由)  
**API文件**: `src/api/business/finance/overdue-detail-api.js`

主要API接口：
- 查询逾期明细列表: `POST /orderInstallment/overdueDetailsPage`
- 获取逾期统计信息: `POST /orderInstallmentDeduct/overdueDetailsPageTotal`
- 导出逾期明细: `GET /overdueDetail/exportExcel`
- 获取逾期明细详情: `GET /overdueDetail/get/{overdueId}`
- 批量处理逾期订单: `POST /overdueDetail/batchProcess`
- 更新代扣状态: `POST /overdueDetail/updateWithholdStatus`

## 5. 商品管理模块

### 5.1 安心付商品管理

**页面文件**: `src/views/business/goods/anxin-goods-list.vue`  
**页面路径**: `/business/goods/anxin-goods-list` (动态路由)  
**API文件**: `src/api/business/goods/anxin-goods-api.js`

主要API接口：
- 查询安心付商品列表: `POST /anxinGoods/queryPage`
- 添加安心付商品: `POST /anxinGoods/add`
- 更新安心付商品: `POST /anxinGoods/update`
- 删除安心付商品: `POST /anxinGoods/delete`
- 获取安心付商品详情: `GET /anxinGoods/get/{goodsId}`
- 上架/下架安心付商品: `POST /anxinGoods/updateStatus`
- 导出安心付商品: `GET /anxinGoods/exportExcel`
- 生成商品二维码: `GET /anxinGoods/qrcode/{goodsId}`

### 5.2 安心付商品表单

**页面文件**: `src/views/business/goods/anxin-goods-form.vue`  
**页面路径**: `/business/goods/anxin-goods-form` (动态路由)  
**API文件**: `src/api/business/goods/anxin-goods-api.js`

主要API接口：
- 添加安心付商品: `POST /anxinGoods/add`
- 更新安心付商品: `POST /anxinGoods/update`
- 获取安心付商品详情: `GET /anxinGoods/get/{goodsId}`

### 5.3 账单付商品列表

**页面文件**: `src/views/business/goods/bill-goods-list.vue`  
**页面路径**: `/business/goods/bill-goods-list` (动态路由)  
**API文件**: `src/api/business/goods/bill-goods-api.js`

主要API接口：
- 查询账单付商品列表: `POST /product/queryPage`
- 删除账单付商品: `GET /product/delete/{productId}`
- 获取账单付商品详情: `GET /product/get/{productId}`
- 上架/下架账单付商品: `POST /billGoods/updateStatus`
- 导出账单付商品: `GET /billGoods/exportExcel`
- 生成商品二维码: 通过本地方法生成支付宝小程序链接

### 5.4 账单付商品表单

**页面文件**: `src/views/business/goods/bill-goods-form.vue`  
**页面路径**: `/business/goods/bill-goods-form` (动态路由)  
**API文件**: `src/api/business/goods/bill-goods-api.js`

主要API接口：
- 添加账单付商品: `POST /product/add`
- 更新账单付商品: `POST /product/update`
- 获取账单付商品详情: `GET /product/get/{productId}`
- 获取可复制的商品列表: `POST /product/copy`
- 上传文件: `POST /support/file/upload?folder=1`
- 获取合同模版列表: `GET /store/contractTemplate`

## 页面路由说明

本项目使用动态路由机制，具体路由路径由菜单配置动态生成。上述路径为推测的标准路径格式，实际访问路径需要根据系统菜单配置确定。

路由生成逻辑位于 `src/router/index.js` 的 `buildRoutes` 函数中，页面组件通过菜单配置的 `component` 字段进行关联。

## 常量定义

各模块的常量定义文件：
- 订单相关: `src/constants/business/order/`
- 投诉相关: `src/constants/business/complaint/complaint-const.js`
- 财务相关: `src/constants/business/finance/`
- 商品相关: `src/constants/business/goods/`

---

*文档生成时间: 2024年*  
*项目: 欢乐付管理后台* 