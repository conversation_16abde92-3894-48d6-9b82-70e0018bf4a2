# Views 目录树结构

```
src/views/
├── business/                                   # 业务模块
│   ├── erp/                                   # ERP 企业资源计划
│   │   ├── catalog/                           # 分类目录管理
│   │   │   ├── components/
│   │   │   │   ├── category-form-modal.vue
│   │   │   │   └── category-tree-table.vue
│   │   │   ├── custom-catalog.vue
│   │   │   └── goods-catalog.vue
│   │   ├── goods/                             # 商品管理
│   │   │   ├── components/
│   │   │   │   └── goods-form-modal.vue
│   │   │   └── goods-list.vue
│   │   └── order/                             # 订单管理 (空目录)
│   ├── oa/                                    # 办公自动化
│   │   ├── enterprise/                        # 企业管理
│   │   │   ├── components/
│   │   │   │   ├── enterprise-bank-list.vue
│   │   │   │   ├── enterprise-bank-operate-modal.vue
│   │   │   │   ├── enterprise-employee-list.vue
│   │   │   │   ├── enterprise-invoice-list.vue
│   │   │   │   ├── enterprise-invoice-operate-modal.vue
│   │   │   │   └── enterprise-operate-modal.vue
│   │   │   ├── enterprise-detail.vue
│   │   │   └── enterprise-list.vue
│   │   └── notice/                            # 通知公告
│   │       ├── components/
│   │       │   ├── notice-form-drawer.vue
│   │       │   ├── notice-form-visible-modal.vue
│   │       │   ├── notice-form-visible-transfer-department.vue
│   │       │   ├── notice-form-visible-transfer-employee.vue
│   │       │   └── notice-view-record-list.vue
│   │       ├── notice-detail.vue
│   │       ├── notice-employee-detail.vue
│   │       ├── notice-employee-list.vue
│   │       └── notice-list.vue
│   └── order/                                 # 订单管理
│       └── segment-order-list.vue             # 分段订单列表
├── support/                                   # 支撑功能模块
│   ├── api-encrypt/
│   │   └── api-encrypt-index.vue
│   ├── cache/
│   │   └── cache-list.vue
│   ├── change-log/
│   │   ├── change-log-form.vue
│   │   ├── change-log-list.vue
│   │   └── change-log-modal.vue
│   ├── code-generator/
│   │   ├── components/
│   │   │   ├── form/
│   │   │   │   ├── code-generator-table-config-form-basic.vue
│   │   │   │   ├── code-generator-table-config-form-delete.vue
│   │   │   │   ├── code-generator-table-config-form-field.vue
│   │   │   │   ├── code-generator-table-config-form-insert-and-update.vue
│   │   │   │   ├── code-generator-table-config-form-query-field.vue
│   │   │   │   ├── code-generator-table-config-form-table-field.vue
│   │   │   │   └── code-generator-table-config-form.vue
│   │   │   └── preview/
│   │   │       └── code-generator-preview-modal.vue
│   │   └── code-generator-list.vue
│   ├── config/
│   │   ├── config-form-modal.vue
│   │   └── config-list.vue
│   ├── dict/
│   │   ├── components/
│   │   │   ├── dict-data-form-modal.vue
│   │   │   ├── dict-data-modal.vue
│   │   │   └── dict-form-modal.vue
│   │   └── index.vue
│   ├── feedback/
│   │   └── feedback-list.vue
│   ├── file/
│   │   └── file-list.vue
│   ├── heart-beat/
│   │   └── heart-beat-list.vue
│   ├── help-doc/
│   │   ├── management/
│   │   │   ├── components/
│   │   │   │   ├── help-doc-catalog-form-modal.vue
│   │   │   │   ├── help-doc-catalog-tree-select.vue
│   │   │   │   ├── help-doc-catalog-tree.vue
│   │   │   │   ├── help-doc-form-drawer.vue
│   │   │   │   └── help-doc-list.vue
│   │   │   └── help-doc-manage-list.vue
│   │   └── user-view/
│   │       ├── components/
│   │       │   └── help-doc-view-record-list.vue
│   │       └── help-doc-user-view.vue
│   ├── job/
│   │   ├── components/
│   │   │   ├── deleted-job-list.vue
│   │   │   ├── job-form-modal.vue
│   │   │   └── job-log-list-modal.vue
│   │   └── job-list.vue
│   ├── level3protect/
│   │   ├── data-masking-list.vue
│   │   └── level3-protect-config-index.vue
│   ├── login-fail/
│   │   └── login-fail-list.vue
│   ├── login-log/
│   │   └── login-log-list.vue
│   ├── message/
│   │   ├── components/
│   │   │   ├── message-receiver-modal.vue
│   │   │   └── message-send-form.vue
│   │   └── message-list.vue
│   ├── operate-log/
│   │   ├── operate-log-detail-modal.vue
│   │   └── operate-log-list.vue
│   ├── reload/
│   │   ├── do-reload-form-modal.vue
│   │   ├── reload-list.vue
│   │   └── reload-result-list.vue
│   └── serial-number/
│       ├── serial-number-generate-form-modal.vue
│       ├── serial-number-list.vue
│       └── serial-number-record-list.vue
└── system/                                    # 系统核心模块
    ├── 40X/
    │   ├── 403.vue
    │   └── 404.vue
    ├── account/
    │   ├── components/
    │   │   ├── center/
    │   │   │   └── index.vue
    │   │   ├── login-log/
    │   │   │   └── index.vue
    │   │   ├── message/
    │   │   │   ├── components/
    │   │   │   │   └── message-detail.vue
    │   │   │   └── index.vue
    │   │   ├── notice/
    │   │   │   └── index.vue
    │   │   ├── operate-log/
    │   │   │   └── index.vue
    │   │   └── password/
    │   │       └── index.vue
    │   └── index.vue
    ├── department/
    │   ├── components/
    │   │   └── department-form-modal.vue
    │   └── department-list.vue
    ├── employee/
    │   ├── components/
    │   │   ├── department-children/
    │   │   │   └── index.vue
    │   │   ├── department-tree/
    │   │   │   └── index.vue
    │   │   ├── employee-department-form-modal/
    │   │   │   └── index.vue
    │   │   ├── employee-form-modal/
    │   │   │   └── index.vue
    │   │   ├── employee-list/
    │   │   │   └── index.vue
    │   │   └── employee-password-dialog/
    │   │       └── index.vue
    │   └── index.vue
    ├── home/
    │   ├── components/
    │   │   ├── echarts/
    │   │   │   ├── category.vue
    │   │   │   ├── gauge.vue
    │   │   │   ├── gradient.vue
    │   │   │   └── pie.vue
    │   │   ├── quick-entry/
    │   │   │   ├── home-quick-entry-modal.vue
    │   │   │   └── home-quick-entry.vue
    │   │   ├── to-be-done-card/
    │   │   │   ├── home-to-be-done.vue
    │   │   │   └── to-be-done-modal.vue
    │   │   ├── changelog-card.vue
    │   │   ├── default-home-card.vue
    │   │   └── official-account-card.vue
    │   ├── ad-modal.vue
    │   ├── home-header.vue
    │   ├── home-notice.vue
    │   └── index.vue
    ├── login/
    │   └── login.vue
    ├── login2/
    │   └── login.vue
    ├── login3/
    │   └── login.vue
    ├── menu/
    │   ├── components/
    │   │   ├── menu-operate-modal.vue
    │   │   └── menu-tree-select.vue
    │   └── menu-list.vue
    ├── position/
    │   ├── position-form.vue
    │   └── position-list.vue
    └── role/
        ├── components/
        │   ├── role-data-scope/
        │   │   └── index.vue
        │   ├── role-employee-list/
        │   │   └── index.vue
        │   ├── role-form-modal/
        │   │   └── index.vue
        │   ├── role-list/
        │   │   └── index.vue
        │   ├── role-setting/
        │   │   └── index.vue
        │   └── role-tree/
        │       ├── index.vue
        │       ├── role-tree-checkbox.vue
        │       ├── role-tree-menu.vue
        │       └── role-tree-point.vue
        └── index.vue
```

**总计：126 个 Vue 文件** 