# SmartAdmin 前端开发规范文档

## 概述

本文档基于 SmartAdmin 项目现有的最佳实践，总结了前端开发的标准规范。所有新功能开发都应严格遵循此规范，确保代码的一致性和可维护性。

## 1. 项目结构规范

### 1.1 模块分类

项目按照功能域划分为三大模块：

- **business**: 业务功能模块
- **system**: 系统管理模块  
- **support**: 支撑工具模块

### 1.2 目录结构

每个功能模块需要在以下三个目录中创建对应文件：

```
src/
├── views/           # 页面视图
│   ├── business/    # 业务模块页面
│   ├── system/      # 系统模块页面
│   └── support/     # 支撑模块页面
├── api/             # API接口定义
│   ├── business/    # 业务模块API
│   ├── system/      # 系统模块API
│   └── support/     # 支撑模块API
└── constants/       # 常量定义
    ├── business/    # 业务模块常量
    ├── system/      # 系统模块常量
    └── support/     # 支撑模块常量
```

## 2. Views 视图开发规范

### 2.1 文件命名规范

- **列表页**: `{模块名}-list.vue`
- **详情页**: `{模块名}-detail.vue`
- **表单页**: `{模块名}-form.vue`
- **组件**: 放在 `components/` 子目录下

**示例**:
```
goods/
├── goods-list.vue
├── goods-detail.vue
└── components/
    └── goods-form-modal.vue
```

### 2.2 页面结构规范

#### 2.2.1 列表页面标准结构

```vue
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <!-- 查询条件 -->
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <!-- 操作按钮 -->
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="TABLE_ID" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <!-- 数据表格 -->
    <a-table>
      <template #headerCell="{ column }">
        <SmartHeaderCell v-model:value="queryForm[column.filterOptions?.key]" :column="column" @change="queryData" />
      </template>
      <template #bodyCell="{ text, record, column }">
        <!-- 表格内容渲染 -->
      </template>
    </a-table>

    <!-- 分页组件 -->
    <div class="smart-query-table-page">
      <a-pagination />
    </div>
  </a-card>
</template>
```

#### 2.2.2 详情页面标准结构

详情页面分为三种类型：**完整页面详情**、**弹窗详情**、**抽屉详情**

##### A. 完整页面详情（推荐用于复杂详情）

```vue
<template>
  <!-- 页面头部信息 -->
  <div class="detail-header">
    <a-page-header :title="detail.title" :avatar="{ src: detail.avatar }">
      <template #extra>
        <a-button @click="onEdit" type="primary" v-privilege="'module:update'">编辑</a-button>
        <a-button @click="onDelete" danger v-privilege="'module:delete'">删除</a-button>
      </template>
      <div>
        <a-descriptions size="small" :column="3">
          <a-descriptions-item label="字段1">{{ detail.field1 }}</a-descriptions-item>
          <a-descriptions-item label="字段2">{{ detail.field2 }}</a-descriptions-item>
          <a-descriptions-item label="字段3">{{ detail.field3 }}</a-descriptions-item>
        </a-descriptions>
      </div>
    </a-page-header>
  </div>

  <!-- 详情内容 -->
  <a-card class="smart-margin-top10" size="small" v-if="hasTabsPrivilege">
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="content" tab="详细内容">
        <div class="content-html" v-html="detail.contentHtml"></div>
      </a-tab-pane>
      <a-tab-pane key="attachment" tab="附件信息" v-if="detail.attachment">
        <FilePreview :fileList="detail.attachment" />
      </a-tab-pane>
      <a-tab-pane key="history" tab="操作记录">
        <DataTracer :dataId="detailId" :type="DATA_TRACER_TYPE" />
      </a-tab-pane>
    </a-tabs>
  </a-card>

  <!-- 表单弹窗 -->
  <ModuleFormModal ref="formModalRef" @refresh="getDetail" />
</template>

<script setup>
  import { computed, onMounted, ref } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { moduleApi } from '/@/api/path/to/module-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import DataTracer from '/@/components/support/data-tracer/index.vue';
  import FilePreview from '/@/components/support/file-preview/index.vue';
  import ModuleFormModal from './components/module-form-modal.vue';

  const route = useRoute();
  const router = useRouter();
  
  // 详情ID
  const detailId = ref();
  const activeKey = ref('content');
  
  // 详情数据
  const detail = ref({});

  // 权限计算
  const hasTabsPrivilege = computed(() => {
    return true; // 根据实际权限计算
  });

  // 页面初始化
  onMounted(() => {
    if (route.query.id) {
      detailId.value = Number(route.query.id);
      getDetail();
    }
  });

  // 获取详情
  async function getDetail() {
    try {
      SmartLoading.show();
      const result = await moduleApi.getDetail(detailId.value);
      detail.value = result.data;
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      SmartLoading.hide();
    }
  }

  // 编辑操作
  const formModalRef = ref();
  function onEdit() {
    formModalRef.value.showModal(detailId.value);
  }

  // 删除操作
  function onDelete() {
    Modal.confirm({
      title: '提示',
      content: '确定要删除该记录吗？',
      okText: '删除',
      okType: 'danger',
      onOk: async () => {
        try {
          SmartLoading.show();
          await moduleApi.delete(detailId.value);
          message.success('删除成功');
          router.back(); // 返回列表页
        } catch (error) {
          smartSentry.captureError(error);
        } finally {
          SmartLoading.hide();
        }
      }
    });
  }
</script>

<style lang="less" scoped>
  .detail-header {
    background-color: #fff;
    padding: 10px;
  }
  .content-html {
    img {
      max-width: 100%;
    }
  }
</style>
```

##### B. 弹窗详情（适用于简单详情）

```vue
<template>
  <a-modal 
    :open="visible" 
    title="详情信息" 
    width="60%" 
    :footer="null" 
    @cancel="onClose"
  >
    <div class="info-box">
      <a-descriptions bordered :column="2" size="small">
        <a-descriptions-item label="字段1">{{ detail.field1 }}</a-descriptions-item>
        <a-descriptions-item label="字段2">{{ detail.field2 }}</a-descriptions-item>
        <a-descriptions-item label="字段3" :span="2">{{ detail.field3 }}</a-descriptions-item>
      </a-descriptions>
    </div>
    
    <div class="info-box" v-if="detail.content">
      <h4>详细内容：</h4>
      <div class="content-text">{{ detail.content }}</div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { moduleApi } from '/@/api/path/to/module-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  const visible = ref(false);
  const detail = reactive({});

  // 显示详情
  async function show(id) {
    visible.value = true;
    await getDetail(id);
  }

  // 关闭详情
  function onClose() {
    visible.value = false;
    Object.keys(detail).forEach(key => {
      detail[key] = '';
    });
  }

  // 获取详情数据
  async function getDetail(id) {
    try {
      SmartLoading.show();
      const result = await moduleApi.getDetail(id);
      Object.assign(detail, result.data);
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({ show });
</script>

<style scoped lang="less">
  .info-box {
    border-bottom: 1px solid #f0f0f0;
    padding: 10px 8px;
    
    &:last-child {
      border-bottom: none;
    }
  }
  .content-text {
    white-space: pre-wrap;
    word-break: break-all;
  }
</style>
```

##### C. 抽屉详情（适用于中等复杂度详情）

```vue
<template>
  <a-drawer
    v-model:open="visible"
    :width="800"
    title="详情信息"
    placement="right"
    :destroyOnClose="true"
  >
    <a-descriptions bordered :column="2" size="small">
      <a-descriptions-item label="字段1">{{ detail.field1 }}</a-descriptions-item>
      <a-descriptions-item label="字段2">{{ detail.field2 }}</a-descriptions-item>
      <a-descriptions-item label="内容" :span="2">
        <div class="content-html" v-html="detail.contentHtml"></div>
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-space>
        <a-button @click="onClose">关闭</a-button>
        <a-button type="primary" @click="onEdit" v-privilege="'module:update'">编辑</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { moduleApi } from '/@/api/path/to/module-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  const emit = defineEmits(['edit']);
  const visible = ref(false);
  const detail = reactive({});

  async function show(id) {
    visible.value = true;
    await getDetail(id);
  }

  function onClose() {
    visible.value = false;
  }

  function onEdit() {
    emit('edit', detail.id);
    onClose();
  }

  async function getDetail(id) {
    try {
      SmartLoading.show();
      const result = await moduleApi.getDetail(id);
      Object.assign(detail, result.data);
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      SmartLoading.hide();
    }
  }

  defineExpose({ show });
</script>
</template>
```

#### 2.2.3 表单页面标准结构

##### A. 表单弹窗/抽屉组件

```vue
<template>
  <a-drawer 
    :title="form.id ? '编辑' : '添加'" 
    :width="600" 
    :open="visible" 
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }">
      <a-form-item label="名称" name="name">
        <a-input v-model:value="form.name" placeholder="请输入名称" />
      </a-form-item>
      
      <a-form-item label="类型" name="type">
        <SmartEnumSelect enum-name="TYPE_ENUM" v-model:value="form.type" />
      </a-form-item>
      
      <a-form-item label="状态" name="status">
        <a-radio-group v-model:value="form.status">
          <a-radio :value="1">启用</a-radio>
          <a-radio :value="0">禁用</a-radio>
        </a-radio-group>
      </a-form-item>
      
      <a-form-item label="描述" name="description">
        <a-textarea v-model:value="form.description" :rows="4" placeholder="请输入描述" />
      </a-form-item>
      
      <a-form-item label="附件">
        <Upload
          :defaultFileList="defaultFileList"
          :maxUploadSize="10"
          :folder="FILE_FOLDER_TYPE"
          buttonText="上传附件"
          @change="onFileChange"
        />
      </a-form-item>
    </a-form>

    <template #footer>
      <a-space>
        <a-button @click="onClose">取消</a-button>
        <a-button type="primary" @click="onSubmit">保存</a-button>
      </a-space>
    </template>
  </a-drawer>
</template>

<script setup>
  import { ref, reactive, nextTick } from 'vue';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { moduleApi } from '/@/api/path/to/module-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import Upload from '/@/components/support/file-upload/index.vue';
  import _ from 'lodash';

  const emit = defineEmits(['reloadList']);

  // 表单组件引用
  const formRef = ref();
  const visible = ref(false);

  // 表单默认值
  const formDefault = {
    id: undefined,
    name: '',
    type: undefined,
    status: 1,
    description: '',
    attachments: [],
  };

  // 表单数据
  const form = reactive({ ...formDefault });

  // 表单验证规则
  const rules = {
    name: [{ required: true, message: '名称不能为空' }],
    type: [{ required: true, message: '请选择类型' }],
    status: [{ required: true, message: '请选择状态' }],
  };

  // 文件列表
  const defaultFileList = ref([]);

  // 显示表单
  function showModal(rowData) {
    Object.assign(form, { ...formDefault });
    defaultFileList.value = [];
    
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
      if (rowData.attachments) {
        defaultFileList.value = rowData.attachments;
      }
    }
    
    visible.value = true;
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  }

  // 关闭表单
  function onClose() {
    visible.value = false;
  }

  // 文件变化
  function onFileChange(fileList) {
    form.attachments = fileList;
  }

  // 提交表单
  function onSubmit() {
    formRef.value
      .validate()
      .then(async () => {
        try {
          SmartLoading.show();
          
          if (form.id) {
            await moduleApi.update(form);
            message.success('修改成功');
          } else {
            await moduleApi.add(form);
            message.success('添加成功');
          }
          
          onClose();
          emit('reloadList');
        } catch (error) {
          smartSentry.captureError(error);
        } finally {
          SmartLoading.hide();
        }
      })
      .catch(() => {
        message.error('请检查表单数据');
      });
  }

  defineExpose({ showModal });
</script>
</template>
```

#### 2.2.4 必需的功能特性

**列表页必需功能**:
- ✅ 查询表单
- ✅ 表格展示
- ✅ 分页组件
- ✅ 列拖拽调整
- ✅ 表头过滤器
- ✅ 权限控制
- ✅ 错误处理

**详情页必需功能**:
- ✅ 数据展示（描述列表/卡片布局）
- ✅ 操作按钮（编辑、删除等）
- ✅ 标签页切换（适用于复杂详情）
- ✅ 附件预览
- ✅ 操作记录
- ✅ 权限控制
- ✅ 加载状态
- ✅ 错误处理

**表单页必需功能**:
- ✅ 表单验证
- ✅ 数据提交
- ✅ 文件上传
- ✅ 状态管理
- ✅ 成功提示
- ✅ 错误处理

**表格配置规范**:
```javascript
const columns = ref([
  {
    title: '列标题',
    dataIndex: 'fieldName',
    resizable: true,           // 支持列宽调整
    width: 150,               // 固定宽度
    sorter: true,             // 排序(可选)
    filterOptions: {          // 过滤器配置(可选)
      type: 'input',          // input/enum-select/dict-select
      key: 'searchKey',
    },
  },
]);
```

#### 2.2.5 Script 部分规范

```javascript
<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { moduleApi } from '/@/api/path/to/module-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import SmartHeaderCell from '/@/components/support/table-header-cell/index.vue';
  import _ from 'lodash';

  // 表格列定义
  const columns = ref([...]);

  // 查询表单
  const queryFormState = { ... };
  const queryForm = reactive(_.cloneDeep(queryFormState));

  // 表格状态
  const tableLoading = ref(false);
  const tableData = ref([]);
  const total = ref(0);
  const selectedRowKeyList = ref([]);

  // 查询方法
  async function queryData() {
    tableLoading.value = true;
    try {
      let queryResult = await moduleApi.queryList(queryForm);
      tableData.value = queryResult.data.list;
      total.value = queryResult.data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  // 页面初始化
  onMounted(queryData);
</script>
```

#### 2.2.6 组件间通信规范

##### A. 父子组件通信

```javascript
// 父组件
<template>
  <ChildComponent 
    :data="parentData" 
    @update="handleUpdate"
    @delete="handleDelete"
  />
</template>

<script setup>
  const parentData = ref({});
  
  function handleUpdate(data) {
    // 处理子组件更新事件
    console.log('子组件更新:', data);
  }
  
  function handleDelete(id) {
    // 处理子组件删除事件
    console.log('子组件删除:', id);
  }
</script>

// 子组件
<script setup>
  const props = defineProps({
    data: {
      type: Object,
      default: () => ({})
    }
  });
  
  const emit = defineEmits(['update', 'delete']);
  
  function onUpdate() {
    emit('update', { id: 1, name: 'updated' });
  }
  
  function onDelete() {
    emit('delete', props.data.id);
  }
</script>
```

##### B. 组件引用和方法调用

```javascript
// 父组件调用子组件方法
<template>
  <FormModal ref="formModalRef" @reload="queryData" />
  <DetailModal ref="detailModalRef" />
</template>

<script setup>
  const formModalRef = ref();
  const detailModalRef = ref();
  
  function showAddForm() {
    formModalRef.value.showModal();
  }
  
  function showEditForm(record) {
    formModalRef.value.showModal(record);
  }
  
  function showDetail(record) {
    detailModalRef.value.show(record.id);
  }
</script>
```

#### 2.2.7 状态管理最佳实践

##### A. 组件内状态管理

```javascript
<script setup>
  // 基础数据状态
  const loading = ref(false);
  const data = ref([]);
  const total = ref(0);
  
  // 表单状态
  const formVisible = ref(false);
  const formData = reactive({
    name: '',
    type: undefined,
    status: 1
  });
  
  // 计算属性
  const filteredData = computed(() => {
    return data.value.filter(item => item.status === 1);
  });
  
  // 监听器
  watch(
    () => formData.type,
    (newType) => {
      console.log('类型变化:', newType);
      // 根据类型变化执行相应逻辑
    }
  );
</script>
```

##### B. 跨组件状态管理（使用 Pinia）

```javascript
// stores/modules/business/goods-store.js
import { defineStore } from 'pinia';
import { goodsApi } from '/@/api/business/goods/goods-api';

export const useGoodsStore = defineStore('goods', {
  state: () => ({
    goodsList: [],
    currentGoods: null,
    loading: false
  }),
  
  getters: {
    enabledGoods: (state) => {
      return state.goodsList.filter(item => item.status === 1);
    }
  },
  
  actions: {
    async fetchGoodsList(params) {
      this.loading = true;
      try {
        const result = await goodsApi.queryList(params);
        this.goodsList = result.data.list;
        return result;
      } finally {
        this.loading = false;
      }
    },
    
    async fetchGoodsDetail(id) {
      const result = await goodsApi.getDetail(id);
      this.currentGoods = result.data;
      return result;
    }
  }
});

// 在组件中使用
<script setup>
  import { useGoodsStore } from '/@/store/modules/business/goods-store';
  
  const goodsStore = useGoodsStore();
  
  onMounted(() => {
    goodsStore.fetchGoodsList({ pageNum: 1, pageSize: 10 });
  });
</script>
```

#### 2.2.8 生命周期使用规范

```javascript
<script setup>
  import { onMounted, onBeforeUnmount, onActivated, onDeactivated } from 'vue';
  
  // 组件挂载时
  onMounted(() => {
    console.log('组件已挂载');
    initializeData();
    setupEventListeners();
  });
  
  // 组件卸载前
  onBeforeUnmount(() => {
    console.log('组件即将卸载');
    cleanup();
    removeEventListeners();
  });
  
  // keep-alive 组件激活时
  onActivated(() => {
    console.log('组件激活');
    refreshData();
  });
  
  // keep-alive 组件失活时
  onDeactivated(() => {
    console.log('组件失活');
    saveCurrentState();
  });
  
  function initializeData() {
    // 初始化数据
  }
  
  function setupEventListeners() {
    // 设置事件监听器
    window.addEventListener('resize', handleResize);
  }
  
  function removeEventListeners() {
    // 移除事件监听器
    window.removeEventListener('resize', handleResize);
  }
  
  function cleanup() {
    // 清理工作
  }
  
  const handleResize = _.throttle(() => {
    // 处理窗口大小变化
  }, 300);
</script>
```

#### 2.2.9 表格动态高度处理

```javascript
<script setup>
  import _ from 'lodash';
  
  // 动态设置表格高度
  const yHeight = ref(0);
  
  onMounted(() => {
    resetGetHeight();
    // 监听窗口大小变化
    window.addEventListener('resize', throttledResetHeight);
  });
  
  onBeforeUnmount(() => {
    window.removeEventListener('resize', throttledResetHeight);
  });
  
  function resetGetHeight() {
    // 搜索部分高度
    const searchEl = document.querySelector('.smart-query-form');
    // 按钮部分高度
    const btnEl = document.querySelector('.smart-table-btn-block');
    // 表格头高度
    const tableHeaderEl = document.querySelector('.ant-table-thead');
    // 分页高度
    const paginationEl = document.querySelector('.smart-query-table-page');
    // 内容区总高度
    const containerEl = document.querySelector('.admin-content');
    
    setTimeout(() => {
      const usedHeight = 
        (searchEl?.offsetHeight || 0) + 
        (btnEl?.offsetHeight || 0) + 
        (tableHeaderEl?.offsetHeight || 0) + 
        (paginationEl?.offsetHeight || 0) + 
        100; // 额外间距
        
      yHeight.value = (containerEl?.offsetHeight || 600) - usedHeight;
    }, 100);
  }
  
  const throttledResetHeight = _.throttle(resetGetHeight, 300);
</script>
```

## 3. API 接口开发规范

### 3.1 文件命名规范

API文件命名：`{模块名}-api.js`

### 3.2 API文件结构

```javascript
/*
 * 模块描述
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      YYYY-MM-DD HH:mm:ss
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { postRequest, getRequest, getDownload, postDownload } from '/@/lib/axios';

export const moduleApi = {
  /**
   * 查询列表 <AUTHOR>
   */
  queryList: (params) => {
    return postRequest('/admin-api/module/query', params);
  },

  /**
   * 获取详情 <AUTHOR>
   */
  getDetail: (id) => {
    return getRequest(`/admin-api/module/detail/${id}`);
  },

  /**
   * 添加数据 <AUTHOR>
   */
  add: (params) => {
    return postRequest('/admin-api/module/add', params);
  },

  /**
   * 更新数据 <AUTHOR>
   */
  update: (params) => {
    return postRequest('/admin-api/module/update', params);
  },

  /**
   * 删除数据 <AUTHOR>
   */
  delete: (id) => {
    return postRequest(`/admin-api/module/delete/${id}`);
  },

  /**
   * 批量删除 <AUTHOR>
   */
  batchDelete: (idList) => {
    return postRequest('/admin-api/module/batchDelete', idList);
  },

  /**
   * 导出数据 <AUTHOR>
   */
  export: (params) => {
    return postDownload('/admin-api/module/export', params);
  },
};
```

### 3.3 API规范要求

1. **方法命名**: 使用语义化命名，如 `queryList`、`getDetail`、`add`、`update`、`delete`
2. **注释规范**: 每个方法必须包含描述和作者信息
3. **请求方式**: 
   - `GET`: 查询单条数据、删除操作
   - `POST`: 查询列表、添加、更新、批量操作
4. **下载文件**: 使用 `getDownload` 或 `postDownload`

### 3.4 API 调用最佳实践

#### A. 错误处理和重试机制

```javascript
export const moduleApi = {
  /**
   * 带重试的查询方法 <AUTHOR>
   */
  async queryListWithRetry(params, retryCount = 2) {
    let lastError;
    for (let i = 0; i <= retryCount; i++) {
      try {
        return await postRequest('/admin-api/module/query', params);
      } catch (error) {
        lastError = error;
        if (i < retryCount) {
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 1000 * (i + 1)));
        }
      }
    }
    throw lastError;
  },

  /**
   * 带超时控制的查询方法 <AUTHOR>
   */
  queryWithTimeout(params, timeout = 30000) {
    return Promise.race([
      postRequest('/admin-api/module/query', params),
      new Promise((_, reject) => 
        setTimeout(() => reject(new Error('请求超时')), timeout)
      )
    ]);
  }
};
```

#### B. 接口数据预处理

```javascript
export const moduleApi = {
  /**
   * 查询列表（带数据预处理） <AUTHOR>
   */
  async queryList(params) {
    // 参数预处理
    const processedParams = {
      ...params,
      // 时间格式化
      startDate: params.startDate ? dayjs(params.startDate).format('YYYY-MM-DD') : undefined,
      endDate: params.endDate ? dayjs(params.endDate).format('YYYY-MM-DD') : undefined,
      // 清除空值
      ..._.pickBy(params, (value) => value !== undefined && value !== '' && value !== null)
    };

    const result = await postRequest('/admin-api/module/query', processedParams);
    
    // 响应数据后处理
    if (result.data && result.data.list) {
      result.data.list = result.data.list.map(item => ({
        ...item,
        // 添加显示标签
        statusText: item.status === 1 ? '启用' : '禁用',
        // 格式化时间
        createTimeFormatted: dayjs(item.createTime).format('YYYY-MM-DD HH:mm:ss'),
        // 添加操作标识
        _canEdit: item.status === 1,
        _canDelete: item.status === 0
      }));
    }

    return result;
  }
};
```

#### C. 接口缓存策略

```javascript
// utils/api-cache.js
class ApiCache {
  constructor() {
    this.cache = new Map();
    this.timeouts = new Map();
  }

  set(key, data, ttl = 300000) { // 默认5分钟
    this.cache.set(key, data);
    
    // 清除之前的超时
    if (this.timeouts.has(key)) {
      clearTimeout(this.timeouts.get(key));
    }
    
    // 设置新的超时
    const timeout = setTimeout(() => {
      this.cache.delete(key);
      this.timeouts.delete(key);
    }, ttl);
    
    this.timeouts.set(key, timeout);
  }

  get(key) {
    return this.cache.get(key);
  }

  has(key) {
    return this.cache.has(key);
  }

  clear() {
    this.cache.clear();
    this.timeouts.forEach(timeout => clearTimeout(timeout));
    this.timeouts.clear();
  }
}

const apiCache = new ApiCache();

// 在 API 中使用缓存
export const moduleApi = {
  async queryList(params) {
    const cacheKey = `moduleList_${JSON.stringify(params)}`;
    
    if (apiCache.has(cacheKey)) {
      return apiCache.get(cacheKey);
    }
    
    const result = await postRequest('/admin-api/module/query', params);
    apiCache.set(cacheKey, result, 300000); // 缓存5分钟
    
    return result;
  }
};
```

## 4. 数据处理和验证规范

### 4.1 表单数据验证

#### A. 基础验证规则

```javascript
// utils/validation-rules.js
export const validationRules = {
  // 必填验证
  required: (message = '此项为必填项') => ({
    required: true,
    message
  }),

  // 长度验证
  length: (min, max, message) => ({
    min,
    max,
    message: message || `长度应在 ${min} 到 ${max} 个字符之间`
  }),

  // 邮箱验证
  email: (message = '请输入正确的邮箱格式') => ({
    pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
    message
  }),

  // 手机号验证
  phone: (message = '请输入正确的手机号') => ({
    pattern: /^1[3-9]\d{9}$/,
    message
  }),

  // 身份证验证
  idCard: (message = '请输入正确的身份证号') => ({
    pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,
    message
  }),

  // 密码强度验证
  strongPassword: (message = '密码必须包含大小写字母、数字和特殊字符，长度8-20位') => ({
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,20}$/,
    message
  }),

  // 自定义验证器
  custom: (validator, message) => ({
    validator: (rule, value) => {
      if (validator(value)) {
        return Promise.resolve();
      } else {
        return Promise.reject(new Error(message));
      }
    }
  })
};

// 在组件中使用
const rules = {
  name: [validationRules.required('名称不能为空'), validationRules.length(2, 50)],
  email: [validationRules.required(), validationRules.email()],
  phone: [validationRules.required(), validationRules.phone()],
  password: [validationRules.required(), validationRules.strongPassword()],
  age: [
    validationRules.required(),
    validationRules.custom(
      (value) => value >= 18 && value <= 100,
      '年龄必须在18-100之间'
    )
  ]
};
```

#### B. 异步验证

```javascript
const rules = {
  username: [
    validationRules.required(),
    {
      validator: async (rule, value) => {
        if (!value) return Promise.resolve();
        
        try {
          const result = await userApi.checkUsername(value);
          if (result.data.exists) {
            return Promise.reject(new Error('用户名已存在'));
          }
          return Promise.resolve();
        } catch (error) {
          return Promise.reject(new Error('验证失败，请重试'));
        }
      },
      trigger: 'blur'
    }
  ]
};
```

### 4.2 数据格式化和转换

```javascript
// utils/data-formatter.js
export const dataFormatter = {
  // 金额格式化
  formatMoney(amount, decimals = 2) {
    if (!amount || isNaN(amount)) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  },

  // 文件大小格式化
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 时间格式化
  formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!time) return '-';
    return dayjs(time).format(format);
  },

  // 相对时间
  formatRelativeTime(time) {
    if (!time) return '-';
    return dayjs(time).fromNow();
  },

  // 脱敏处理
  maskPhone(phone) {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  },

  maskEmail(email) {
    if (!email) return '';
    const [name, domain] = email.split('@');
    if (name.length <= 2) return email;
    return name.charAt(0) + '***' + name.slice(-1) + '@' + domain;
  },

  maskIdCard(idCard) {
    if (!idCard) return '';
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
  },

  // 高亮关键词
  highlightKeywords(text, keywords) {
    if (!keywords || !text) return text;
    const regex = new RegExp(`(${keywords})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  }
};
```

### 4.3 数据过滤和搜索

```javascript
// utils/data-filter.js
export class DataFilter {
  constructor(data) {
    this.originalData = data;
    this.filteredData = [...data];
  }

  // 文本搜索
  search(keyword, fields = []) {
    if (!keyword) {
      this.filteredData = [...this.originalData];
      return this;
    }

    this.filteredData = this.originalData.filter(item => {
      return fields.some(field => {
        const value = _.get(item, field, '');
        return String(value).toLowerCase().includes(keyword.toLowerCase());
      });
    });

    return this;
  }

  // 条件过滤
  filter(condition) {
    this.filteredData = this.filteredData.filter(condition);
    return this;
  }

  // 排序
  sort(field, order = 'asc') {
    this.filteredData.sort((a, b) => {
      const aVal = _.get(a, field);
      const bVal = _.get(b, field);
      
      if (order === 'desc') {
        return bVal > aVal ? 1 : -1;
      } else {
        return aVal > bVal ? 1 : -1;
      }
    });

    return this;
  }

  // 分页
  paginate(pageNum, pageSize) {
    const start = (pageNum - 1) * pageSize;
    const end = start + pageSize;
    this.filteredData = this.filteredData.slice(start, end);
    return this;
  }

  // 获取结果
  result() {
    return this.filteredData;
  }

  // 重置
  reset() {
    this.filteredData = [...this.originalData];
    return this;
  }
}

// 使用示例
const filter = new DataFilter(tableData.value);
const result = filter
  .search(searchKeyword.value, ['name', 'description'])
  .filter(item => item.status === selectedStatus.value)
  .sort('createTime', 'desc')
  .result();
```

## 5. 性能优化规范

### 5.1 组件性能优化

#### A. 虚拟滚动

```javascript
// 大数据量表格优化
<template>
  <a-table
    :data-source="tableData"
    :columns="columns"
    :scroll="{ y: yHeight }"
    :pagination="false"
    :virtual="true"
    :rowHeight="40"
  />
</template>

<script setup>
  // 启用虚拟滚动的配置
  const yHeight = ref(400);
  const tableData = ref([]);

  // 分批加载数据
  const loadMoreData = async () => {
    const newData = await moduleApi.queryList({
      pageNum: currentPage.value,
      pageSize: 100
    });
    
    tableData.value.push(...newData.data.list);
    currentPage.value++;
  };
</script>
```

#### B. 图片懒加载

```javascript
// utils/lazy-load.js
export const LazyLoad = {
  install(app) {
    const observerOptions = {
      rootMargin: '50px',
      threshold: 0.1
    };

    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          const src = img.dataset.src;
          
          if (src) {
            img.src = src;
            img.onload = () => {
              img.classList.add('loaded');
            };
            observer.unobserve(img);
          }
        }
      });
    }, observerOptions);

    app.directive('lazy', {
      mounted(el, binding) {
        el.dataset.src = binding.value;
        el.src = '/src/assets/images/placeholder.png'; // 占位图
        observer.observe(el);
      }
    });
  }
};

// 使用
<img v-lazy="imageUrl" alt="图片" class="lazy-image" />
```

#### C. 计算属性缓存

```javascript
<script setup>
  // 复杂计算使用计算属性
  const expensiveComputation = computed(() => {
    console.log('计算执行'); // 只在依赖变化时执行
    return tableData.value
      .filter(item => item.status === 1)
      .map(item => ({
        ...item,
        computedField: performComplexCalculation(item)
      }))
      .sort((a, b) => b.score - a.score);
  });

  // 使用 useMemo 进行更细粒度的缓存
  const memoizedData = useMemo(() => {
    return heavyDataProcessing(rawData.value);
  }, [rawData]);
</script>
```

### 5.2 网络请求优化

#### A. 请求防抖和节流

```javascript
<script setup>
  import { debounce, throttle } from 'lodash';

  // 搜索防抖
  const debouncedSearch = debounce((keyword) => {
    performSearch(keyword);
  }, 300);

  // 滚动节流
  const throttledScroll = throttle((event) => {
    handleScroll(event);
  }, 100);

  // 自动保存防抖
  const debouncedSave = debounce(async (formData) => {
    try {
      await moduleApi.autoSave(formData);
      message.success('自动保存成功');
    } catch (error) {
      console.error('自动保存失败:', error);
    }
  }, 2000);

  watch(formData, debouncedSave, { deep: true });
</script>
```

#### B. 并发请求优化

```javascript
<script setup>
  // 并行请求
  async function loadPageData() {
    try {
      SmartLoading.show();
      
      const [listResult, optionsResult, configResult] = await Promise.all([
        moduleApi.queryList(queryForm),
        moduleApi.getOptions(),
        moduleApi.getConfig()
      ]);

      tableData.value = listResult.data.list;
      optionsList.value = optionsResult.data;
      configData.value = configResult.data;
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      SmartLoading.hide();
    }
  }

  // 请求队列管理
  class RequestQueue {
    constructor(concurrency = 3) {
      this.concurrency = concurrency;
      this.running = [];
      this.queue = [];
    }

    async add(request) {
      return new Promise((resolve, reject) => {
        this.queue.push({ request, resolve, reject });
        this.process();
      });
    }

    async process() {
      if (this.running.length >= this.concurrency || this.queue.length === 0) {
        return;
      }

      const { request, resolve, reject } = this.queue.shift();
      const promise = request().then(resolve).catch(reject);
      this.running.push(promise);

      promise.finally(() => {
        this.running = this.running.filter(p => p !== promise);
        this.process();
      });
    }
  }

  const requestQueue = new RequestQueue(3);
</script>
```

### 5.3 内存管理优化

```javascript
<script setup>
  import { onBeforeUnmount } from 'vue';

  // 清理定时器
  const timers = [];
  function createTimer(callback, delay) {
    const timer = setTimeout(callback, delay);
    timers.push(timer);
    return timer;
  }

  onBeforeUnmount(() => {
    timers.forEach(timer => clearTimeout(timer));
  });

  // 清理事件监听器
  const eventListeners = [];
  function addEventListerner(element, event, handler) {
    element.addEventListener(event, handler);
    eventListeners.push({ element, event, handler });
  }

  onBeforeUnmount(() => {
    eventListeners.forEach(({ element, event, handler }) => {
      element.removeEventListener(event, handler);
    });
  });

  // 大对象引用清理
  onBeforeUnmount(() => {
    // 清理大对象
    largeDataSet.value = null;
    chartInstance.value?.dispose();
  });
</script>
```

## 6. Constants 常量开发规范

### 6.1 文件命名规范

常量文件命名：`{模块名}-const.js`

### 6.2 常量文件结构

```javascript
/*
 * 模块描述
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      YYYY-MM-DD HH:mm:ss
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

// 状态枚举
export const STATUS_ENUM = {
  ENABLED: {
    value: 1,
    desc: '启用',
    color: 'success',
    icon: 'check-circle'
  },
  DISABLED: {
    value: 0,
    desc: '禁用',
    color: 'error',
    icon: 'close-circle'
  },
};

// 类型枚举
export const TYPE_ENUM = {
  TYPE_A: {
    value: 1,
    desc: '类型A',
    color: 'blue',
    category: 'primary'
  },
  TYPE_B: {
    value: 2,
    desc: '类型B',
    color: 'green',
    category: 'secondary'
  },
};

// 业务常量
export const BUSINESS_CONFIG = {
  MAX_FILE_SIZE: 10 * 1024 * 1024, // 10MB
  ALLOWED_FILE_TYPES: ['.jpg', '.png', '.pdf', '.doc', '.docx'],
  DEFAULT_PAGE_SIZE: 10,
  MAX_PAGE_SIZE: 100,
};

// 权限常量
export const PERMISSION_CODES = {
  QUERY: 'module:query',
  ADD: 'module:add',
  UPDATE: 'module:update',
  DELETE: 'module:delete',
  EXPORT: 'module:export',
  IMPORT: 'module:import',
};

export default {
  STATUS_ENUM,
  TYPE_ENUM,
  BUSINESS_CONFIG,
  PERMISSION_CODES,
};
```

### 6.3 枚举工具函数

```javascript
// utils/enum-helper.js
export class EnumHelper {
  static getOptions(enumObj) {
    return Object.values(enumObj).map(item => ({
      label: item.desc,
      value: item.value
    }));
  }

  static getDescByValue(enumObj, value) {
    const item = Object.values(enumObj).find(item => item.value === value);
    return item ? item.desc : '';
  }

  static getColorByValue(enumObj, value) {
    const item = Object.values(enumObj).find(item => item.value === value);
    return item ? item.color : 'default';
  }

  static getIconByValue(enumObj, value) {
    const item = Object.values(enumObj).find(item => item.value === value);
    return item ? item.icon : '';
  }

  static getValuesByCategory(enumObj, category) {
    return Object.values(enumObj)
      .filter(item => item.category === category)
      .map(item => item.value);
  }
}

// 在组件中使用
import { EnumHelper } from '/@/utils/enum-helper';
import { STATUS_ENUM } from '/@/constants/business/module-const';

const statusOptions = EnumHelper.getOptions(STATUS_ENUM);
const statusDesc = EnumHelper.getDescByValue(STATUS_ENUM, 1);
```

### 6.4 注册到全局常量

在 `src/constants/index.js` 中导入：

```javascript
import moduleName from './path/to/module-const';

export default {
  // 其他常量...
  ...moduleName,
};
```

## 7. 安全性规范

### 7.1 输入验证和过滤

```javascript
// utils/security-helper.js
export const SecurityHelper = {
  // XSS 防护
  escapeHtml(text) {
    const map = {
      '&': '&amp;',
      '<': '&lt;',
      '>': '&gt;',
      '"': '&quot;',
      "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, m => map[m]);
  },

  // SQL 注入防护（前端预防）
  sanitizeSqlInput(input) {
    const sqlKeywords = [
      'SELECT', 'INSERT', 'UPDATE', 'DELETE', 'DROP', 'CREATE',
      'ALTER', 'EXEC', 'UNION', 'SCRIPT', 'OR', 'AND'
    ];
    
    const upperInput = input.toUpperCase();
    for (const keyword of sqlKeywords) {
      if (upperInput.includes(keyword)) {
        throw new Error('输入包含非法字符');
      }
    }
    return input;
  },

  // 文件类型验证
  validateFileType(file, allowedTypes) {
    const fileExtension = '.' + file.name.split('.').pop().toLowerCase();
    if (!allowedTypes.includes(fileExtension)) {
      throw new Error(`不支持的文件类型: ${fileExtension}`);
    }
    return true;
  },

  // 文件大小验证
  validateFileSize(file, maxSize) {
    if (file.size > maxSize) {
      throw new Error(`文件大小超过限制: ${this.formatFileSize(maxSize)}`);
    }
    return true;
  }
};
```

### 7.2 敏感数据处理

```javascript
// utils/data-masking.js
export const DataMasking = {
  // 手机号脱敏
  maskPhone(phone) {
    if (!phone || phone.length !== 11) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  },

  // 身份证脱敏
  maskIdCard(idCard) {
    if (!idCard) return '';
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
  },

  // 银行卡脱敏
  maskBankCard(cardNumber) {
    if (!cardNumber) return '';
    return cardNumber.replace(/(\d{4})\d+(\d{4})/, '$1 **** **** $2');
  },

  // 邮箱脱敏
  maskEmail(email) {
    if (!email) return '';
    const [username, domain] = email.split('@');
    if (username.length <= 2) return email;
    return username.charAt(0) + '***' + username.slice(-1) + '@' + domain;
  }
};
```

### 7.3 权限验证

```javascript
// utils/permission-helper.js
export class PermissionHelper {
  static hasPermission(permission) {
    const userPermissions = useUserStore().permissions;
    return userPermissions.includes(permission);
  }

  static hasAnyPermission(permissions) {
    return permissions.some(permission => this.hasPermission(permission));
  }

  static hasAllPermissions(permissions) {
    return permissions.every(permission => this.hasPermission(permission));
  }

  static checkOperationPermission(operation, module) {
    const permissionCode = `${module}:${operation}`;
    if (!this.hasPermission(permissionCode)) {
      throw new Error(`您没有权限执行此操作: ${operation}`);
    }
    return true;
  }
}

// 在组件中使用权限检查
<script setup>
  import { PermissionHelper } from '/@/utils/permission-helper';

  function handleEdit(record) {
    try {
      PermissionHelper.checkOperationPermission('update', 'goods');
      // 执行编辑操作
    } catch (error) {
      message.error(error.message);
    }
  }
</script>
```

## 8. 测试和调试规范

### 8.1 组件测试

```javascript
// tests/components/GoodsList.test.js
import { mount } from '@vue/test-utils';
import { describe, it, expect, vi } from 'vitest';
import GoodsList from '/@/views/business/goods/goods-list.vue';

describe('GoodsList', () => {
  it('renders correctly', () => {
    const wrapper = mount(GoodsList);
    expect(wrapper.find('.smart-query-form').exists()).toBe(true);
    expect(wrapper.find('.ant-table').exists()).toBe(true);
  });

  it('calls queryData on mount', async () => {
    const mockQueryData = vi.fn();
    const wrapper = mount(GoodsList, {
      global: {
        mocks: {
          queryData: mockQueryData
        }
      }
    });
    
    expect(mockQueryData).toHaveBeenCalled();
  });

  it('filters data correctly', async () => {
    const wrapper = mount(GoodsList);
    const searchInput = wrapper.find('input[placeholder*="搜索"]');
    
    await searchInput.setValue('测试商品');
    await searchInput.trigger('input');
    
    // 验证过滤结果
    expect(wrapper.vm.filteredData).toContain('测试商品');
  });
});
```

### 8.2 API 测试

```javascript
// tests/api/goods-api.test.js
import { describe, it, expect, vi } from 'vitest';
import { goodsApi } from '/@/api/business/goods/goods-api';

// Mock axios
vi.mock('/@/lib/axios', () => ({
  postRequest: vi.fn(),
  getRequest: vi.fn()
}));

describe('goodsApi', () => {
  it('queryList should call postRequest with correct params', async () => {
    const mockResponse = { data: { list: [], total: 0 } };
    const { postRequest } = await import('/@/lib/axios');
    postRequest.mockResolvedValue(mockResponse);

    const params = { pageNum: 1, pageSize: 10 };
    const result = await goodsApi.queryList(params);

    expect(postRequest).toHaveBeenCalledWith('/admin-api/goods/query', params);
    expect(result).toEqual(mockResponse);
  });
});
```

### 8.3 调试工具

```javascript
// utils/debug-helper.js
export class DebugHelper {
  static log(message, data = null) {
    if (import.meta.env.DEV) {
      console.log(`[DEBUG] ${message}`, data);
    }
  }

  static error(message, error = null) {
    if (import.meta.env.DEV) {
      console.error(`[ERROR] ${message}`, error);
    }
  }

  static time(label) {
    if (import.meta.env.DEV) {
      console.time(label);
    }
  }

  static timeEnd(label) {
    if (import.meta.env.DEV) {
      console.timeEnd(label);
    }
  }

  static trace(message) {
    if (import.meta.env.DEV) {
      console.trace(message);
    }
  }

  // 性能监控
  static measurePerformance(fn, label) {
    return async (...args) => {
      this.time(label);
      try {
        const result = await fn(...args);
        return result;
      } finally {
        this.timeEnd(label);
      }
    };
  }
}

// 使用示例
const measuredQueryData = DebugHelper.measurePerformance(queryData, 'QueryData');
```

## 9. 权限控制规范

### 9.1 权限代码命名

权限代码格式：`{模块名}:{操作}`

**示例**:
```
goods:query          # 查询商品
goods:add           # 添加商品
goods:update        # 更新商品
goods:delete        # 删除商品
goods:export        # 导出商品
```

### 9.2 权限使用

```vue
<template>
  <!-- 按钮权限 -->
  <a-button v-privilege="'goods:add'">添加</a-button>
  
  <!-- 区域权限 -->
  <div v-privilege="'goods:query'">
    <!-- 内容 -->
  </div>
</template>
```

### 9.3 复杂权限控制

```javascript
<script setup>
  import { computed } from 'vue';
  import { useUserStore } from '/@/store/modules/system/user';

  const userStore = useUserStore();

  // 权限计算
  const canEdit = computed(() => {
    return userStore.hasPermission('goods:update') && record.status === 1;
  });

  const canBatchDelete = computed(() => {
    return userStore.hasPermission('goods:delete') && selectedRows.value.length > 0;
  });

  // 功能权限检查
  function checkPermissionAndExecute(permission, callback) {
    if (userStore.hasPermission(permission)) {
      callback();
    } else {
      message.warning('您没有权限执行此操作');
    }
  }
</script>
```

## 10. 表格配置规范

### 10.1 表格ID注册

在 `src/constants/support/table-id-const.js` 中添加：

```javascript
// 业务模块表格ID
const businessInitTableId = 30000;
const BUSINESS = {
  MODULE_NAME: businessInitTableId + 1,
};
```

### 10.2 表格列配置

```javascript
const columns = ref([
  {
    title: '列标题',                    // 列标题
    dataIndex: 'fieldName',           // 字段名
    resizable: true,                  // 可调整列宽
    width: 150,                       // 列宽
    sorter: true,                     // 可排序(可选)
    ellipsis: true,                   // 超长省略(可选)
    fixed: 'left',                    // 固定列(可选)
    filterOptions: {                  // 过滤器配置(可选)
      type: 'input',                  // 过滤器类型
      key: 'searchField',             // 搜索字段
      enumName: 'STATUS_ENUM',        // 枚举名(enum-select类型)
      dictCode: 'DICT_CODE',          // 字典编码(dict-select类型)
    },
  },
]);
```

### 10.3 表格高级配置

```javascript
// 表格配置
const tableConfig = ref({
  // 行选择配置
  rowSelection: {
    type: 'checkbox',
    selectedRowKeys: selectedRowKeyList,
    onChange: (selectedRowKeys, selectedRows) => {
      selectedRowKeyList.value = selectedRowKeys;
      selectedRowList.value = selectedRows;
    },
    getCheckboxProps: (record) => ({
      disabled: record.status === 0, // 禁用状态不可选
    }),
  },
  
  // 滚动配置
  scroll: {
    x: 1200,
    y: yHeight,
  },
  
  // 分页配置
  pagination: {
    current: queryForm.pageNum,
    pageSize: queryForm.pageSize,
    total: total,
    showSizeChanger: true,
    showQuickJumper: true,
    showTotal: (total, range) => `第 ${range[0]}-${range[1]} 条，共 ${total} 条`,
    pageSizeOptions: PAGE_SIZE_OPTIONS,
    onChange: (pageNum, pageSize) => {
      queryForm.pageNum = pageNum;
      queryForm.pageSize = pageSize;
      queryData();
    },
  },
});
```

### 10.4 表格过滤器完整配置

**实际框架支持的过滤器类型**:

```javascript
const columns = ref([
  {
    title: '创建时间',
    dataIndex: 'createTime',
    resizable: true,
    width: 150,
    filterOptions: {
      type: 'date-range',             // 日期范围选择器
      key: 'createTimeRange',         // 对应查询表单字段
      ranges: true,                   // 是否显示快捷选择
    },
  },
  {
    title: '商品信息',
    dataIndex: 'goodsInfo',
    resizable: true,
    width: 200,
    filterOptions: {
      type: 'input',                  // 文本输入框
      key: 'goodsName',              // 对应查询表单字段
    },
  },
  {
    title: '状态',
    dataIndex: 'status',
    resizable: true,
    width: 100,
    filterOptions: {
      type: 'enum-select',            // 枚举选择器
      enumName: 'ORDER_STATUS_ENUM',  // 枚举名称
      key: 'orderStatus',            // 对应查询表单字段
    },
  },
  {
    title: '产地',
    dataIndex: 'place',
    resizable: true,
    width: 120,
    filterOptions: {
      type: 'dict-select',            // 字典选择器
      dictCode: 'GOODS_PLACE',       // 字典编码
      key: 'place',                  // 对应查询表单字段
      multiple: true,                // 是否多选
    },
  },
  {
    title: '员工',
    dataIndex: 'employeeName',
    resizable: true,
    width: 120,
    filterOptions: {
      type: 'employee-select',        // 员工选择器
      key: 'employeeId',             // 对应查询表单字段
    },
  },
  {
    title: '企业',
    dataIndex: 'enterpriseName',
    resizable: true,
    width: 150,
    filterOptions: {
      type: 'enterprise-select',      // 企业选择器
      key: 'enterpriseId',           // 对应查询表单字段
    },
  },
  {
    title: '是否启用',
    dataIndex: 'enableFlag',
    resizable: true,
    width: 100,
    filterOptions: {
      type: 'boolean-select',         // 布尔选择器
      key: 'enableFlag',             // 对应查询表单字段
    },
  },
  {
    title: '商品分类',
    dataIndex: 'categoryName',
    resizable: true,
    width: 150,
    filterOptions: {
      type: 'category-tree',          // 分类树选择器
      categoryType: 1,               // 分类类型
      key: 'categoryId',             // 对应查询表单字段
    },
  },
  {
    title: '更新时间',
    dataIndex: 'updateTime',
    resizable: true,
    width: 150,
    filterOptions: {
      type: 'datetime-range',         // 日期时间范围选择器
      key: 'updateTimeRange',        // 对应查询表单字段
      ranges: true,                  // 是否显示快捷选择
    },
  },
  {
    title: '月份',
    dataIndex: 'month',
    resizable: true,
    width: 120,
    filterOptions: {
      type: 'month',                 // 月份选择器
      key: 'monthRange',             // 对应查询表单字段
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    fixed: 'right',
    width: 120,
    filterOptions: {
      type: 'submit',                // 查询按钮
      btnType: 'primary',            // 按钮类型
    },
  },
]);
```

### 10.5 表格使用完整示例

```vue
<template>
  <a-table
    size="small"
    :dataSource="tableData"
    :columns="columns"
    rowKey="id"
    :scroll="{ x: 1000, y: yHeight }"
    bordered
    :pagination="false"
    :showSorterTooltip="false"
    :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
    @change="onChange"
    @resizeColumn="handleResizeColumn"
  >
    <!-- 表头过滤器 -->
    <template #headerCell="{ column }">
      <SmartHeaderCell 
        v-model:value="queryForm[column.filterOptions?.key || column.dataIndex]" 
        :column="column" 
        @change="queryData" 
      />
    </template>
    
    <!-- 表格单元格内容 -->
    <template #bodyCell="{ text, record, column }">
      <template v-if="column.dataIndex === 'status'">
        <span>{{ $smartEnumPlugin.getDescByValue('STATUS_ENUM', text) }}</span>
      </template>
      <template v-if="column.dataIndex === 'action'">
        <div class="smart-table-operate">
          <a-button @click="editRecord(record)" type="link" v-privilege="'module:update'">编辑</a-button>
          <a-button @click="deleteRecord(record)" danger type="link" v-privilege="'module:delete'">删除</a-button>
        </div>
      </template>
    </template>
  </a-table>
</template>

<script setup>
  import SmartHeaderCell from '/@/components/support/table-header-cell/index.vue';
  
  // 表格过滤器数据处理
  function queryData(filterData) {
    if (filterData && filterData.search) {
      // 处理过滤器触发的查询
      if (filterData.type === 'date-range' || filterData.type === 'datetime-range') {
        queryForm[filterData.key + 'Begin'] = filterData.value[0];
        queryForm[filterData.key + 'End'] = filterData.value[1];
      } else {
        queryForm[filterData.key] = filterData.value;
      }
      
      // 重置页码并查询
      queryForm.pageNum = 1;
      ajaxQuery();
    }
  }
</script>
```

## 11. 表单组件规范

### 11.1 表单类型选择

**根据实际代码分析，项目中主要使用以下三种表单类型**:

#### A. 模态框表单（适用于简单表单）

```javascript
// 文件命名: {模块名}-form-modal.vue
<template>
  <a-modal 
    :open="visible" 
    :title="form.id ? '编辑' : '添加'" 
    ok-text="确认" 
    cancel-text="取消" 
    @ok="onSubmit" 
    @cancel="onClose"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <!-- 表单项 -->
    </a-form>
  </a-modal>
</template>
```

#### B. 抽屉表单（主流推荐，适用于复杂表单）

```javascript
// 文件命名: {模块名}-form-modal.vue 或 {模块名}-form-drawer.vue
<template>
  <a-drawer 
    :title="form.id ? '编辑' : '添加'" 
    :width="500" 
    :open="visible" 
    :body-style="{ paddingBottom: '80px' }" 
    @close="onClose"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <!-- 表单项 -->
    </a-form>
    
    <!-- 底部按钮（固定定位） -->
    <div class="drawer-footer">
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button type="primary" @click="onSubmit">提交</a-button>
    </div>
  </a-drawer>
</template>

<style>
.drawer-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
</style>
```

#### C. 复杂配置表单（适用于代码生成器等复杂场景）

```javascript
// 文件命名: {模块名}-config-form.vue
<template>
  <a-drawer 
    title="配置表单" 
    :width="900" 
    :open="visible" 
    @close="onClose"
  >
    <a-tabs v-model:activeKey="activeKey">
      <a-tab-pane key="basic" tab="基础配置">
        <!-- 基础配置表单 -->
      </a-tab-pane>
      <a-tab-pane key="advanced" tab="高级配置">
        <!-- 高级配置表单 -->
      </a-tab-pane>
    </a-tabs>
  </a-drawer>
</template>
```

### 11.2 表单组件完整示例

```vue
<template>
  <a-drawer 
    :title="form.goodsId ? '编辑' : '添加'" 
    :width="500" 
    :open="visible" 
    :body-style="{ paddingBottom: '80px' }" 
    @close="onClose"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 5 }">
      <!-- 分类选择 -->
      <a-form-item label="商品分类" name="categoryId">
        <CategoryTree 
          v-model:value="form.categoryId" 
          placeholder="请选择商品分类" 
          :categoryType="CATEGORY_TYPE_ENUM.GOODS.value" 
        />
      </a-form-item>
      
      <!-- 文本输入 -->
      <a-form-item label="商品名称" name="goodsName">
        <a-input v-model:value="form.goodsName" placeholder="请输入商品名称" />
      </a-form-item>
      
      <!-- 枚举选择 -->
      <a-form-item label="商品状态" name="goodsStatus">
        <SmartEnumSelect enum-name="GOODS_STATUS_ENUM" v-model:value="form.goodsStatus" />
      </a-form-item>
      
      <!-- 字典选择（支持多选） -->
      <a-form-item label="产地" name="place">
        <DictSelect 
          width="100%" 
          :dict-code="DICT_CODE_ENUM.GOODS_PLACE" 
          v-model:value="form.place" 
          mode="tags" 
        />
      </a-form-item>
      
      <!-- 单选组 -->
      <a-form-item label="上架状态" name="shelvesFlag">
        <a-radio-group v-model:value="form.shelvesFlag">
          <a-radio :value="true">上架</a-radio>
          <a-radio :value="false">下架</a-radio>
        </a-radio-group>
      </a-form-item>
      
      <!-- 数字输入 -->
      <a-form-item label="商品价格" name="price">
        <a-input-number 
          style="width: 100%" 
          placeholder="请输入商品价格" 
          v-model:value="form.price" 
          :min="0" 
        />
      </a-form-item>
      
      <!-- 文本域 -->
      <a-form-item label="备注" name="remark">
        <a-input style="width: 100%" placeholder="请输入备注" v-model:value="form.remark" />
      </a-form-item>
    </a-form>
    
    <!-- 底部按钮（固定定位） -->
    <div class="drawer-footer">
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button type="primary" @click="onSubmit">提交</a-button>
    </div>
  </a-drawer>
</template>

<script setup>
  import { ref, nextTick, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { moduleApi } from '/@/api/path/to/module-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import CategoryTree from '/@/components/business/category-tree-select/index.vue';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import DictSelect from '/@/components/support/dict-select/index.vue';
  import { CATEGORY_TYPE_ENUM } from '/@/constants/business/erp/category-const';
  import { GOODS_STATUS_ENUM } from '/@/constants/business/erp/goods-const';
  import { DICT_CODE_ENUM } from '/@/constants/support/dict-const';
  import { goodsApi } from '/@/api/business/goods/goods-api';
  import _ from 'lodash';

  // emit事件定义
  const emit = defineEmits(['reloadList']);

  // 组件引用
  const formRef = ref();
  const visible = ref(false);

  // 表单默认值
  const formDefault = {
    goodsId: undefined,
    categoryId: undefined,
    goodsName: undefined,
    goodsStatus: GOODS_STATUS_ENUM.APPOINTMENT.value,
    place: [],
    price: undefined,
    shelvesFlag: true,
    remark: '',
  };

  // 表单数据
  let form = reactive({ ...formDefault });

  // 表单验证规则
  const rules = {
    categoryId: [{ required: true, message: '请选择商品分类' }],
    goodsName: [{ required: true, message: '商品名称不能为空' }],
    goodsStatus: [{ required: true, message: '商品状态不能为空' }],
    price: [{ required: true, message: '商品价格不能为空' }],
    place: [{ required: true, message: '产地不能为空' }],
  };

  // 显示表单
  function showDrawer(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    
    // 处理特殊字段（如数组转换）
    if (form.place && form.place.length > 0) {
      form.place = form.place.split(',');
    }
    
    visible.value = true;
    nextTick(() => {
      formRef.value.clearValidate();
    });
  }

  // 关闭表单
  function onClose() {
    Object.assign(form, formDefault);
    visible.value = false;
  }

  // 提交表单
  function onSubmit() {
    formRef.value
      .validate()
      .then(async () => {
        SmartLoading.show();
        try {
          if (form.goodsId) {
            await goodsApi.updateGoods(form);
          } else {
            await goodsApi.addGoods(form);
          }
          message.success(`${form.goodsId ? '修改' : '添加'}成功`);
          onClose();
          emit('reloadList');
        } catch (error) {
          smartSentry.captureError(error);
        } finally {
          SmartLoading.hide();
        }
      })
      .catch((error) => {
        console.log('error', error);
        message.error('参数验证错误，请仔细填写表单数据!');
      });
  }

  // 暴露方法
  defineExpose({
    showDrawer,
  });
</script>

<style scoped>
.drawer-footer {
  position: absolute;
  right: 0;
  bottom: 0;
  width: 100%;
  border-top: 1px solid #e9e9e9;
  padding: 10px 16px;
  background: #fff;
  text-align: right;
  z-index: 1;
}
</style>
```

### 11.3 表单调用规范

```javascript
// 在列表页面中的使用
<template>
  <GoodsFormModal ref="formModal" @reloadList="queryData" />
</template>

<script setup>
  const formModal = ref();
  
  // 新增
  function addGoods() {
    formModal.value.showDrawer();
  }
  
  // 编辑
  function editGoods(record) {
    formModal.value.showDrawer(record);
  }
</script>
```

## 12. 样式规范

### 12.1 使用框架样式类

```css
/* 查询表单 */
.smart-query-form
.smart-query-form-row
.smart-query-form-item

/* 表格操作 */
.smart-table-btn-block
.smart-table-operate-block
.smart-table-setting-block
.smart-table-operate

/* 分页 */
.smart-query-table-page

/* 间距 */
.smart-margin-left10
.smart-margin-right10
.smart-margin-top10
.smart-margin-bottom10
```

### 12.2 响应式设计

```less
// 响应式样式
.responsive-layout {
  @media (max-width: 768px) {
    .smart-table-btn-block {
      flex-direction: column;
      gap: 10px;
    }
    
    .ant-table {
      font-size: 12px;
    }
  }
  
  @media (min-width: 1200px) {
    .smart-query-form {
      .ant-row {
        display: flex;
        flex-wrap: wrap;
      }
    }
  }
}
```

### 12.3 自定义样式

- 优先使用框架提供的样式类
- 必要时可添加模块特定样式
- 避免内联样式，使用CSS类
- 使用CSS变量进行主题定制

```less
// 自定义主题变量
:root {
  --primary-color: #1890ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --border-radius: 4px;
  --box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.custom-card {
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  
  &:hover {
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  }
}
```

## 13. 国际化规范

### 13.1 文本外化

```javascript
// 使用 $t 函数
<template>
  <a-button>{{ $t('common.add') }}</a-button>
</template>

// 或使用 t 函数
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const buttonText = t('common.add');
```

### 13.2 国际化配置

```javascript
// locales/zh-CN/common.js
export default {
  add: '添加',
  edit: '编辑',
  delete: '删除',
  save: '保存',
  cancel: '取消',
  confirm: '确认',
  search: '搜索',
  reset: '重置',
  export: '导出',
  import: '导入',
  
  // 表格相关
  table: {
    operation: '操作',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间',
    noData: '暂无数据',
  },
  
  // 消息提示
  message: {
    success: '操作成功',
    error: '操作失败',
    deleteConfirm: '确定要删除吗？',
    batchDeleteConfirm: '确定要批量删除选中的数据吗？',
  }
};
```

## 14. 开发最佳实践

### 14.1 代码质量

1. **一致性**: 严格遵循现有代码风格
2. **可读性**: 添加必要的注释和文档
3. **可维护性**: 保持代码结构清晰
4. **可扩展性**: 考虑未来功能扩展

### 14.2 性能优化

1. **懒加载**: 大数据量表格使用虚拟滚动
2. **防抖节流**: 搜索和resize事件使用lodash工具
3. **缓存**: 合理使用computed和watch
4. **组件拆分**: 复杂页面拆分为多个组件

### 14.3 代码复用

```javascript
// composables/useTableData.js
export function useTableData(api, queryForm) {
  const loading = ref(false);
  const data = ref([]);
  const total = ref(0);

  const queryData = async () => {
    loading.value = true;
    try {
      const result = await api.queryList(queryForm);
      data.value = result.data.list;
      total.value = result.data.total;
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    data,
    total,
    queryData
  };
}

// 在组件中使用
const { loading, data, total, queryData } = useTableData(goodsApi, queryForm);
```

### 14.4 测试要求

1. **功能测试**: 确保所有功能正常工作
2. **权限测试**: 验证权限控制正确
3. **响应式测试**: 确保在不同屏幕尺寸下正常显示
4. **浏览器兼容**: 确保主流浏览器兼容

## 15. 代码检查清单

**新功能开发完成后，请逐项检查**:

### 15.1 基础规范检查

- [ ] 文件命名符合规范
- [ ] 目录结构正确
- [ ] 代码注释完整
- [ ] 导入语句有序

### 15.2 功能实现检查

- [ ] API接口定义完整
- [ ] 常量定义并注册
- [ ] 权限控制配置
- [ ] 表格ID注册
- [ ] 错误处理完善

### 15.3 用户体验检查

- [ ] 加载状态显示
- [ ] 成功/失败提示
- [ ] 表单验证完善
- [ ] 响应式布局适配
- [ ] 操作反馈及时

### 15.4 性能和安全检查

- [ ] 大数据量优化
- [ ] 防抖节流处理
- [ ] 内存泄漏预防
- [ ] 输入验证完善
- [ ] 敏感数据脱敏

### 15.5 测试检查

- [ ] 功能测试通过
- [ ] 权限测试验证
- [ ] 边界条件测试
- [ ] 异常情况处理

---

**注意**: 此规范基于SmartAdmin v3.0.0，如有框架升级，请及时更新此文档。

**维护者**: 1024创新实验室  
**更新时间**: 2025年1月  
**版本**: v2.0.0（详细增强版）

## 12. 错误处理规范

### 12.1 统一错误处理

```javascript
try {
  // API调用
  let result = await moduleApi.someMethod(params);
  // 处理成功结果
} catch (e) {
  smartSentry.captureError(e);  // 统一错误上报
} finally {
  SmartLoading.hide();          // 关闭loading
}
```

### 12.2 用户提示

```javascript
// 成功提示
message.success('操作成功');

// 错误提示(框架会自动处理API错误)
message.error('操作失败');

// 确认弹窗
Modal.confirm({
  title: '提示',
  content: '确定要删除吗？',
  okText: '确定',
  okType: 'danger',
  onOk() {
    // 执行操作
  },
});
```

### 12.3 错误边界处理

```javascript
// utils/error-boundary.js
export class ErrorBoundary {
  static handleAsyncError(fn, errorMessage = '操作失败') {
    return async (...args) => {
      try {
        return await fn(...args);
      } catch (error) {
        console.error('AsyncError:', error);
        smartSentry.captureError(error);
        message.error(errorMessage);
        throw error;
      }
    };
  }

  static handleSyncError(fn, errorMessage = '操作失败') {
    return (...args) => {
      try {
        return fn(...args);
      } catch (error) {
        console.error('SyncError:', error);
        smartSentry.captureError(error);
        message.error(errorMessage);
        throw error;
      }
    };
  }
}

// 使用示例
const safeQueryData = ErrorBoundary.handleAsyncError(queryData, '查询数据失败');
```

## 13. 表格ID注册规范

### 13.1 表格ID配置文件

**文件位置**: `src/constants/support/table-id-const.js`

```javascript
/*
 * 表格id常量
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-08-26 20:19:22
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

// 系统模块表格ID
const systemInitTableId = 10000;
const SYSTEM = {
  EMPLOYEE: systemInitTableId + 1,
  ROLE: systemInitTableId + 2,
  MENU: systemInitTableId + 3,
  DEPARTMENT: systemInitTableId + 4,
  POSITION: systemInitTableId + 5,
};

// 支撑模块表格ID  
const supportInitTableId = 20000;
const SUPPORT = {
  CONFIG: supportInitTableId + 1,
  DICT: supportInitTableId + 2,
  FEEDBACK: supportInitTableId + 3,
  FILE: supportInitTableId + 4,
  HELP_DOC: supportInitTableId + 5,
  JOB: supportInitTableId + 6,
  LOGIN_LOG: supportInitTableId + 7,
  OPERATE_LOG: supportInitTableId + 8,
  RELOAD: supportInitTableId + 9,
  SERIAL_NUMBER: supportInitTableId + 10,
  CHANGE_LOG: supportInitTableId + 11,
  CODE_GENERATOR: supportInitTableId + 12,
  HEART_BEAT: supportInitTableId + 13,
  CACHE: supportInitTableId + 14,
  MESSAGE: supportInitTableId + 15,
  LOGIN_FAIL: supportInitTableId + 16,
  LEVEL3_PROTECT: supportInitTableId + 17,
  API_ENCRYPT: supportInitTableId + 18,
};

// 业务模块表格ID
const businessInitTableId = 30000;
const BUSINESS = {
  ERP: {
    GOODS: businessInitTableId + 1,
    CATALOG: businessInitTableId + 2,
    ORDER: businessInitTableId + 3,
  },
  OA: {
    NOTICE: businessInitTableId + 101,
    ENTERPRISE: businessInitTableId + 102,
  },
};

export const TABLE_ID_CONST = {
  SYSTEM,
  SUPPORT,
  BUSINESS,
};

export default TABLE_ID_CONST;
```

### 13.2 表格ID使用规范

```javascript
// 在列表页面中使用
import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';

<template>
  <TableOperator 
    v-model="columns" 
    :tableId="TABLE_ID_CONST.BUSINESS.ERP.GOODS" 
    :refresh="queryData" 
  />
</template>
```

### 13.3 新增表格ID步骤

1. **确定模块类型**: 系统(SYSTEM)、支撑(SUPPORT)、业务(BUSINESS)
2. **找到对应起始ID**: 
   - SYSTEM: 10000
   - SUPPORT: 20000  
   - BUSINESS: 30000
3. **按顺序递增**: 在对应模块中按顺序添加
4. **嵌套结构**: 业务模块可以使用嵌套结构（如ERP.GOODS）

**示例 - 添加新的商品管理表格ID**:
```javascript
const BUSINESS = {
  ERP: {
    GOODS: businessInitTableId + 1,
    CATALOG: businessInitTableId + 2,
    ORDER: businessInitTableId + 3,
    SUPPLIER: businessInitTableId + 4,  // 新增供应商表格ID
  },
};
```

## 14. 国际化规范

### 14.1 文本外化

```javascript
// 使用 $t 函数
<template>
  <a-button>{{ $t('common.add') }}</a-button>
</template>

// 或使用 t 函数
import { useI18n } from 'vue-i18n';
const { t } = useI18n();
const buttonText = t('common.add');
```

### 14.2 国际化配置

```javascript
// locales/zh-CN/common.js
export default {
  add: '添加',
  edit: '编辑',
  delete: '删除',
  save: '保存',
  cancel: '取消',
  confirm: '确认',
  search: '搜索',
  reset: '重置',
  export: '导出',
  import: '导入',
  
  // 表格相关
  table: {
    operation: '操作',
    status: '状态',
    createTime: '创建时间',
    updateTime: '更新时间',
    noData: '暂无数据',
  },
  
  // 消息提示
  message: {
    success: '操作成功',
    error: '操作失败',
    deleteConfirm: '确定要删除吗？',
    batchDeleteConfirm: '确定要批量删除选中的数据吗？',
  }
};
```

## 15. 开发最佳实践

### 15.1 代码质量

1. **一致性**: 严格遵循现有代码风格
2. **可读性**: 添加必要的注释和文档
3. **可维护性**: 保持代码结构清晰
4. **可扩展性**: 考虑未来功能扩展

### 15.2 性能优化

1. **懒加载**: 大数据量表格使用虚拟滚动
2. **防抖节流**: 搜索和resize事件使用lodash工具
3. **缓存**: 合理使用computed和watch
4. **组件拆分**: 复杂页面拆分为多个组件

### 15.3 代码复用

```javascript
// composables/useTableData.js
export function useTableData(api, queryForm) {
  const loading = ref(false);
  const data = ref([]);
  const total = ref(0);

  const queryData = async () => {
    loading.value = true;
    try {
      const result = await api.queryList(queryForm);
      data.value = result.data.list;
      total.value = result.data.total;
    } catch (error) {
      smartSentry.captureError(error);
    } finally {
      loading.value = false;
    }
  };

  return {
    loading,
    data,
    total,
    queryData
  };
}

// 在组件中使用
const { loading, data, total, queryData } = useTableData(goodsApi, queryForm);
```

### 15.4 测试要求

1. **功能测试**: 确保所有功能正常工作
2. **权限测试**: 验证权限控制正确
3. **响应式测试**: 确保在不同屏幕尺寸下正常显示
4. **浏览器兼容**: 确保主流浏览器兼容

## 16. 代码检查清单

**新功能开发完成后，请逐项检查**:

### 16.1 基础规范检查

- [ ] 文件命名符合规范
- [ ] 目录结构正确
- [ ] 代码注释完整
- [ ] 导入语句有序

### 16.2 功能实现检查

- [ ] API接口定义完整
- [ ] 常量定义并注册
- [ ] 权限控制配置
- [ ] 表格ID注册
- [ ] 错误处理完善

### 16.3 用户体验检查

- [ ] 加载状态显示
- [ ] 成功/失败提示
- [ ] 表单验证完善
- [ ] 响应式布局适配
- [ ] 操作反馈及时

### 16.4 性能和安全检查

- [ ] 大数据量优化
- [ ] 防抖节流处理
- [ ] 内存泄漏预防
- [ ] 输入验证完善
- [ ] 敏感数据脱敏

### 16.5 测试检查

- [ ] 功能测试通过
- [ ] 权限测试验证
- [ ] 边界条件测试
- [ ] 异常情况处理

---

**注意**: 此规范基于SmartAdmin v3.0.0，如有框架升级，请及时更新此文档。

**维护者**: 1024创新实验室  
**更新时间**: 2025年1月  
**版本**: v2.1.0（实际代码匹配增强版）