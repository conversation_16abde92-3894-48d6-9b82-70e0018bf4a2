# Views 目录结构文档

## 项目概述
本文档详细记录了 `src/views` 目录下的所有 Vue 组件文件结构，共包含 126 个 Vue 文件。

---

## 📁 一级目录结构

```
src/views/
├── business/          # 业务模块
├── support/           # 支撑功能模块 
└── system/            # 系统核心模块
```

---

## 🏢 Business 业务模块

### ERP 企业资源计划
**路径：`src/views/business/erp/`**

#### 📦 catalog - 分类目录管理
- `custom-catalog.vue` - 自定义分类页面
- `goods-catalog.vue` - 商品分类页面
- **components/**
  - `category-form-modal.vue` - 分类表单弹窗组件
  - `category-tree-table.vue` - 分类树表格组件

#### 🛍️ goods - 商品管理
- `goods-list.vue` - 商品列表页面
- **components/**
  - `goods-form-modal.vue` - 商品表单弹窗组件

#### 📋 order - 订单管理
- *暂无文件 (空目录)*

### OA 办公自动化
**路径：`src/views/business/oa/`**

#### 🏢 enterprise - 企业管理
- `enterprise-list.vue` - 企业列表页面
- `enterprise-detail.vue` - 企业详情页面
- **components/**
  - `enterprise-operate-modal.vue` - 企业操作弹窗
  - `enterprise-bank-list.vue` - 企业银行账户列表
  - `enterprise-bank-operate-modal.vue` - 银行账户操作弹窗
  - `enterprise-invoice-list.vue` - 企业发票列表
  - `enterprise-invoice-operate-modal.vue` - 发票操作弹窗
  - `enterprise-employee-list.vue` - 企业员工列表

#### 📢 notice - 通知公告
- `notice-list.vue` - 通知列表页面
- `notice-detail.vue` - 通知详情页面
- `notice-employee-list.vue` - 员工通知列表
- `notice-employee-detail.vue` - 员工通知详情
- **components/**
  - `notice-form-drawer.vue` - 通知表单抽屉
  - `notice-form-visible-modal.vue` - 通知可见性设置弹窗
  - `notice-form-visible-transfer-department.vue` - 部门可见性转移
  - `notice-form-visible-transfer-employee.vue` - 员工可见性转移
  - `notice-view-record-list.vue` - 通知查看记录列表

### 订单管理
**路径：`src/views/business/order/`**

#### 💰 segment-order - 分段订单
- `segment-order-list.vue` - 分段订单列表页面

---

## 🛠️ Support 支撑功能模块

### 🔐 api-encrypt - API加密
- `api-encrypt-index.vue` - API加密管理页面

### 💾 cache - 缓存管理
- `cache-list.vue` - 缓存列表页面

### 📝 change-log - 更新日志
- `change-log-list.vue` - 更新日志列表
- `change-log-form.vue` - 更新日志表单
- `change-log-modal.vue` - 更新日志弹窗

### 🔧 code-generator - 代码生成器
- `code-generator-list.vue` - 代码生成器列表
- **components/form/** - 表单组件
  - `code-generator-table-config-form.vue` - 表配置主表单
  - `code-generator-table-config-form-basic.vue` - 基础配置表单
  - `code-generator-table-config-form-delete.vue` - 删除配置表单
  - `code-generator-table-config-form-field.vue` - 字段配置表单
  - `code-generator-table-config-form-insert-and-update.vue` - 增改配置表单
  - `code-generator-table-config-form-query-field.vue` - 查询字段配置表单
  - `code-generator-table-config-form-table-field.vue` - 表字段配置表单
- **components/preview/** - 预览组件
  - `code-generator-preview-modal.vue` - 代码预览弹窗

### ⚙️ config - 系统配置
- `config-list.vue` - 配置列表页面
- `config-form-modal.vue` - 配置表单弹窗

### 📚 dict - 字典管理
- `index.vue` - 字典管理主页面
- **components/**
  - `dict-form-modal.vue` - 字典表单弹窗
  - `dict-data-modal.vue` - 字典数据弹窗
  - `dict-data-form-modal.vue` - 字典数据表单弹窗

### 💬 feedback - 意见反馈
- `feedback-list.vue` - 反馈列表页面

### 📁 file - 文件管理
- `file-list.vue` - 文件列表页面

### 💓 heart-beat - 心跳检测
- `heart-beat-list.vue` - 心跳检测列表

### 📖 help-doc - 帮助文档
#### management - 管理端
- `help-doc-manage-list.vue` - 帮助文档管理列表
- **components/**
  - `help-doc-catalog-tree.vue` - 文档分类树
  - `help-doc-catalog-tree-select.vue` - 文档分类树选择器
  - `help-doc-catalog-form-modal.vue` - 文档分类表单弹窗
  - `help-doc-form-drawer.vue` - 文档表单抽屉
  - `help-doc-list.vue` - 文档列表组件

#### user-view - 用户查看端
- `help-doc-user-view.vue` - 用户查看帮助文档
- **components/**
  - `help-doc-view-record-list.vue` - 文档查看记录列表

### ⚡ job - 定时任务
- `job-list.vue` - 定时任务列表
- **components/**
  - `job-form-modal.vue` - 任务表单弹窗
  - `job-log-list-modal.vue` - 任务日志列表弹窗
  - `deleted-job-list.vue` - 已删除任务列表

### 🛡️ level3protect - 三级等保
- `level3-protect-config-index.vue` - 三级等保配置页面
- `data-masking-list.vue` - 数据脱敏列表

### 🚫 login-fail - 登录失败
- `login-fail-list.vue` - 登录失败记录列表

### 📊 login-log - 登录日志
- `login-log-list.vue` - 登录日志列表

### 💌 message - 消息管理
- `message-list.vue` - 消息列表页面
- **components/**
  - `message-send-form.vue` - 消息发送表单
  - `message-receiver-modal.vue` - 消息接收者弹窗

### 📋 operate-log - 操作日志
- `operate-log-list.vue` - 操作日志列表
- `operate-log-detail-modal.vue` - 操作日志详情弹窗

### 🔄 reload - 系统重载
- `reload-list.vue` - 重载列表页面
- `reload-result-list.vue` - 重载结果列表
- `do-reload-form-modal.vue` - 执行重载表单弹窗

### 🔢 serial-number - 序列号
- `serial-number-list.vue` - 序列号列表
- `serial-number-record-list.vue` - 序列号记录列表
- `serial-number-generate-form-modal.vue` - 序列号生成表单弹窗

---

## 🖥️ System 系统核心模块

### 🚫 40X - 错误页面
- `403.vue` - 403 禁止访问页面
- `404.vue` - 404 页面未找到

### 👤 account - 个人账户
- `index.vue` - 个人中心主页面
- **components/**
  - **center/** - 个人信息中心
    - `index.vue` - 个人信息页面
  - **login-log/** - 登录日志
    - `index.vue` - 个人登录日志
  - **message/** - 个人消息
    - `index.vue` - 个人消息列表
    - **components/**
      - `message-detail.vue` - 消息详情
  - **notice/** - 个人通知
    - `index.vue` - 个人通知页面
  - **operate-log/** - 个人操作日志
    - `index.vue` - 个人操作记录
  - **password/** - 密码管理
    - `index.vue` - 密码修改页面

### 🏢 department - 部门管理
- `department-list.vue` - 部门列表页面
- **components/**
  - `department-form-modal.vue` - 部门表单弹窗

### 👥 employee - 员工管理
- `index.vue` - 员工管理主页面
- **components/**
  - **department-children/** - 子部门
    - `index.vue` - 子部门组件
  - **department-tree/** - 部门树
    - `index.vue` - 部门树组件
  - **employee-department-form-modal/** - 员工部门表单
    - `index.vue` - 员工部门关系表单弹窗
  - **employee-form-modal/** - 员工表单
    - `index.vue` - 员工表单弹窗
  - **employee-list/** - 员工列表
    - `index.vue` - 员工列表组件
  - **employee-password-dialog/** - 员工密码对话框
    - `index.vue` - 员工密码修改对话框

### 🏠 home - 首页
- `index.vue` - 首页主页面
- `home-header.vue` - 首页头部
- `home-notice.vue` - 首页通知
- `ad-modal.vue` - 广告弹窗
- **components/**
  - `changelog-card.vue` - 更新日志卡片
  - `default-home-card.vue` - 默认首页卡片
  - `official-account-card.vue` - 公众号卡片
  - **echarts/** - 图表组件
    - `category.vue` - 分类图表
    - `gauge.vue` - 仪表盘图表
    - `gradient.vue` - 渐变图表
    - `pie.vue` - 饼图
  - **quick-entry/** - 快捷入口
    - `home-quick-entry.vue` - 快捷入口组件
    - `home-quick-entry-modal.vue` - 快捷入口设置弹窗
  - **to-be-done-card/** - 待办事项
    - `home-to-be-done.vue` - 待办事项组件
    - `to-be-done-modal.vue` - 待办事项弹窗

### 🔐 login - 登录页面
- **login/** - 登录方式1
  - `login.vue` - 标准登录页面
- **login2/** - 登录方式2
  - `login.vue` - 登录页面样式2
- **login3/** - 登录方式3
  - `login.vue` - 登录页面样式3

### 📋 menu - 菜单管理
- `menu-list.vue` - 菜单列表页面
- **components/**
  - `menu-operate-modal.vue` - 菜单操作弹窗
  - `menu-tree-select.vue` - 菜单树选择器

### 💼 position - 职位管理
- `position-list.vue` - 职位列表页面
- `position-form.vue` - 职位表单页面

### 🔑 role - 角色权限
- `index.vue` - 角色管理主页面
- **components/**
  - **role-data-scope/** - 数据权限
    - `index.vue` - 数据权限设置
  - **role-employee-list/** - 角色员工
    - `index.vue` - 角色员工列表
  - **role-form-modal/** - 角色表单
    - `index.vue` - 角色表单弹窗
  - **role-list/** - 角色列表
    - `index.vue` - 角色列表组件
  - **role-setting/** - 角色设置
    - `index.vue` - 角色设置页面
  - **role-tree/** - 权限树
    - `index.vue` - 权限树组件
    - `role-tree-checkbox.vue` - 权限树复选框
    - `role-tree-menu.vue` - 权限树菜单
    - `role-tree-point.vue` - 权限树功能点

---

## 📊 统计信息

- **总文件数量**: 126 个 Vue 文件
- **主要模块数量**: 3 个 (business, support, system)
- **业务子模块**: 4 个 (erp, oa, order, 等)
- **支撑功能模块**: 17 个
- **系统核心模块**: 9 个

---

## 📅 文档信息

- **生成时间**: 2025年01月27日
- **文档版本**: v1.0
- **项目**: ryf-admin-web
- **维护者**: 1024创新实验室

---

## 🔗 相关链接

- [项目根目录](../README.md)
- [API 文档](./api-structure.md)
- [组件文档](./components-structure.md) 