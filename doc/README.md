# 项目文档目录

本目录包含 `ryf-admin-web` 项目的各种文档文件。

## 📁 文档列表

### Views 目录结构文档
- **[views-directory-structure.md](./views-directory-structure.md)** - 详细的 Views 目录结构文档
  - 包含所有 126 个 Vue 文件的详细说明
  - 按模块分类组织
  - 包含文件功能描述和用途

- **[views-tree-structure.md](./views-tree-structure.md)** - Views 目录树状结构
  - 以树状图形式展示完整目录结构
  - 简洁明了的文件层级关系
  - 便于快速浏览和定位文件

## 🗂️ 目录结构概览

```
doc/
├── README.md                           # 本文件
├── views-directory-structure.md        # Views 详细文档
└── views-tree-structure.md            # Views 树状结构
```

## 📊 统计信息

- **Vue 文件总数**: 126 个
- **主要模块**: 3 个 (business、support、system)
- **业务子模块**: 4 个
- **支撑功能模块**: 17 个
- **系统核心模块**: 9 个

## 🚀 使用说明

1. **查看详细功能** - 阅读 `views-directory-structure.md` 了解每个文件的具体功能
2. **浏览目录结构** - 查看 `views-tree-structure.md` 快速了解项目组织结构
3. **查找特定功能** - 使用 Ctrl+F 在文档中搜索关键词

## 🔄 更新说明

当项目结构发生变化时，请及时更新相关文档：

1. 新增 Vue 文件时，更新对应的文档
2. 删除或移动文件时，同步修改文档内容
3. 修改文件功能时，更新功能描述

## 🧪 测试账号

### 商户测试账号
- **用户名**: ***********
- **密码**: 12345678
- **用途**: 商户端功能测试，用于联调和功能验证

### 管理员账号
- **用户名**: admin
- **密码**: 123456
- **用途**: 系统管理和后台操作

> ⚠️ **注意**: 以上账号仅用于开发测试环境，请勿在生产环境使用

## 📞 联系信息

- **项目**: ryf-admin-web
- **维护者**: 1024创新实验室
- **更新时间**: 2025年01月27日 

部署命令：
sh deploy.sh test
sudo sh deploy.sh test


查看git，看下已修改的全部代码，写下git提交日志,text格式，不要看之前的聊天记录修改的内容，日志要包含文件路径。
