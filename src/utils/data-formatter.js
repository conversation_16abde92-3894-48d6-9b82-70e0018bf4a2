/*
 * 数据格式化工具类
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2025-01-27 10:00:00
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

import dayjs from 'dayjs';

export const dataFormatter = {
  // 金额格式化
  formatMoney(amount, decimals = 2) {
    if (!amount || isNaN(amount)) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals
    });
  },

  // 文件大小格式化
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  },

  // 时间格式化
  formatTime(time, format = 'YYYY-MM-DD HH:mm:ss') {
    if (!time) return '-';
    return dayjs(time).format(format);
  },

  // 相对时间
  formatRelativeTime(time) {
    if (!time) return '-';
    return dayjs(time).fromNow();
  },

  // 脱敏处理
  maskPhone(phone) {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  },

  maskEmail(email) {
    if (!email) return '';
    const [name, domain] = email.split('@');
    if (name.length <= 2) return email;
    return name.charAt(0) + '***' + name.slice(-1) + '@' + domain;
  },

  maskIdCard(idCard) {
    if (!idCard) return '';
    return idCard.replace(/(\d{6})\d{8}(\d{4})/, '$1********$2');
  },

  maskBankCard(cardNumber) {
    if (!cardNumber) return '';
    return cardNumber.replace(/(\d{4})\d+(\d{4})/, '$1 **** **** $2');
  },

  // 高亮关键词
  highlightKeywords(text, keywords) {
    if (!keywords || !text) return text;
    const regex = new RegExp(`(${keywords})`, 'gi');
    return text.replace(regex, '<mark>$1</mark>');
  },

  // 百分比格式化
  formatPercent(value, decimals = 2) {
    if (!value || isNaN(value)) return '0.00%';
    return (Number(value) * 100).toFixed(decimals) + '%';
  },

  // 数字格式化（千分位）
  formatNumber(number) {
    if (!number || isNaN(number)) return '0';
    return Number(number).toLocaleString('zh-CN');
  },

  // 订单编号格式化
  formatOrderCode(code) {
    if (!code) return '';
    // 插入分隔符，使订单号更易读
    return code.replace(/(.{2})(.{8})(.{2})(.+)/, '$1-$2-$3-$4');
  },

  // 状态文本格式化
  formatStatus(status, enumObj) {
    const statusItem = Object.values(enumObj).find(item => item.value === status);
    return statusItem ? statusItem.desc : '未知';
  },

  // 金额单位转换
  formatAmountWithUnit(amount) {
    if (!amount || isNaN(amount)) return '0';
    const num = Number(amount);
    
    if (num >= 100000000) {
      return (num / 100000000).toFixed(2) + '亿';
    } else if (num >= 10000) {
      return (num / 10000).toFixed(2) + '万';
    } else {
      return num.toFixed(2);
    }
  },

  // 时长格式化
  formatDuration(seconds) {
    if (!seconds || isNaN(seconds)) return '0秒';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    
    if (hours > 0) {
      return `${hours}小时${minutes}分钟${secs}秒`;
    } else if (minutes > 0) {
      return `${minutes}分钟${secs}秒`;
    } else {
      return `${secs}秒`;
    }
  },

  // 距离格式化
  formatDistance(meters) {
    if (!meters || isNaN(meters)) return '0m';
    
    if (meters >= 1000) {
      return (meters / 1000).toFixed(1) + 'km';
    } else {
      return meters + 'm';
    }
  },
}; 