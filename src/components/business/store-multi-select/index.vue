<!--
  * 门店多选组件
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-select
    v-model:value="selectedStoreIds"
    mode="multiple"
    :placeholder="placeholder"
    :style="{ width: width }"
    :allowClear="allowClear"
    :loading="loading"
    :disabled="disabled"
    :showSearch="showSearch"
    :filterOption="filterOption"
    :maxTagCount="maxTagCount"
    :maxTagPlaceholder="maxTagPlaceholder"
    :dropdownStyle="dropdownStyle"
    @change="handleChange"
  >
    <a-select-option
      v-for="store in storeList"
      :key="store.storeId"
      :value="store.storeId"
    >
      {{ store.storeName }}
    </a-select-option>
  </a-select>
</template>

<script setup>
  import { ref, onMounted, watch } from 'vue';
  import { storeApi } from '/@/api/system/store-api';
  import { useUserStore } from '/@/store/modules/system/user';
  import { smartSentry } from '/@/lib/smart-sentry';

  // Props
  const props = defineProps({
    modelValue: {
      type: Array,
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择门店'
    },
    width: {
      type: String,
      default: '200px'
    },
    allowClear: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    showSearch: {
      type: Boolean,
      default: true
    },
    autoLoad: {
      type: Boolean,
      default: true
    },
    // 最多显示多少个标签，超过则显示 +N
    maxTagCount: {
      type: [Number, String],
      default: 'responsive'
    },
    // 当超过最大标签数时的占位文本
    maxTagPlaceholder: {
      type: [String, Function],
      default: undefined
    }
  });

  // Emits
  const emit = defineEmits(['update:modelValue', 'change']);

  // 响应式数据
  const selectedStoreIds = ref([]);
  const storeList = ref([]);
  const loading = ref(false);
  const userStore = useUserStore();

  // 下拉框样式
  const dropdownStyle = {
    maxHeight: '300px',
    overflow: 'auto'
  };

  // 监听 modelValue 变化
  watch(
    () => props.modelValue,
    (newValue) => {
      selectedStoreIds.value = newValue || [];
    },
    { immediate: true }
  );

  // 过滤选项
  const filterOption = (input, option) => {
    const store = storeList.value.find(s => s.storeId === option.value);
    return store && store.storeName.toLowerCase().includes(input.toLowerCase());
  };

  // 处理选择变化
  const handleChange = (values) => {
    selectedStoreIds.value = values;
    emit('update:modelValue', values);
    emit('change', values);
  };

  // 加载门店列表
  const loadStoreList = async () => {
    try {
      loading.value = true;
      const employeeId = userStore.employeeId;
      
      if (!employeeId) {
        console.warn('员工ID为空，无法加载门店列表');
        return;
      }

      const result = await storeApi.getEmployeeStoreList(employeeId);
      storeList.value = result.data || [];
      
    } catch (error) {
      console.error('加载门店列表失败:', error);
      smartSentry.captureError(error);
    } finally {
      loading.value = false;
    }
  };

  // 组件挂载时自动加载
  onMounted(() => {
    if (props.autoLoad) {
      loadStoreList();
    }
  });

  // 暴露方法
  defineExpose({
    loadStoreList,
    getStoreList: () => storeList.value,
    getSelectedStores: () => {
      return storeList.value.filter(store => 
        selectedStoreIds.value.includes(store.storeId)
      );
    }
  });
</script>

<style scoped>
/* 保持原生 ant design 样式，只做必要的微调 */
</style> 