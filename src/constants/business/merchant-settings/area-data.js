/**
 * 全国省市区数据
 * 用于级联选择器
 */

// 省市区级联数据
export const regionData = [
  {
    value: '11',
    label: '北京市',
    children: [
      {
        value: '1101',
        label: '市辖区',
        children: [
          { value: '110101', label: '东城区' },
          { value: '110102', label: '西城区' },
          { value: '110105', label: '朝阳区' },
          { value: '110106', label: '丰台区' },
          { value: '110107', label: '石景山区' },
          { value: '110108', label: '海淀区' },
          { value: '110109', label: '门头沟区' },
          { value: '110111', label: '房山区' },
          { value: '110112', label: '通州区' },
          { value: '110113', label: '顺义区' },
          { value: '110114', label: '昌平区' },
          { value: '110115', label: '大兴区' },
          { value: '110116', label: '怀柔区' },
          { value: '110117', label: '平谷区' },
          { value: '110118', label: '密云区' },
          { value: '110119', label: '延庆区' }
        ]
      }
    ]
  },
  {
    value: '31',
    label: '上海市',
    children: [
      {
        value: '3101',
        label: '市辖区',
        children: [
          { value: '310101', label: '黄浦区' },
          { value: '310104', label: '徐汇区' },
          { value: '310105', label: '长宁区' },
          { value: '310106', label: '静安区' },
          { value: '310107', label: '普陀区' },
          { value: '310109', label: '虹口区' },
          { value: '310110', label: '杨浦区' },
          { value: '310112', label: '闵行区' },
          { value: '310113', label: '宝山区' },
          { value: '310114', label: '嘉定区' },
          { value: '310115', label: '浦东新区' },
          { value: '310116', label: '金山区' },
          { value: '310117', label: '松江区' },
          { value: '310118', label: '青浦区' },
          { value: '310120', label: '奉贤区' },
          { value: '310151', label: '崇明区' }
        ]
      }
    ]
  },
  {
    value: '51',
    label: '四川省',
    children: [
      {
        value: '5101',
        label: '成都市',
        children: [
          { value: '510104', label: '锦江区' },
          { value: '510105', label: '青羊区' },
          { value: '510106', label: '金牛区' },
          { value: '510107', label: '武侯区' },
          { value: '510108', label: '成华区' },
          { value: '510112', label: '龙泉驿区' },
          { value: '510113', label: '青白江区' },
          { value: '510114', label: '新都区' },
          { value: '510115', label: '温江区' },
          { value: '510116', label: '双流区' },
          { value: '510117', label: '郫都区' },
          { value: '510118', label: '新津区' },
          { value: '510121', label: '金堂县' },
          { value: '510129', label: '大邑县' },
          { value: '510131', label: '蒲江县' },
          { value: '510181', label: '都江堰市' },
          { value: '510182', label: '彭州市' },
          { value: '510183', label: '邛崃市' },
          { value: '510184', label: '崇州市' },
          { value: '510185', label: '简阳市' }
        ]
      },
      {
        value: '5103',
        label: '自贡市',
        children: [
          { value: '510302', label: '自流井区' },
          { value: '510303', label: '贡井区' },
          { value: '510304', label: '大安区' },
          { value: '510311', label: '沿滩区' },
          { value: '510321', label: '荣县' },
          { value: '510322', label: '富顺县' }
        ]
      },
      {
        value: '5104',
        label: '攀枝花市',
        children: [
          { value: '510402', label: '东区' },
          { value: '510403', label: '西区' },
          { value: '510411', label: '仁和区' },
          { value: '510421', label: '米易县' },
          { value: '510422', label: '盐边县' }
        ]
      },
      {
        value: '5105',
        label: '泸州市',
        children: [
          { value: '510502', label: '江阳区' },
          { value: '510503', label: '纳溪区' },
          { value: '510504', label: '龙马潭区' },
          { value: '510521', label: '泸县' },
          { value: '510522', label: '合江县' },
          { value: '510524', label: '叙永县' },
          { value: '510525', label: '古蔺县' }
        ]
      }
    ]
  },
  {
    value: '44',
    label: '广东省',
    children: [
      {
        value: '4401',
        label: '广州市',
        children: [
          { value: '440103', label: '荔湾区' },
          { value: '440104', label: '越秀区' },
          { value: '440105', label: '海珠区' },
          { value: '440106', label: '天河区' },
          { value: '440111', label: '白云区' },
          { value: '440112', label: '黄埔区' },
          { value: '440113', label: '番禺区' },
          { value: '440114', label: '花都区' },
          { value: '440115', label: '南沙区' },
          { value: '440117', label: '从化区' },
          { value: '440118', label: '增城区' }
        ]
      },
      {
        value: '4403',
        label: '深圳市',
        children: [
          { value: '440303', label: '罗湖区' },
          { value: '440304', label: '福田区' },
          { value: '440305', label: '南山区' },
          { value: '440306', label: '宝安区' },
          { value: '440307', label: '龙岗区' },
          { value: '440308', label: '盐田区' },
          { value: '440309', label: '龙华区' },
          { value: '440310', label: '坪山区' },
          { value: '440311', label: '光明区' }
        ]
      }
    ]
  },
  {
    value: '32',
    label: '江苏省',
    children: [
      {
        value: '3201',
        label: '南京市',
        children: [
          { value: '320102', label: '玄武区' },
          { value: '320104', label: '秦淮区' },
          { value: '320105', label: '建邺区' },
          { value: '320106', label: '鼓楼区' },
          { value: '320111', label: '浦口区' },
          { value: '320113', label: '栖霞区' },
          { value: '320114', label: '雨花台区' },
          { value: '320115', label: '江宁区' },
          { value: '320116', label: '六合区' },
          { value: '320117', label: '溧水区' },
          { value: '320118', label: '高淳区' }
        ]
      },
      {
        value: '3205',
        label: '苏州市',
        children: [
          { value: '320505', label: '虎丘区' },
          { value: '320506', label: '吴中区' },
          { value: '320507', label: '相城区' },
          { value: '320508', label: '姑苏区' },
          { value: '320509', label: '吴江区' },
          { value: '320581', label: '常熟市' },
          { value: '320582', label: '张家港市' },
          { value: '320583', label: '昆山市' },
          { value: '320585', label: '太仓市' }
        ]
      }
    ]
  }
];

// 地址代码到文本的映射
export const codeToText = {};

// 递归构建 codeToText 映射
function buildCodeToText(data) {
  data.forEach(item => {
    codeToText[item.value] = item.label;
    if (item.children) {
      buildCodeToText(item.children);
    }
  });
}

// 初始化映射
buildCodeToText(regionData);

export default {
  regionData,
  codeToText
}; 