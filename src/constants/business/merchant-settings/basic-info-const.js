// 主体类型枚举
export const ENTITY_TYPE_ENUM = {
  INDIVIDUAL: { value: 'INDIVIDUAL', desc: '个体工商户' },
  ENTERPRISE: { value: 'ENTERPRISE', desc: '企业' },
  ORGANIZATION: { value: 'ORGANIZATION', desc: '事业单位/社会团体' }
};

// 主体类型选项
export const ENTITY_TYPE_OPTIONS = Object.keys(ENTITY_TYPE_ENUM).map(key => ({
  value: ENTITY_TYPE_ENUM[key].value,
  label: ENTITY_TYPE_ENUM[key].desc
}));

// 商户类型枚举
export const MERCHANT_TYPE_ENUM = {
  HUAXUE_SHIDAI: { value: 'HUAXUE_SHIDAI', desc: '画学时代' },
  ZHIYAN_QITA: { value: 'ZHIYAN_QITA', desc: '智彦其他' },
  EDUCATION: { value: 'EDUCATION', desc: '教育培训' },
  ECOMMERCE: { value: 'ECOMMERCE', desc: '电商零售' }
};

// 商户类型选项
export const MERCHANT_TYPE_OPTIONS = Object.keys(MERCHANT_TYPE_ENUM).map(key => ({
  value: MERCHANT_TYPE_ENUM[key].value,
  label: MERCHANT_TYPE_ENUM[key].desc
}));

// 经营类目枚举
export const BUSINESS_CATEGORY_ENUM = {
  OTHER_EDUCATION: { value: 'OTHER_EDUCATION', desc: '其他教育/培训' },
  VOCATIONAL_EDUCATION: { value: 'VOCATIONAL_EDUCATION', desc: '职业技能培训' },
  ART_EDUCATION: { value: 'ART_EDUCATION', desc: '艺术教育' },
  LANGUAGE_EDUCATION: { value: 'LANGUAGE_EDUCATION', desc: '语言教育' }
};

// 经营类目选项
export const BUSINESS_CATEGORY_OPTIONS = Object.keys(BUSINESS_CATEGORY_ENUM).map(key => ({
  value: BUSINESS_CATEGORY_ENUM[key].value,
  label: BUSINESS_CATEGORY_ENUM[key].desc
}));

// 图片类型枚举
export const IMAGE_TYPE_ENUM = {
  BUSINESS_LICENSE: 'BUSINESS_LICENSE',
  ID_CARD_FRONT: 'ID_CARD_FRONT',
  ID_CARD_BACK: 'ID_CARD_BACK',
  STOREFRONT: 'STOREFRONT',
  INTERIOR: 'INTERIOR',
  QUALIFICATIONS: 'QUALIFICATIONS'
};

// 图片类型描述
export const IMAGE_TYPE_DESC = {
  [IMAGE_TYPE_ENUM.BUSINESS_LICENSE]: '营业执照',
  [IMAGE_TYPE_ENUM.ID_CARD_FRONT]: '身份证人像面',
  [IMAGE_TYPE_ENUM.ID_CARD_BACK]: '身份证国徽面',
  [IMAGE_TYPE_ENUM.STOREFRONT]: '门头照',
  [IMAGE_TYPE_ENUM.INTERIOR]: '内景照',
  [IMAGE_TYPE_ENUM.QUALIFICATIONS]: '资质照片'
}; 