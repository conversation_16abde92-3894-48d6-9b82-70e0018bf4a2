/**
 * 逾期明细 常量
 *
 * @Author:    SmartAdmin
 * @Date:      2024-12-19 15:30:00
 * @Copyright  SmartAdmin
 */

/**
 * 存在客诉状态枚举
 */
export const COMPLAINT_STATUS_ENUM = {
  NO: { value: 0, desc: '否' },
  YES: { value: 1, desc: '是' }
};

/**
 * 代扣解除状态枚举
 */
export const WITHHOLD_RELEASE_ENUM = {
  NO: { value: 0, desc: '否' },
  YES: { value: 1, desc: '是' }
};

/**
 * 订单状态枚举
 */
export const ORDER_STATUS_ENUM = {
  WAIT_AUTHORIZ: { value: 'WAIT_AUTHORIZ', desc: '待授权' },
  EXECUTION_CONTRACT: { value: 'EXECUTION_CONTRACT', desc: '履约中' },
  ENDED: { value: 'ENDED', desc: '已结束' },
  TERMINATED: { value: 'TERMINATED', desc: '已解约' },
  PAUSED: { value: 'PAUSED', desc: '已暂停' },
  CANCELED: { value: 'CANCELED', desc: '已取消' }
};

/**
 * 收款方式枚举
 */
export const PAYMENT_METHOD_ENUM = {
  BANK_SEGMENT: { value: 'BANK_SEGMENT', desc: '账单付' },
  ZFB_ANXIN: { value: 'ZFB_ANXIN', desc: '安心付' }
};

/**
 * 逾期等级枚举
 */
export const OVERDUE_LEVEL_ENUM = {
  NORMAL: { value: 'NORMAL', desc: '正常', color: '#52c41a' },
  WARNING: { value: 'WARNING', desc: '预警', color: '#faad14' },
  RISK: { value: 'RISK', desc: '风险', color: '#ff4d4f' },
  SERIOUS: { value: 'SERIOUS', desc: '严重', color: '#722ed1' }
};

/**
 * 逾期处理状态枚举
 */
export const OVERDUE_PROCESS_STATUS_ENUM = {
  PENDING: { value: 'PENDING', desc: '待处理', color: '#1890ff' },
  PROCESSING: { value: 'PROCESSING', desc: '处理中', color: '#faad14' },
  PROCESSED: { value: 'PROCESSED', desc: '已处理', color: '#52c41a' },
  FAILED: { value: 'FAILED', desc: '处理失败', color: '#ff4d4f' }
};

/**
 * 扣款失败原因枚举
 */
export const PAYMENT_FAIL_REASON_ENUM = {
  INSUFFICIENT_BALANCE: { value: 'INSUFFICIENT_BALANCE', desc: '余额不足' },
  CARD_FROZEN: { value: 'CARD_FROZEN', desc: '银行卡冻结' },
  NETWORK_ERROR: { value: 'NETWORK_ERROR', desc: '网络异常' },
  SYSTEM_ERROR: { value: 'SYSTEM_ERROR', desc: '系统错误' },
  USER_CANCEL: { value: 'USER_CANCEL', desc: '用户取消' },
  OTHER: { value: 'OTHER', desc: '其他原因' }
};

// 导出所有枚举选项
export const COMPLAINT_STATUS_OPTIONS = Object.values(COMPLAINT_STATUS_ENUM);
export const WITHHOLD_RELEASE_OPTIONS = Object.values(WITHHOLD_RELEASE_ENUM);
export const ORDER_STATUS_OPTIONS = Object.values(ORDER_STATUS_ENUM);
export const PAYMENT_METHOD_OPTIONS = Object.values(PAYMENT_METHOD_ENUM);
export const OVERDUE_LEVEL_OPTIONS = Object.values(OVERDUE_LEVEL_ENUM);
export const OVERDUE_PROCESS_STATUS_OPTIONS = Object.values(OVERDUE_PROCESS_STATUS_ENUM);
export const PAYMENT_FAIL_REASON_OPTIONS = Object.values(PAYMENT_FAIL_REASON_ENUM); 