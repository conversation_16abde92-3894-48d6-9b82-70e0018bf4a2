// 收款方式枚举
export const PAYMENT_METHOD_ENUM = {
  BANK_CARD_INSTALLMENT: { value: 1, label: '银行卡分期' },
  ALIPAY_INSTALLMENT: { value: 2, label: '支付宝分期' },
  WECHAT_INSTALLMENT: { value: 3, label: '微信分期' },
  BANK_CARD_FULL: { value: 4, label: '银行卡全款' },
  ALIPAY_FULL: { value: 5, label: '支付宝全款' },
  WECHAT_FULL: { value: 6, label: '微信全款' }
}

// 扣款状态枚举
export const DEDUCTION_STATUS_ENUM = {
  WAIT_AUTHORIZ: { value: 'WAIT_AUTHORIZ', label: '待授权', color: 'default' },
  PENDING_DEDUCT: { value: 'PENDING_DEDUCT', label: '待扣款', color: 'blue' },
  PAID: { value: 'PAID', label: '已扣款', color: 'green' },
  DEDUCT_FAIL: { value: 'DEDUCT_FAIL', label: '扣款失败', color: 'red' },
  PAUSED: { value: 'PAUSED', label: '已暂停', color: 'warning' },
  TERMINATED: { value: 'TERMINATED', label: '已解约', color: 'red' },
  CANCELED: { value: 'CANCELED', label: '已取消', color: 'default' }
}

// 支付渠道枚举
export const PAYMENT_CHANNEL_ENUM = {
  WITHHOLDING: { value: 1, label: '代扣' },
  OFFLINE_COLLECTION: { value: 2, label: '线下收款' },
  BANK_CARD: { value: 3, label: '银行卡' },
  ALIPAY: { value: 4, label: '支付宝' },
  WECHAT: { value: 5, label: '微信' }
}

// 平台入账方式枚举
export const PLATFORM_ACCOUNT_METHOD_ENUM = {
  PLATFORM_ACCOUNT: { value: 1, label: '平台账户打费' },
  BANK_TRANSFER: { value: 2, label: '银行转账' },
  THIRD_PARTY: { value: 3, label: '第三方代收' }
}

// 表格列配置
export const FINANCE_DETAIL_TABLE_COLUMNS = [
  {
    title: '订单编号',
    dataIndex: 'orderNo',
    width: 180,
    resizable: true,
    fixed: 'left'
  },
  {
    title: '商品名称',
    dataIndex: 'productName',
    width: 150,
    resizable: true
  },
  {
    title: '用户手机号',
    dataIndex: 'userPhone',
    width: 130,
    resizable: true
  },
  {
    title: '用户姓名',
    dataIndex: 'userName',
    width: 100,
    resizable: true
  },
  {
    title: '邀请码',
    dataIndex: 'inviteCode',
    width: 120,
    resizable: true
  },
  {
    title: '流水号',
    dataIndex: 'transactionNo',
    width: 200,
    resizable: true
  },
  {
    title: '订单总金额',
    dataIndex: 'totalAmount',
    width: 120,
    resizable: true
  },
  {
    title: '期数',
    dataIndex: 'installmentNumber',
    width: 80,
    resizable: true
  },
  {
    title: '应收日期',
    dataIndex: 'dueDate',
    width: 120,
    resizable: true
  },
  {
    title: '应收金额',
    dataIndex: 'dueAmount',
    width: 100,
    resizable: true
  },
  {
    title: '收款方式',
    dataIndex: 'paymentMethod',
    width: 120,
    resizable: true
  },
  {
    title: '实收金额',
    dataIndex: 'actualAmount',
    width: 100,
    resizable: true
  },
  {
    title: '优惠金额',
    dataIndex: 'discountAmount',
    width: 100,
    resizable: true
  },
  {
    title: '收款时间',
    dataIndex: 'paymentTime',
    width: 150,
    resizable: true
  },
  {
    title: '下单时间',
    dataIndex: 'orderTime',
    width: 150,
    resizable: true
  },
  {
    title: '扣款状态',
    dataIndex: 'deductionStatus',
    width: 100,
    resizable: true
  },
  {
    title: '支付渠道',
    dataIndex: 'paymentChannel',
    width: 100,
    resizable: true
  },
  {
    title: '平台服务费',
    dataIndex: 'platformServiceFee',
    width: 120,
    resizable: true
  },
  {
    title: '平台入账方式',
    dataIndex: 'platformAccountMethod',
    width: 130,
    resizable: true
  },
  {
    title: '交易手续费（预估）',
    dataIndex: 'transactionFee',
    width: 150,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 200,
    resizable: true,
    ellipsis: true
  }
] 