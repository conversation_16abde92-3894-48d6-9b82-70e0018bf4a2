/**
 * 分段明细 常量
 *
 * @Author:    SmartAdmin
 * @Date:      2024-12-19 16:00:00
 * @Copyright  SmartAdmin
 */

/**
 * 扣款状态枚举
 */
export const DEDUCTION_STATUS_ENUM = {
  WAIT_AUTHORIZ: { value: 'WAIT_AUTHORIZ', desc: '待授权', color: '#d9d9d9' },
  PENDING_DEDUCT: { value: 'PENDING_DEDUCT', desc: '待扣款', color: '#1890ff' },
  PAID: { value: 'PAID', desc: '已扣款', color: '#52c41a' },
  DEDUCT_FAIL: { value: 'DEDUCT_FAIL', desc: '扣款失败', color: '#ff4d4f' },
  PAUSED: { value: 'PAUSED', desc: '已暂停', color: '#fa8c16' },
  TERMINATED: { value: 'TERMINATED', desc: '已解约', color: '#ff4d4f' },
  CANCELED: { value: 'CANCELED', desc: '已取消', color: '#d9d9d9' }
};

/**
 * 是否逾期枚举
 */
export const OVERDUE_STATUS_ENUM = {
  ALL: { value: '', desc: '全部' },
  YES: { value: true, desc: '是' },
  NO: { value: false, desc: '否' }
};

/**
 * 存在客诉状态枚举
 */
export const COMPLAINT_STATUS_ENUM = {
  ALL: { value: '', desc: '全部' },
  YES: { value: true, desc: '是' },
  NO: { value: false, desc: '否' }
};

/**
 * 代扣解除状态枚举
 */
export const WITHHOLD_RELEASE_ENUM = {
  ALL: { value: '', desc: '全部' },
  ACTIVE: { value: 'ACTIVE', desc: '正常代扣' },
  MANUAL_RELEASE: { value: 'MANUAL_RELEASE', desc: '手动解除' },
  AUTO_RELEASE: { value: 'AUTO_RELEASE', desc: '自动解除' },
  SYSTEM_RELEASE: { value: 'SYSTEM_RELEASE', desc: '系统解除' }
};

/**
 * 收款方式枚举
 */
export const PAYMENT_METHOD_ENUM = {
  BANK_CARD: { value: 'BANK_CARD', desc: '银行卡分期' },
  CREDIT_CARD: { value: 'CREDIT_CARD', desc: '信用卡分期' },
  ALIPAY_CREDIT: { value: 'ALIPAY_CREDIT', desc: '花呗分期' },
  WECHAT_CREDIT: { value: 'WECHAT_CREDIT', desc: '微信分付' },
  MANUAL_PAYMENT: { value: 'MANUAL_PAYMENT', desc: '人工收款' },
  OTHER: { value: 'OTHER', desc: '其他方式' }
};

/**
 * 扣款失败原因枚举
 */
export const PAYMENT_FAIL_REASON_ENUM = {
  INSUFFICIENT_BALANCE: { value: 'INSUFFICIENT_BALANCE', desc: '余额不足' },
  CARD_FROZEN: { value: 'CARD_FROZEN', desc: '银行卡冻结' },
  NETWORK_ERROR: { value: 'NETWORK_ERROR', desc: '网络异常' },
  SYSTEM_ERROR: { value: 'SYSTEM_ERROR', desc: '系统错误' },
  USER_CANCEL: { value: 'USER_CANCEL', desc: '用户取消' },
  LIMIT_EXCEEDED: { value: 'LIMIT_EXCEEDED', desc: '超过限额' },
  CARD_EXPIRED: { value: 'CARD_EXPIRED', desc: '银行卡过期' },
  OTHER: { value: 'OTHER', desc: '其他原因' }
};

/**
 * 分段类型枚举
 */
export const SEGMENT_TYPE_ENUM = {
  INSTALLMENT: { value: 'INSTALLMENT', desc: '分期付款' },
  DEFERRED: { value: 'DEFERRED', desc: '延期付款' },
  ADVANCE: { value: 'ADVANCE', desc: '预付款' },
  BALANCE: { value: 'BALANCE', desc: '尾款' }
};

/**
 * 订单状态枚举
 */
export const ORDER_STATUS_ENUM = {
  PENDING: { value: 'PENDING', desc: '待付款', color: '#1890ff' },
  PAYING: { value: 'PAYING', desc: '付款中', color: '#faad14' },
  PAID: { value: 'PAID', desc: '已付款', color: '#52c41a' },
  COMPLETED: { value: 'COMPLETED', desc: '已完成', color: '#52c41a' },
  CANCELLED: { value: 'CANCELLED', desc: '已取消', color: '#d9d9d9' },
  REFUNDED: { value: 'REFUNDED', desc: '已退款', color: '#722ed1' }
};

// 导出所有枚举选项
export const DEDUCTION_STATUS_OPTIONS = Object.values(DEDUCTION_STATUS_ENUM);
export const OVERDUE_STATUS_OPTIONS = Object.values(OVERDUE_STATUS_ENUM);
export const COMPLAINT_STATUS_OPTIONS = Object.values(COMPLAINT_STATUS_ENUM);
export const WITHHOLD_RELEASE_OPTIONS = Object.values(WITHHOLD_RELEASE_ENUM);
export const PAYMENT_METHOD_OPTIONS = Object.values(PAYMENT_METHOD_ENUM);
export const PAYMENT_FAIL_REASON_OPTIONS = Object.values(PAYMENT_FAIL_REASON_ENUM);
export const SEGMENT_TYPE_OPTIONS = Object.values(SEGMENT_TYPE_ENUM);
export const ORDER_STATUS_OPTIONS = Object.values(ORDER_STATUS_ENUM); 