/*
 * 订单模块常量
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2025-01-27 10:00:00
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

// 订单状态枚举
export const ORDER_STATUS_ENUM = {
  PENDING: {
    value: 1,
    desc: '待支付',
    color: 'processing',
    icon: 'clock-circle'
  },
  FULFILLING: {
    value: 2,
    desc: '履约中',
    color: 'warning',
    icon: 'sync'
  },
  COMPLETED: {
    value: 3,
    desc: '已完成',
    color: 'success',
    icon: 'check-circle'
  },
  CANCELLED: {
    value: 4,
    desc: '已取消',
    color: 'error',
    icon: 'close-circle'
  },
  REFUNDED: {
    value: 5,
    desc: '已退款',
    color: 'default',
    icon: 'undo'
  },
};

// 付款状态枚举
export const PAYMENT_STATUS_ENUM = {
  PENDING: {
    value: 1,
    desc: '待扣款',
    color: 'default',
    icon: 'clock-circle'
  },
  SUCCESS: {
    value: 2,
    desc: '扣款成功',
    color: 'success',
    icon: 'check-circle'
  },
  FAILED: {
    value: 3,
    desc: '扣款失败',
    color: 'error',
    icon: 'close-circle'
  },
  PARTIAL: {
    value: 4,
    desc: '部分扣款',
    color: 'warning',
    icon: 'exclamation-circle'
  },
};

// 扣款周期枚举
export const DEDUCTION_CYCLE_ENUM = {
  MONTHLY: {
    value: '按月',
    desc: '按月',
    days: 30
  },
  QUARTERLY: {
    value: '按季度',
    desc: '按季度',
    days: 90
  },
  HALF_YEARLY: {
    value: '按半年',
    desc: '按半年',
    days: 180
  },
  YEARLY: {
    value: '按年',
    desc: '按年',
    days: 365
  },
};

// 收款方式枚举
export const PAYMENT_METHOD_ENUM = {
  AUTO_DEDUCTION: {
    value: 'auto_deduction',
    desc: '自动扣款',
    color: 'blue',
    icon: 'credit-card'
  },
  MANUAL_PAYMENT: {
    value: 'manual_payment',
    desc: '手动付款',
    color: 'green',
    icon: 'user'
  },
  OFFLINE_PAYMENT: {
    value: 'offline_payment',
    desc: '线下收款',
    color: 'orange',
    icon: 'bank'
  },
};

// 收款渠道枚举
export const PAYMENT_CHANNEL_ENUM = {
  CASH: {
    value: 'cash',
    desc: '现金',
    color: 'green',
    icon: 'dollar'
  },
  BANK_TRANSFER: {
    value: 'bank_transfer',
    desc: '银行转账',
    color: 'blue',
    icon: 'bank'
  },
  ALIPAY: {
    value: 'alipay',
    desc: '支付宝',
    color: 'cyan',
    icon: 'alipay'
  },
  WECHAT: {
    value: 'wechat',
    desc: '微信',
    color: 'green',
    icon: 'wechat'
  },
  OTHER: {
    value: 'other',
    desc: '其他',
    color: 'default',
    icon: 'question'
  },
};

// 业务常量
export const ORDER_CONFIG = {
  MAX_INSTALLMENTS: 36,           // 最大分期数
  MIN_INSTALLMENTS: 1,            // 最小分期数
  MAX_AMOUNT: 1000000,           // 最大订单金额
  MIN_AMOUNT: 0.01,              // 最小订单金额
  DEFAULT_INSTALLMENTS: 12,       // 默认分期数
  PAYMENT_VOUCHER_MAX_SIZE: 10,   // 收款凭证最大文件大小(MB)
};

// 权限常量
export const ORDER_PERMISSION_CODES = {
  QUERY: 'segmentOrder:query',
  ADD: 'segmentOrder:add',
  UPDATE: 'segmentOrder:update',
  DELETE: 'segmentOrder:delete',
  BATCH_DELETE: 'segmentOrder:batchDelete',
  EXPORT: 'segmentOrder:export',
  DETAIL: 'segmentOrder:detail',
  OFFLINE_PAYMENT: 'segmentOrder:offlinePayment',
  UPDATE_STATUS: 'segmentOrder:updateStatus',
  EXECUTE_DEDUCTION: 'segmentOrder:executeDeduction',
  DOWNLOAD_CONTRACT: 'segmentOrder:downloadContract',
  TERMINATE: 'segmentOrder:terminate',
};

export default {
  ORDER_STATUS_ENUM,
  PAYMENT_STATUS_ENUM,
  DEDUCTION_CYCLE_ENUM,
  PAYMENT_METHOD_ENUM,
  PAYMENT_CHANNEL_ENUM,
  ORDER_CONFIG,
  ORDER_PERMISSION_CODES,
}; 