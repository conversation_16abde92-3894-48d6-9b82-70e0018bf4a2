/*
 * 分段订单相关常量
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2024-01-01 00:00:00
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */

// 订单状态枚举
export const ORDER_STATUS_ENUM = {
  PENDING: {
    value: 1,
    desc: '待处理',
  },
  CONFIRMED: {
    value: 2,
    desc: '已确认',
  },
  SHIPPED: {
    value: 3,
    desc: '已发货',
  },
  DELIVERED: {
    value: 4,
    desc: '已送达',
  },
  COMPLETED: {
    value: 5,
    desc: '已完成',
  },
  CANCELLED: {
    value: 6,
    desc: '已取消',
  },
  PERFORMING: {
    value: 7,
    desc: '履约中',
  },
};

// 发货状态枚举
export const SHIPPING_STATUS_ENUM = {
  NOT_SHIPPED: {
    value: 0,
    desc: '未发货',
  },
  SHIPPED: {
    value: 1,
    desc: '已发货',
  },
  DELIVERED: {
    value: 2,
    desc: '已送达',
  },
};

// 收货状态枚举
export const RECEIVE_STATUS_ENUM = {
  NOT_RECEIVED: {
    value: 0,
    desc: '未收货',
  },
  RECEIVED: {
    value: 1,
    desc: '已收货',
  },
};

// 订单类型枚举
export const ORDER_TYPE_ENUM = {
  SEGMENT: {
    value: 1,
    desc: '分段订单',
  },
  NORMAL: {
    value: 2,
    desc: '普通订单',
  },
};

export default {
  ORDER_STATUS_ENUM,
  SHIPPING_STATUS_ENUM,
  RECEIVE_STATUS_ENUM,
  ORDER_TYPE_ENUM,
}; 