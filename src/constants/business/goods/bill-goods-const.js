/**
 * 账单付商品 常量
 *
 * @Author:    SmartAdmin
 * @Date:      2024-12-19 16:30:00
 * @Copyright  SmartAdmin
 */

/**
 * 上架状态枚举
 */
export const SHELVES_STATUS_ENUM = {
  ALL: { value: '', desc: '全部' },
  ONLINE: { value: true, desc: '上架', color: '#52c41a' },
  OFFLINE: { value: false, desc: '下架', color: '#ff4d4f' }
};

/**
 * 收款方式枚举
 */
export const PAYMENT_RECEIVE_METHOD_ENUM = {
  ZFB_ANXIN: { value: 'ZFB_ANXIN', desc: '安心付' },
  BANK_SEGMENT: { value: 'BANK_SEGMENT', desc: '银行卡' },
  // 保留原有的枚举值以兼容
  ALIPAY_ANXIN: { value: 'ALIPAY_ANXIN', desc: '支付宝安心付' },
  WECHAT_PAYMENT: { value: 'WECHAT_PAYMENT', desc: '微信分付' },
  BANK_CARD: { value: 'BANK_CARD', desc: '银行卡代扣' },
  CREDIT_CARD: { value: 'CREDIT_CARD', desc: '信用卡分期' },
  UNION_PAY: { value: 'UNION_PAY', desc: '银联分期' },
  MANUAL_PAYMENT: { value: 'MANUAL_PAYMENT', desc: '人工收款' }
};

/**
 * 付款方式枚举
 */
export const PAYMENT_METHOD_ENUM = {
  XY_SEGMENT: { value: 'XY_SEGMENT', desc: '分段（X段+Y段）' },
  EQUAL_SEGMENT: { value: 'EQUAL_SEGMENT', desc: '等额分段' },
};

/**
 * 扣款周期枚举
 */
export const DEDUCTION_CYCLE_ENUM = {
  DEDUCT_BY_MONTH: { value: 'DEDUCT_BY_MONTH', desc: '按月' },
  DEDUCT_BY_WEEK: { value: 'DEDUCT_BY_WEEK', desc: '按周' },
  DEDUCT_BY_DAY: { value: 'DEDUCT_BY_DAY', desc: '按日' },
  DEDUCT_BY_QUARTER: { value: 'DEDUCT_BY_QUARTER', desc: '按季度' },
  DEDUCT_BY_YEAR: { value: 'DEDUCT_BY_YEAR', desc: '按年' },
};

/**
 * 还款日类型枚举
 */
export const PAYMENT_DAY_TYPE_ENUM = {
  ORDER_DAY_DELAY: { value: 'ORDER_DAY_DELAY', desc: '下单日顺延' },
  FIX_PAYMENT_DAY: { value: 'FIX_PAYMENT_DAY', desc: '固定每月' },
};

/**
 * 可选期数规则枚举
 */
export const OPTIONAL_PERIOD_TYPE_ENUM = {
  FIX_TOTAL_PERIOD: { value: 'FIX_TOTAL_PERIOD', desc: '固定总期数' },
};

/**
 * 商品状态枚举
 */
export const GOODS_STATUS_ENUM = {
  DRAFT: { value: 'DRAFT', desc: '草稿', color: '#d9d9d9' },
  PENDING: { value: 'PENDING', desc: '待审核', color: '#faad14' },
  APPROVED: { value: 'APPROVED', desc: '已审核', color: '#52c41a' },
  REJECTED: { value: 'REJECTED', desc: '已拒绝', color: '#ff4d4f' },
  DISABLED: { value: 'DISABLED', desc: '已禁用', color: '#d9d9d9' }
};

/**
 * 分期类型枚举
 */
export const INSTALLMENT_TYPE_ENUM = {
  FRONT_BACK: { value: 'FRONT_BACK', desc: '前后段式' },
  EQUAL: { value: 'EQUAL', desc: '等额分期' },
  CUSTOM: { value: 'CUSTOM', desc: '自定义分期' }
};

/**
 * 商品分类枚举
 */
export const GOODS_CATEGORY_ENUM = {
  COURSE: { value: 'COURSE', desc: '课程类' },
  SERVICE: { value: 'SERVICE', desc: '服务类' },
  PRODUCT: { value: 'PRODUCT', desc: '商品类' },
  VIRTUAL: { value: 'VIRTUAL', desc: '虚拟商品' }
};

// 导出所有枚举选项
export const SHELVES_STATUS_OPTIONS = Object.values(SHELVES_STATUS_ENUM);
export const PAYMENT_RECEIVE_METHOD_OPTIONS = Object.values(PAYMENT_RECEIVE_METHOD_ENUM);
export const PAYMENT_METHOD_OPTIONS = Object.values(PAYMENT_METHOD_ENUM);
export const DEDUCTION_CYCLE_OPTIONS = Object.values(DEDUCTION_CYCLE_ENUM);
export const PAYMENT_DAY_TYPE_OPTIONS = Object.values(PAYMENT_DAY_TYPE_ENUM);
export const OPTIONAL_PERIOD_TYPE_OPTIONS = Object.values(OPTIONAL_PERIOD_TYPE_ENUM);
export const GOODS_STATUS_OPTIONS = Object.values(GOODS_STATUS_ENUM);
export const INSTALLMENT_TYPE_OPTIONS = Object.values(INSTALLMENT_TYPE_ENUM);
export const GOODS_CATEGORY_OPTIONS = Object.values(GOODS_CATEGORY_ENUM); 