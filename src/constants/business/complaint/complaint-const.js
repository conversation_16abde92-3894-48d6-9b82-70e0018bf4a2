// 投诉状态枚举
export const COMPLAINT_STATUS_ENUM = {
  PENDING: { value: 1, label: '待处理', color: 'orange' },
  PROCESSING: { value: 2, label: '处理中', color: 'blue' },
  COMPLETED: { value: 3, label: '已完成', color: 'green' },
  REJECTED: { value: 4, label: '已拒绝', color: 'red' }
}

// 投诉代扣解约状态枚举
export const COMPLAINT_AGREEMENT_STATUS_ENUM = {
  PENDING: { value: 1, label: '待解约' },
  COMPLETED: { value: 2, label: '已解约' }
}

// 当前代扣解约状态枚举
export const CURRENT_AGREEMENT_STATUS_ENUM = {
  PENDING: { value: 1, label: '待解约' },
  COMPLETED: { value: 2, label: '已解约' }
}

// 处理类型枚举
export const COMPLAINT_HANDLE_TYPE_ENUM = {
  REFUND: { value: 1, label: '退款' },
  COMPENSATION: { value: 2, label: '赔偿' },
  AGREEMENT_CANCEL: { value: 3, label: '代扣解约' },
  SERVICE_IMPROVEMENT: { value: 4, label: '服务改进' },
  OTHER: { value: 5, label: '其他' }
}

// 处理结果类型枚举
export const COMPLAINT_SOLVE_RESULT_TYPE_ENUM = {
  CONTINUE_CONTRACT: { value: 1, label: '和用户协商一致，继续履约' },
  TERMINATE_CONTRACT: { value: 2, label: '和用户协商一致，解约订单' },
  TERMINATION_PLAN: { value: 3, label: '和用户协商一致，达成解约方案' },
  PLAN_NEGOTIATING: { value: 4, label: '解约方案协商中' },
  SERVICE_FEE_REQUIRED: { value: 5, label: '需交已产生的服务费（需上传服务记录）' },
  SERVICE_COMPLETED: { value: 6, label: '服务已经完毕，无法解约（需上传服务记录）' },
  USER_UNREACHABLE: { value: 7, label: '联系不上用户' }
}

// 表格列配置
export const COMPLAINT_TABLE_COLUMNS = [
  {
    title: '订单信息',
    dataIndex: 'orderInfo',
    width: 200,
    resizable: true
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 100,
    resizable: true
  },
  {
    title: '用户信息',
    dataIndex: 'userInfo',
    width: 150,
    resizable: true
  },
  {
    title: '邀请码',
    dataIndex: 'inviteCode',
    width: 100,
    resizable: true
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 120,
    resizable: true
  },
  {
    title: '投诉内容',
    dataIndex: 'complaintContent',
    width: 200,
    resizable: true,
    ellipsis: true
  },
  {
    title: '是否满期',
    dataIndex: 'isExpired',
    width: 100,
    resizable: true
  },
  {
    title: '过期期数',
    dataIndex: 'expiredPeriods',
    width: 100,
    resizable: true
  },
  {
    title: '最高逾期天数',
    dataIndex: 'maxOverdueDays',
    width: 120,
    resizable: true
  },
  {
    title: '最近应扣日期',
    dataIndex: 'latestDeductionDate',
    width: 150,
    resizable: true
  },
  {
    title: '处理状态',
    dataIndex: 'handleStatus',
    width: 100,
    resizable: true
  }
] 