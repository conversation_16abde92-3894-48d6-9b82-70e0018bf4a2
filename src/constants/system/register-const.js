// 资质文件类型枚举
export const QUALIFICATION_TYPE_ENUM = {
  EDUCATION_PERMIT: { value: 'EDUCATION_PERMIT', desc: '办学许可证' },
  TRAINING_PERMIT: { value: 'TRAINING_PERMIT', desc: '培训机构许可证' },
  FOOD_PERMIT: { value: 'FOOD_PERMIT', desc: '食品经营许可证' },
  MEDICAL_PERMIT: { value: 'MEDICAL_PERMIT', desc: '医疗机构执业许可证' },
  OTHER: { value: 'OTHER', desc: '其他资质证明' }
};

// 资质文件类型选项
export const QUALIFICATION_TYPE_OPTIONS = Object.keys(QUALIFICATION_TYPE_ENUM).map(key => ({
  value: QUALIFICATION_TYPE_ENUM[key].value,
  label: QUALIFICATION_TYPE_ENUM[key].desc
}));

// 图片上传类型
export const UPLOAD_IMAGE_TYPE = {
  BUSINESS_LICENSE: 'BUSINESS_LICENSE',
  ID_CARD_FRONT: 'ID_CARD_FRONT', 
  ID_CARD_BACK: 'ID_CARD_BACK',
  STOREFRONT: 'STOREFRONT',
  INTERIOR: 'INTERIOR',
  QUALIFICATION: 'QUALIFICATION'
};

// 表单步骤
export const FORM_STEPS = [
  { key: 'basic', title: '主体信息' },
  { key: 'legal', title: '法人信息' },
  { key: 'business', title: '经营信息' },
  { key: 'admin', title: '管理员信息' }
]; 