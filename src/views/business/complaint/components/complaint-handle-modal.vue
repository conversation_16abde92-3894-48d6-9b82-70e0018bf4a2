<!--
  * 投诉处理弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-01-01
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    title="处理投诉"
    :width="800"
    centered
    :mask-closable="false"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
  >
    <a-spin :spinning="confirmLoading">
      <div style="padding: 20px 0;">
        <!-- 投诉时间 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">投诉时间</div>
          <div>{{ complaintData?.createTime || '-' }}</div>
        </div>
        
        <!-- 投诉内容 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">投诉内容</div>
          <div style="line-height: 1.6;">
            {{ complaintData?.complaintContent || '-' }}
          </div>
        </div>
        
        <!-- 投诉上传图片 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">投诉上传图片</div>
          <div v-if="complaintImageList && complaintImageList.length > 0" style="display: flex; gap: 12px;">
            <a-image-preview-group>
              <a-image
                v-for="(imageUrl, index) in complaintImageList"
                :key="index"
                :width="120"
                :height="120"
                :src="imageUrl"
                :alt="`投诉图片${index + 1}`"
                style="object-fit: cover; border-radius: 4px; border: 1px solid #d9d9d9;"
              />
            </a-image-preview-group>
          </div>
          <div v-else style="color: #999;">暂无图片</div>
        </div>

        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="vertical"
        >
          <!-- 处理反馈 -->
          <div style="margin-bottom: 24px;">
            <div style="color: #666; margin-bottom: 8px;"><span style="color: red;">*</span> 处理反馈</div>
            <a-form-item name="solveResult" style="margin-bottom: 0;">
              <a-textarea 
                v-model:value="formData.solveResult" 
                placeholder="请输入处理反馈"
                :auto-size="{ minRows: 3, maxRows: 4 }"
                :maxlength="100"
                show-count
              />
            </a-form-item>
          </div>

          <!-- 处理结论类型 -->
          <div style="margin-bottom: 24px;">
            <div style="color: #666; margin-bottom: 8px;"><span style="color: red;">*</span> 处理结论类型</div>
            <a-form-item name="solveResultType" style="margin-bottom: 0;">
              <a-select 
                v-model:value="formData.solveResultType" 
                placeholder="请选择"
                style="width: 100%"
              >
                <a-select-option 
                  v-for="item in Object.values(COMPLAINT_SOLVE_RESULT_TYPE_ENUM)" 
                  :key="item.value" 
                  :value="item.value"
                >
                  {{ item.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </div>

          <!-- 上传处理图片 -->
          <div style="margin-bottom: 24px;">
            <div style="color: #666; margin-bottom: 8px;">上传处理图片</div>
            <a-form-item name="solvePictures" style="margin-bottom: 0;">
              <div class="upload-container">
                <!-- 已上传的图片 -->
                <a-image-preview-group v-if="uploadedImages.length > 0">
                  <div v-for="(image, index) in uploadedImages" :key="index" class="uploaded-image-item">
                    <a-image 
                      :width="104" 
                      :height="104" 
                      :src="image.url" 
                      :alt="`处理图片${index + 1}`"
                      style="object-fit: cover; border-radius: 6px;"
                    />
                    <div class="image-remove" @click="removeImage(index)" :style="{ pointerEvents: confirmLoading ? 'none' : 'auto' }">
                        <CloseOutlined />
                    </div>
                  </div>
                </a-image-preview-group>
                <!-- 上传按钮 -->
                <div 
                  v-if="uploadedImages.length < 6" 
                  class="upload-btn" 
                  :class="{ disabled: uploading || confirmLoading }" 
                  @click="triggerFileInput"
                >
                  <div class="upload-icon">
                    <PlusOutlined v-if="!uploading" />
                    <LoadingOutlined v-if="uploading" class="spinning" />
                  </div>
                  <div class="upload-text">{{ uploading ? '上传中...' : '点击上传' }}</div>
                </div>
                <!-- 隐藏的文件输入框 -->
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/*"
                  multiple
                  style="display: none"
                  @change="handleFileChange"
                  :disabled="uploading || confirmLoading"
                />
              </div>
              <div class="field-tip">支持.jpg .png格式，最多上传6张图片</div>
            </a-form-item>
          </div>
        </a-form>
      </div>
    </a-spin>

    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk" :loading="confirmLoading">确定</a-button>
    </template>


  </a-modal>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { message } from 'ant-design-vue';
import { CloseOutlined, PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { complaintApi } from '/@/api/business/complaint/complaint-api';
import { fileApi } from '/@/api/support/file-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { COMPLAINT_SOLVE_RESULT_TYPE_ENUM } from '/@/constants/business/complaint/complaint-const';

const emit = defineEmits(['reloadList']);

// 弹窗状态
const modalVisible = ref(false);
const confirmLoading = ref(false);
const formRef = ref();

// 文件输入框引用
const fileInput = ref();



// 图片上传相关
const uploadedImages = ref([]);
const uploading = ref(false);

// 数据状态
const complaintData = ref(null);

// 投诉图片列表 - 将API返回的逗号分隔字符串转换为数组
const complaintImageList = computed(() => {
  if (!complaintData.value?.pictures) return [];
  return complaintData.value.pictures.split(',').filter(url => url.trim());
});

// 表单数据
const formDataState = {
  solveResult: '', // 处理反馈
  solveResultType: undefined, // 处理结果类型
  solvePictures: '' // 处理图片，多个以逗号分隔
};

const formData = reactive({ ...formDataState });

// 表单验证规则
const rules = {
  solveResult: [
    { required: true, message: '请输入处理反馈', trigger: 'blur' },
    { min: 1, max: 100, message: '处理反馈长度为1-100个字符', trigger: 'blur' }
  ],
  solveResultType: [
    { required: true, message: '请选择处理结论类型', trigger: 'change' }
  ]
};

// 显示投诉处理弹窗
function showModal(record) {
  complaintData.value = record;
  resetForm();
  modalVisible.value = true;
}

// 重置表单
function resetForm() {
  Object.assign(formData, { ...formDataState });
  uploadedImages.value = [];
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 确认处理
async function handleOk() {
  try {
    await formRef.value.validate();
    confirmLoading.value = true;
    
    const requestData = {
      complaintId: complaintData.value.complaintId,
      solveResult: formData.solveResult,
      solveResultType: formData.solveResultType,
      solvePictures: formData.solvePictures
    };
    
    // 调用真实API
    await complaintApi.handleComplaint(requestData);
    
    message.success('投诉处理成功');
    modalVisible.value = false;
    emit('reloadList');
    
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    smartSentry.captureError(error);
    message.error('投诉处理失败');
  } finally {
    confirmLoading.value = false;
  }
}

// 取消
function handleCancel() {
  modalVisible.value = false;
  resetForm();
}

// 触发文件选择
function triggerFileInput() {
  if (!uploading.value && !confirmLoading.value) {
  fileInput.value?.click();
  }
}

// 处理文件选择
async function handleFileChange(event) {
  const files = event.target.files;
  if (!files || files.length === 0) return;
  
  // 检查是否超过最大数量限制
  const remainingSlots = 6 - uploadedImages.value.length;
  const filesToProcess = Array.from(files).slice(0, remainingSlots);
  
  uploading.value = true;
  
  try {
    for (const file of filesToProcess) {
      if (!validateFile(file)) continue;
      
      // 创建预览图片
      const imageData = await createImageData(file);
      
      // 上传文件
      const url = await uploadFile(file);
      
      // 添加到已上传列表
      uploadedImages.value.push({
        url: url,
        name: file.name,
        preview: imageData.url
      });
    }
    
    // 更新表单数据
    updateFormPicturesData();
    message.success('图片上传成功');
    
  } catch (error) {
    console.error('图片上传失败:', error);
    message.error('图片上传失败');
  } finally {
    uploading.value = false;
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  }
}

// 验证文件
function validateFile(file) {
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return false;
    }
    
    // 验证文件大小（限制为5MB）
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
    message.error('文件大小不能超过5MB');
    return false;
  }
  
  return true;
    }
    
// 创建图片数据
function createImageData(file) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      resolve({
        url: e.target.result,
        name: file.name,
        size: file.size,
        file: file
      });
    };
    reader.readAsDataURL(file);
  });
}

// 上传文件
async function uploadFile(file, folder = 1) {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await fileApi.uploadFile(formData, folder);
    return response.data.fileUrl || '';
  } catch (error) {
    console.error('文件上传失败:', error);
    throw new Error('文件上传失败');
  }
}

// 删除图片
function removeImage(index) {
  uploadedImages.value.splice(index, 1);
  updateFormPicturesData();
  message.success('图片删除成功');
}

// 更新表单图片数据
function updateFormPicturesData() {
  // 将图片URL用逗号分隔拼接成字符串
  formData.solvePictures = uploadedImages.value.map(img => img.url).join(',');
}



// 暴露方法
defineExpose({
  showModal
});
</script>

<style scoped>
.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.uploaded-image-item {
  position: relative;
  display: inline-block;
}

.image-remove {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
}

.image-remove:hover {
  background: rgba(0, 0, 0, 0.7);
}

.upload-btn {
  width: 104px;
  height: 104px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
}

.upload-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.upload-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.upload-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.field-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}
</style> 