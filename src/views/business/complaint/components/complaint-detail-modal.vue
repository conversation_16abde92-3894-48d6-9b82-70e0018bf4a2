<!--
  * 投诉详情弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-01-01
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    title="投诉详情"
    :width="800"
    centered
    @cancel="handleCancel"
    :footer="null"
  >
    <a-spin :spinning="loading">
      <div v-if="detailData" style="padding: 20px 0;">
        <!-- 提交时间 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">提交时间</div>
          <div>{{ detailData.createTime || '-' }}</div>
        </div>

        <!-- 投诉内容 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">投诉内容</div>
          <div style="line-height: 1.6;">
            {{ detailData.complaintContent || '-' }}
          </div>
        </div>

        <!-- 投诉上传图片 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">投诉上传图片</div>
          <div v-if="complaintImageList && complaintImageList.length > 0" style="display: flex; gap: 12px;">
            <a-image-preview-group>
              <a-image
                v-for="(imageUrl, index) in complaintImageList"
                :key="index"
                :width="120"
                :height="120"
                :src="imageUrl"
                :alt="`投诉图片${index + 1}`"
                style="object-fit: cover; border-radius: 4px; border: 1px solid #d9d9d9;"
              />
            </a-image-preview-group>
          </div>
          <div v-else style="color: #999;">暂无图片</div>
        </div>

        <!-- 处理反馈 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">处理反馈</div>
          <div>{{ detailData.solveResult || '-' }}</div>
        </div>

        <!-- 处理结论类型 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">处理结论类型</div>
          <div>{{ getSolveResultTypeText(detailData.solveResultType) || '-' }}</div>
        </div>

        <!-- 上传处理图片 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">上传处理图片</div>
          <div v-if="solveImageList && solveImageList.length > 0" style="display: flex; gap: 12px;">
            <a-image-preview-group>
              <a-image
                v-for="(imageUrl, index) in solveImageList"
                :key="index"
                :width="120"
                :height="120"
                :src="imageUrl"
                :alt="`处理图片${index + 1}`"
                style="object-fit: cover; border-radius: 4px; border: 1px solid #d9d9d9;"
              />
            </a-image-preview-group>
          </div>
          <div v-else style="color: #999;">暂无图片</div>
        </div>

        <!-- 处理时间 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">处理时间</div>
          <div>{{ detailData.solveTime || '-' }}</div>
        </div>
      </div>
    </a-spin>

    <template #footer>
      <a-button @click="handleCancel">取消</a-button>
      <a-button type="primary" @click="handleOk">确定</a-button>
    </template>


  </a-modal>
</template>

<script setup>
import { ref, computed } from 'vue';
import { complaintApi } from '/@/api/business/complaint/complaint-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { COMPLAINT_SOLVE_RESULT_TYPE_ENUM } from '/@/constants/business/complaint/complaint-const';

// 弹窗状态
const modalVisible = ref(false);
const loading = ref(false);



// 详情数据
const detailData = ref(null);

// 投诉图片列表 - 将API返回的逗号分隔字符串转换为数组
const complaintImageList = computed(() => {
  if (!detailData.value?.pictures) return [];
  return detailData.value.pictures.split(',').filter(url => url.trim());
});

// 处理结果图片列表 - 将API返回的逗号分隔字符串转换为数组
const solveImageList = computed(() => {
  if (!detailData.value?.solvePictures) return [];
  return detailData.value.solvePictures.split(',').filter(url => url.trim());
});

// 显示详情弹窗
async function showModal(record) {
  modalVisible.value = true;
  loading.value = true;
  
  try {
    const result = await complaintApi.getComplaintDetail(record.complaintId);
    detailData.value = result.data;
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    loading.value = false;
  }
}

// 关闭弹窗
function handleCancel() {
  modalVisible.value = false;
  detailData.value = null;
}

// 确定按钮
function handleOk() {
  modalVisible.value = false;
  detailData.value = null;
}

// 获取处理结果类型文本 - API字段名 solveResultType
function getSolveResultTypeText(type) {
  const enumItem = Object.values(COMPLAINT_SOLVE_RESULT_TYPE_ENUM).find(item => item.value === type);
  return enumItem ? enumItem.label : '';
}



// 暴露方法
defineExpose({
  showModal
});
</script>

<style scoped>
</style> 