<!--
  * 投诉管理列表
  *
  * @Author:    SmartAdmin
  * @Date:      2024-01-01
  * @Copyright  SmartAdmin
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="订单号" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.orderNo" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item label="用户姓名" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.customerName" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item label="用户手机号" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.customerPhone" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item label="处理状态" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.complaintStatus" 
          placeholder="全部" 
          allowClear
        >
          <a-select-option :value="undefined">全部</a-select-option>
          <a-select-option value="WAIT_DEAL">待处理</a-select-option>
          <a-select-option value="DEAL_COMPLETED">已处理</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="订单状态" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.orderStatus" 
          placeholder="全部" 
          allowClear
        >
          <a-select-option :value="undefined">全部</a-select-option>
          <a-select-option value="WAIT_AUTHORIZ">待授权</a-select-option>
          <a-select-option value="EXECUTION_CONTRACT">履约中</a-select-option>
          <a-select-option value="ENDED">已结束</a-select-option>
          <a-select-option value="TERMINATED">已解约</a-select-option>
          <a-select-option value="CANCELED">已取消</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="收款方式" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.receivePaymentType" 
          placeholder="全部" 
          allowClear
        >
          <a-select-option :value="undefined">全部</a-select-option>
          <a-select-option value="BANK_SEGMENT">账单付</a-select-option>
          <a-select-option value="ZFB_ANXIN">安心付</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="是否逾期" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.overdueFlag" 
          placeholder="全部" 
          allowClear
        >
          <a-select-option :value="undefined">全部</a-select-option>
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="逾期天数" class="smart-query-form-item">
        <a-input-number 
          style="width: 120px" 
          v-model:value="queryForm.overdueDaysFrom" 
          placeholder="请输入"
          :min="0"
        />
        <span style="margin: 0 10px;">至</span>
        <a-input-number 
          style="width: 120px" 
          v-model:value="queryForm.overdueDaysTo" 
          placeholder="请输入"
          :min="0"
        />
      </a-form-item>

      <a-form-item label="订单代扣解除" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.cancelWithholding" 
          placeholder="全部" 
          allowClear
        >
          <a-select-option :value="undefined">全部</a-select-option>
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="投诉日期" class="smart-query-form-item">
        <a-range-picker 
          style="width: 240px" 
          v-model:value="queryForm.complaintDateRange" 
          format="YYYY/MM/DD"
          :placeholder="['开始日期', '结束日期']"
        />
      </a-form-item>

      <a-form-item label="最近应扣日期" class="smart-query-form-item">
        <a-range-picker 
          style="width: 240px" 
          v-model:value="queryForm.latestDeductionDateRange" 
          format="YYYY/MM/DD"
          :placeholder="['开始日期', '结束日期']"
        />
      </a-form-item>

      <a-form-item label="处理结论类型" class="smart-query-form-item">
        <a-select 
          style="width: 200px" 
          v-model:value="queryForm.solveResultType" 
          placeholder="请选择" 
          allowClear
        >
          <a-select-option :value="undefined">全部</a-select-option>
          <a-select-option 
            v-for="item in Object.values(COMPLAINT_SOLVE_RESULT_TYPE_ENUM)" 
            :key="item.value" 
            :value="item.value"
          >
            {{ item.label }}
          </a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="投诉次数" class="smart-query-form-item">
        <a-input-number 
          style="width: 120px" 
          v-model:value="queryForm.complaintCountFrom" 
          placeholder="请输入"
          :min="0"
        />
        <span style="margin: 0 10px;">至</span>
        <a-input-number 
          style="width: 120px" 
          v-model:value="queryForm.complaintCountTo" 
          placeholder="请输入"
          :min="0"
        />
      </a-form-item>

      <a-form-item label="门店名称" class="smart-query-form-item">
        <StoreMultiSelect 
          v-model="queryForm.storeName" 
          width="200px"
          placeholder="请选择门店"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button 
          v-privilege="'business:complaint-list:export'"
          @click="onExportComplaints">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator 
          v-model="columns" 
          :tableId="TABLE_ID_CONST.BUSINESS.COMPLAINT.LIST" 
          :refresh="queryData" 
        />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="complaintId"
      :scroll="{ x: 2450, y: 2000 }"
      bordered
      :pagination="false"
      :loading="tableLoading"
      @resizeColumn="handleResizeColumn"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'orderInfo'">
          <div class="order-info-cell">
            <div>{{ record.productName }}</div>
            <div class="text-gray">订单编号：
              <a @click="goToSegmentOrderDetail(record.orderId)" class="order-link">
                {{ record.orderNo }}
              </a>
            </div>
            <div class="text-gray">收款方式：{{ getReceivePaymentTypeText(record.receivePaymentType) }}</div>
            <div class="text-gray">订单金额：{{ record.orderAmount }}</div>
            <div class="text-gray">创建人：{{ record.actualName || '-' }}</div>
            <div class="text-gray">投诉时间：{{ record.createTime }}</div>
          </div>
        </template>

        <template v-if="column.dataIndex === 'orderStatus'">
          <a-tag :color="getOrderStatusColor(record.orderStatus)">
            {{ getOrderStatusText(record.orderStatus) }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'userInfo'">
          <div class="user-info-cell">
            <div>{{ record.customerName }}</div>
            <div class="text-gray">{{ maskPhoneNumber(record.customerPhone) }}</div>
          </div>
        </template>

        <template v-if="column.dataIndex === 'inviteCode'">
          <div class="invitation-info-cell">
            <div>邀请码：{{ record.invitationCode || '-' }}</div>
            <div>员工：{{ record.employeeName || '-' }}</div>
            <div>部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>

        <template v-if="column.dataIndex === 'contactPerson'">
          <div>
            <div>{{ record.contactName }}</div>
            <div>{{ maskPhoneNumber(record.customerPhone) }}</div>
          </div>
        </template>

        <template v-if="column.dataIndex === 'complaintContent'">
          <div class="complaint-content" :title="record.complaintContent">
            {{ record.complaintContent && record.complaintContent.length > 50 
                ? record.complaintContent.substring(0, 50) + '...' 
                : record.complaintContent || '-' }}
          </div>
        </template>

        <template v-if="column.dataIndex === 'isExpired'">
          <a-tag :color="record.overdueFlag ? 'red' : 'green'">
            {{ record.overdueFlag ? '是' : '否' }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'orderOverdueCount'">
          {{ record.orderOverdueCount }}
        </template>

        <template v-if="column.dataIndex === 'overdueMaxDays'">
          {{ record.overdueMaxDays }}
        </template>

        <template v-if="column.dataIndex === 'latestDeductDate'">
          {{ record.latestDeductDate }}
        </template>

        <template v-if="column.dataIndex === 'handleStatus'">
          <a-tag :color="getComplaintStatusColor(record.complaintStatus)">
            {{ getComplaintStatusText(record.complaintStatus) }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'processFeedback'">
          <div class="process-feedback-content">
            {{ record.solveResult || '-' }}
          </div>
        </template>

        <template v-if="column.dataIndex === 'processTime'">
          {{ record.solveTime || '-' }}
        </template>

        <template v-if="column.dataIndex === 'processFeedbackType'">
          {{ getSolveResultTypeText(record.solveResultType) }}
        </template>

        <template v-if="column.dataIndex === 'bindDeductionInfo'">
          <div class="bind-deduction-info">
            <div>当前代扣工具：{{ getDeductMethodText(record.deductMethod) }}</div>
            <div>代扣是否解绑：{{ record.deductUnbindFlag ? '是' : '否' }}</div>
            <div>解绑时间：{{ record.deductUnbindTime || '-' }}</div>
            <div>解绑来源：{{ getDeductUnbindSourceDesc(record.deductUnbindSource) }}</div>
          </div>
        </template>

        <template v-if="column.dataIndex === 'userComplaintCount'">
          {{ record.complaintCount || 0 }}
        </template>

        <template v-if="column.dataIndex === 'finallyReceivableDate'">
          {{ record.finallyReceivableDate || '-' }}
        </template>

        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ text || '-' }}</span>
        </template>

        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <!-- 只有待处理状态才显示处理按钮 -->
            <a-button 
              v-privilege="'business:complaint-list:handle'"
              v-if="record.complaintStatus === 'WAIT_DEAL'" 
              @click="handleComplaint(record)" 
              type="link"
            >
              处理
            </a-button>
            <a-button v-privilege="'business:complaint-list:viewDetail'" @click="viewComplaintDetail(record)" type="link">
              处理详情
            </a-button>
            <a-button v-privilege="'business:complaint-list:viewOrderDetail'" @click="viewOrderDetail(record)" type="link">
              订单详情
            </a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共 ${total} 条数据`"
      />
    </div>

    <!-- 处理投诉弹窗 -->
    <ComplaintHandleModal 
      ref="handleModal" 
      @reloadList="queryData" 
    />

    <!-- 投诉详情弹窗 -->
    <ComplaintDetailModal 
      ref="detailModal" 
    />
  </a-card>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { useRouter } from 'vue-router';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  ExportOutlined 
} from '@ant-design/icons-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { complaintApi } from '/@/api/business/complaint/complaint-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { 
  COMPLAINT_STATUS_ENUM,
  COMPLAINT_SOLVE_RESULT_TYPE_ENUM
} from '/@/constants/business/complaint/complaint-const';
import { smartSentry } from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
import ComplaintHandleModal from './components/complaint-handle-modal.vue';
import ComplaintDetailModal from './components/complaint-detail-modal.vue';
import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';
import _ from 'lodash';

// ---------------------------- 路由实例 ----------------------------
const router = useRouter();

// ---------------------------- 表格列 ----------------------------

const columns = ref([
  {
    title: '订单信息',
    dataIndex: 'orderInfo',
    width: 300,
    resizable: true
  },
  {
    title: '处理状态',
    dataIndex: 'handleStatus',
    width: 120,
    resizable: true
  },
  {
    title: '订单状态',
    dataIndex: 'orderStatus',
    width: 120,
    resizable: true
  },
  {
    title: '用户信息',
    dataIndex: 'userInfo',
    width: 180,
    resizable: true
  },
  {
    title: '邀请码',
    dataIndex: 'inviteCode',
    width: 200,
    resizable: true
  },
  {
    title: '联系人',
    dataIndex: 'contactPerson',
    width: 150,
    resizable: true
  },
  {
    title: '投诉内容',
    dataIndex: 'complaintContent',
    width: 200,
    resizable: true,
    ellipsis: true
  },
  {
    title: '是否逾期',
    dataIndex: 'isExpired',
    width: 100,
    resizable: true
  },
  {
    title: '逾期期数',
    dataIndex: 'orderOverdueCount',
    width: 100,
    resizable: true
  },
  {
    title: '最高逾期天数',
    dataIndex: 'overdueMaxDays',
    width: 130,
    resizable: true
  },
  {
    title: '最近应扣日期',
    dataIndex: 'latestDeductDate',
    width: 150,
    resizable: true
  },
  {
    title: '处理反馈',
    dataIndex: 'processFeedback',
    width: 200,
    resizable: true,
    ellipsis: true
  },
  {
    title: '处理时间',
    dataIndex: 'processTime',
    width: 150,
    resizable: true
  },
  {
    title: '处理反馈类型',
    dataIndex: 'processFeedbackType',
    width: 180,
    resizable: true
  },
  {
    title: '绑定代扣信息',
    dataIndex: 'bindDeductionInfo',
    width: 250,
    resizable: true
  },
  {
    title: '用户投诉次数',
    dataIndex: 'userComplaintCount',
    width: 130,
    resizable: true,
    align: 'center'
  },
  {
    title: '最后一期应收日期',
    dataIndex: 'finallyReceivableDate',
    width: 150,
    resizable: true
  },
  {
    title: '门店名称',
    dataIndex: 'storeName',
    width: 120,
    resizable: true
  },
  {
    title: '操作',
    dataIndex: 'action',
    width: 200,
    resizable: true,
    fixed: 'right'
  }
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  orderNo: '', // 订单编号
  customerName: '', // 用户姓名
  customerPhone: '', // 用户手机号
  complaintStatus: undefined, // 投诉状态:[WAIT_DEAL:待处理; DEAL_COMPLETED:已处理]
  orderStatus: undefined, // 订单状态:[WAIT_AUTHORIZ:待授权; EXECUTION_CONTRACT:履约中; ENDED:已结束; CANCELED:已取消; TERMINATED:已解约; PAUSED:已暂停]
  receivePaymentType: undefined, // 收款方式:[ZFB_ANXIN:安心付; BANK_SEGMENT:账单付]
  overdueFlag: undefined, // 是否逾期:0否1是
  overdueDaysFrom: undefined, // 逾期天数起始
  overdueDaysTo: undefined, // 逾期天数结束
  cancelWithholding: undefined, // 代扣解除:0否1是
  complaintDateRange: undefined, // 投诉日期范围
  latestDeductionDateRange: undefined, // 最近应扣日期范围
  solveResultType: undefined, // 处理结果类型
  complaintCountFrom: undefined, // 投诉次数起始
  complaintCountTo: undefined, // 投诉次数结束
  storeName: [], // 门店名称查询字段
  pageNum: 1,
  pageSize: 10
};

// 查询表单form
const queryForm = reactive(_.cloneDeep(queryFormState));
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);

function handleResizeColumn(w, col) {
  columns.value.forEach((item) => {
    if (item.dataIndex === col.dataIndex) {
      item.width = Math.floor(w);
      item.dragAndDropFlag = true;
    }
  });
}

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, _.cloneDeep(queryFormState));
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch() {
  queryForm.pageNum = 1;
  queryData();
}

// 统一参数处理函数
function prepareQueryParams() {
  const params = _.cloneDeep(queryForm);
  
  // 处理字符串类型参数：空值转null
  if (params.complaintStatus !== '' && params.complaintStatus != null) {
    // 保持原值
  } else {
    params.complaintStatus = null;
  }
  
  if (params.orderStatus !== '' && params.orderStatus != null) {
    // 保持原值
  } else {
    params.orderStatus = null;
  }
  
  if (params.receivePaymentType !== '' && params.receivePaymentType != null) {
    // 保持原值
  } else {
    params.receivePaymentType = null;
  }
  
  if (params.solveResultType !== '' && params.solveResultType != null) {
    // 保持原值
  } else {
    params.solveResultType = null;
  }
  
  // 处理Integer类型参数：空值转null，有值转数字
  if (params.overdueFlag !== '' && params.overdueFlag != null) {
    params.overdueFlag = parseInt(params.overdueFlag);
  } else {
    params.overdueFlag = null;
  }
  
  if (params.cancelWithholding !== '' && params.cancelWithholding != null) {
    params.cancelWithholding = parseInt(params.cancelWithholding);
  } else {
    params.cancelWithholding = null;
  }
  
  // 处理投诉日期范围
  if (queryForm.complaintDateRange && queryForm.complaintDateRange.length === 2) {
    params.complaintStartDate = queryForm.complaintDateRange[0]?.format?.('YYYY-MM-DD');
    params.complaintEndDate = queryForm.complaintDateRange[1]?.format?.('YYYY-MM-DD');
  }
  delete params.complaintDateRange;
  
  // 处理最近应扣日期范围
  if (queryForm.latestDeductionDateRange && queryForm.latestDeductionDateRange.length === 2) {
    params.latestDeductStartDate = queryForm.latestDeductionDateRange[0]?.format?.('YYYY-MM-DD');
    params.latestDeductEndDate = queryForm.latestDeductionDateRange[1]?.format?.('YYYY-MM-DD');
  }
  delete params.latestDeductionDateRange;
  
  // 处理投诉次数范围
  // 这些字段保持不变
  
  // 处理门店名称查询参数（多选时逗号拼接门店ID）
  if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
    // 如果选择了门店，将ID数组用逗号拼接成字符串
    params.storeName = params.storeName.join(',');
  } else {
    // 如果没有选择门店，传 null
    params.storeName = null;
  }
  
  return params;
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    // 使用统一的参数处理
    const queryParams = prepareQueryParams();
    
    // 调用真实API
    let queryResult = await complaintApi.queryComplaintList(queryParams);
    tableData.value = queryResult.data.list;
    total.value = queryResult.data.total;
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}

onMounted(queryData);

// ---------------------------- 工具方法 ----------------------------

// 手机号脱敏处理
function maskPhoneNumber(phone) {
  if (!phone || phone.length < 7) {
    return phone || '-';
  }
  return phone.substring(0, 3) + '****' + phone.substring(phone.length - 4);
}

// ---------------------------- 状态显示方法 ----------------------------

// 订单状态:[WAIT_AUTHORIZ:待授权; EXECUTION_CONTRACT:履约中; ENDED:已结束; CANCELED:已取消; TERMINATED:已解约; PAUSED:已暂停]
function getOrderStatusColor(status) {
  const colorMap = {
    'WAIT_AUTHORIZ': 'default',   // 待授权 - 灰色
    'EXECUTION_CONTRACT': 'blue', // 履约中 - 蓝色
    'ENDED': 'green',             // 已结束 - 绿色
    'CANCELED': 'default',        // 已取消 - 灰色
    'TERMINATED': 'red',          // 已解约 - 红色
    'PAUSED': 'warning',          // 已暂停 - 黄色
    'PENDING_BIND': 'orange'      // 待绑卡（保留以防API返回）
  };
  return colorMap[status] || 'default';
}

function getOrderStatusText(status) {
  const statusMap = {
    'WAIT_AUTHORIZ': '待授权',
    'EXECUTION_CONTRACT': '履约中',
    'ENDED': '已结束',
    'CANCELED': '已取消',
    'TERMINATED': '已解约',
    'PAUSED': '已暂停',
    'PENDING_BIND': '待绑卡'
  };
  return statusMap[status] || '未知';
}

// 投诉状态:[WAIT_DEAL:待处理; DEAL_COMPLETED:已处理]
function getComplaintStatusColor(status) {
  const colorMap = {
    'WAIT_DEAL': 'red',
    'DEAL_COMPLETED': 'green'
  };
  return colorMap[status] || 'default';
}

function getComplaintStatusText(status) {
  const statusMap = {
    'WAIT_DEAL': '待处理',
    'DEAL_COMPLETED': '已处理'
  };
  return statusMap[status] || '未知';
}

// 收款方式:[ZFB_ANXIN:安心付; BANK_SEGMENT:账单付]
function getReceivePaymentTypeText(type) {
  const typeMap = {
    'ZFB_ANXIN': '安心付',
    'BANK_SEGMENT': '账单付'
  };
  return typeMap[type] || '-';
}

// 代扣方式:[ANXIN_DEDUCT:安心付代扣; BANK_DEDUCT:银行卡代扣; NONE:无]
function getDeductMethodText(method) {
  const methodMap = {
    'ANXIN_DEDUCT': '安心付代扣',
    'BANK_DEDUCT': '银行卡代扣',
    'NONE': '无'
  };
  return methodMap[method] || '-';
}

// 解绑来源枚举
function getDeductUnbindSourceDesc(value) {
  const enumMap = {
    'PLACE_ORDER_UNBIND': '下单未绑卡',
    'MERCHANT_UNBIND': '商家解绑'
  };
  return enumMap[value] || value || '-';
}

// 处理结果类型
function getSolveResultTypeText(type) {
  const enumItem = Object.values(COMPLAINT_SOLVE_RESULT_TYPE_ENUM).find(item => item.value === type);
  return enumItem ? enumItem.label : '-';
}

// ---------------------------- 处理投诉相关 ----------------------------

const handleModal = ref();
const detailModal = ref();

// 处理单个投诉
function handleComplaint(record) {
  handleModal.value.showModal(record);
}

// 查看投诉详情
function viewComplaintDetail(record) {
  detailModal.value.showModal(record);
}

// 跳转到分期订单详情
const goToSegmentOrderDetail = (orderId) => {
  router.push({
    path: '/order/segment-order-detail',
    query: { id: orderId }
  });
};

// 查看订单详情
function viewOrderDetail(record) {
  // 跳转到分段订单详情页面
  router.push({
    path: '/order/segment-order-detail',
    query: { id: record.orderId },
  });
}

// ---------------------------- 导出功能 ----------------------------

async function onExportComplaints() {
  message.info('导出功能开发中...');
}
</script>

<style scoped>
.smart-table-operate {
  display: flex;
  gap: 8px;
}

.order-info-cell {
  line-height: 1.6;
}

.user-info-cell {
  line-height: 1.6;
}

.text-gray {
  color: inherit;
  font-size: inherit;
}

.bind-deduction-info {
  line-height: 1.6;
}

.process-feedback-content {
  max-width: 200px;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.5;
}

.complaint-content {
  max-width: 200px;
  word-break: break-all;
  white-space: pre-wrap;
  line-height: 1.5;
}

.order-link {
  color: #1890ff;
  cursor: pointer;
}

.order-link:hover {
  color: #40a9ff;
}

.invitation-info-cell {
  line-height: 1.6;
}

/* 筛选区域卡片圆角样式 */
.smart-query-form {
  border-radius: 4px;
}

/* 隐藏表格滚动条但保留滚动功能 */
:deep(.ant-table-body) {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE/Edge */
}

:deep(.ant-table-body)::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}
</style> 