<!--
  * 分段订单详情页面
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 10:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <!-- 基本信息卡片 -->
  <a-card style="margin-bottom: 15px" size="small">
    <a-descriptions :title="`订单详情 - ${detail.orderNo || '加载中...'}`" :column="3" size="small">
      
      <a-descriptions-item label="订单编号">{{ detail.orderNo || '-' }}</a-descriptions-item>
      <a-descriptions-item label="创建时间">{{ detail.createTime || '-' }}</a-descriptions-item>
      <a-descriptions-item label="商品名称">{{ detail.productName || '-' }}</a-descriptions-item>
      
      <a-descriptions-item label="用户手机号">{{ maskPhone(detail.customerPhone) }}</a-descriptions-item>
      <a-descriptions-item label="用户姓名">{{ detail.customerName || '-' }}</a-descriptions-item>
      <a-descriptions-item label="订单状态">
        <a-tag :color="getStatusColor(detail.orderStatus)">
          {{ getOrderStatusText(detail.orderStatus) }}
        </a-tag>
      </a-descriptions-item>
      
      <a-descriptions-item label="订单金额">
        {{ formatMoney(detail.orderAmount) }}
      </a-descriptions-item>
      <a-descriptions-item label="扣款周期">{{ getDeductPeriodTypeText(detail.deductPeriodType) }}</a-descriptions-item>
      <a-descriptions-item label="分期数">{{ detail.totalInstallmentCount || 0 }}</a-descriptions-item>
      
      <a-descriptions-item label="前段分期数">{{ detail.forepartInstallmentCount || 0 }}</a-descriptions-item>
      <a-descriptions-item label="前段每期金额">{{ formatMoney(detail.forepartEachAmount) }}</a-descriptions-item>
      <a-descriptions-item label="后段每期金额">{{ formatMoney(detail.backendEachAmount) }}</a-descriptions-item>
      
      <a-descriptions-item label="已扣金额">
        {{ formatMoney(detail.deductedAmount) }}
      </a-descriptions-item>
      <a-descriptions-item label="已扣期数">{{ detail.deductedCount || 0 }}</a-descriptions-item>
      <a-descriptions-item label="未扣金额">
        {{ formatMoney(detail.remainAmount) }}
      </a-descriptions-item>
      
      <a-descriptions-item label="邀请码">{{ detail.invitationCode || '-' }}</a-descriptions-item>
      <a-descriptions-item label="是否逾期">
        <a-tag :color="detail.overdueFlag ? 'red' : 'green'">
          {{ detail.overdueFlag ? '是' : '否' }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="逾期金额" v-if="detail.overdueFlag">
        {{ formatMoney(detail.overdueAmount) }}
      </a-descriptions-item>
    </a-descriptions>
  </a-card>

  <!-- 收款信息卡片 -->
  <a-card size="small">
    <!-- 操作按钮 -->
    <div class="smart-table-btn-block" style="display: flex; justify-content: flex-start; align-items: center; margin: 0 0 12px 0;">
      <a-button 
        @click="showOfflinePayment" 
        type="primary" 
        style="margin-right: 8px;"
      >
        线下收款
      </a-button>
      <a-button 
        v-if="detail.receivePaymentType === 'ZFB_ANXIN'"
        @click="showCancelSingleDeduction" 
        type="primary"
      >
        取消单期代扣
      </a-button>
    </div>

    <!-- 收款计划表格 -->
    <a-table
      size="small"
      :dataSource="paymentPlanList"  
      :columns="paymentColumns"
      rowKey="installmentDeductId"
      :pagination="false"
      bordered
      :scroll="{ x: 1200 }"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'periodNumber'">
          {{ record.installmentNo }}
        </template>
        <template v-if="column.dataIndex === 'scheduledDate'">
          {{ record.receivableDate }}
        </template>
        <template v-if="column.dataIndex === 'segmentAmount'">
          {{ formatMoney(record.installmentAmount) }}
        </template>
        <template v-if="column.dataIndex === 'paymentStatus'">
          <a-tag :color="getPaymentStatusColor(record.deductStatus)">
            {{ getPaymentStatusText(record.deductStatus) }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'paidAmount'">
          {{ record.deductAmount != null ? formatMoney(record.deductAmount) : '-' }}
        </template>
        <template v-if="column.dataIndex === 'paidTime'">
          {{ record.deductTime || '-' }}
        </template>
        <template v-if="column.dataIndex === 'failureReason'">
          {{ record.deductFailReason || '-' }}
        </template>
        <template v-if="column.dataIndex === 'remark'">
          {{ record.remark || '-' }}
        </template>
      </template>
    </a-table>

    <!-- 分页 -->
    <div class="smart-query-table-page">
      <a-pagination
        v-model:current="paymentQuery.pageNum"
        v-model:pageSize="paymentQuery.pageSize"
        :total="paymentTotal"
        :show-total="(total) => `共 ${total} 条`"
        show-size-changer
        show-quick-jumper
        :page-size-options="[20, 50, 100]"
        @change="queryPaymentPlan"
        @showSizeChange="queryPaymentPlan"
      />
    </div>
  </a-card>

  <!-- 解约信息卡片 -->
  <a-card v-if="detail.orderStatus === 'TERMINATED'" style="margin-top: 15px" size="small">
    <a-descriptions title="解约信息" :column="2" size="small">
      <a-descriptions-item label="解约操作人">{{ detail.orderAfterSalesVO.customerName || '-' }}</a-descriptions-item>
      <a-descriptions-item label="解约时间">{{ detail.orderAfterSalesVO.updateTime || '-' }}</a-descriptions-item>
      <a-descriptions-item label="解约类型">{{ getTerminateTypeText(detail.orderAfterSalesVO.terminatedType) }}</a-descriptions-item>
      <a-descriptions-item label="解约备注">{{ detail.orderAfterSalesVO.auditRemark || '-' }}</a-descriptions-item>
    </a-descriptions>
  </a-card>

  <!-- 线下收款弹窗 -->
  <OfflinePaymentModal
    v-model:visible="offlinePaymentVisible"
    :order-info="{ orderId: detailId }"
    @confirm="handleOfflinePaymentConfirm"
    @cancel="handleOfflinePaymentCancel"
  />

  <!-- 取消单期代扣弹窗 -->
  <CancelSingleDeductionModal
    v-model:visible="cancelDeductionVisible"
    :payment-plan-list="paymentPlanList"
    @confirm="handleCancelDeductionConfirm"
    @cancel="handleCancelDeductionCancel"
  />
</template>

<script setup>
  import { onMounted, ref, reactive } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { message, Modal } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { segmentOrderApi } from '/@/api/business/order/segment-order-api';
  import { dataFormatter } from '/@/utils/data-formatter';
  import OfflinePaymentModal from './components/offline-payment-modal.vue';
  import CancelSingleDeductionModal from './components/cancel-single-deduction-modal.vue';

  const route = useRoute();
  const router = useRouter();
  
  // 详情ID
  const detailId = ref();
  
  // 详情数据
  const detail = ref({});

  // 收款计划查询参数
  const paymentQuery = reactive({
    orderId: undefined,
    pageNum: 1,
    pageSize: 20,
    searchCount: true,
    sortItemList: []
  });

  // 收款计划数据
  const paymentPlanList = ref([]);
  const paymentTotal = ref(0);

  // 线下收款弹窗
  const offlinePaymentVisible = ref(false);

  // 取消单期代扣弹窗
  const cancelDeductionVisible = ref(false);

  // 收款计划表格列
  const paymentColumns = ref([
    {
      title: '期数',
      dataIndex: 'periodNumber',
      width: 80,
      align: 'center',
    },
    {
      title: '应收日期',
      dataIndex: 'scheduledDate',
      width: 120,
    },
    {
      title: '分段金额',
      dataIndex: 'segmentAmount',
      width: 120,
      align: 'right',
    },
    {
      title: '扣款状态',
      dataIndex: 'paymentStatus',
      width: 100,
      align: 'center',
    },
    {
      title: '扣款金额',
      dataIndex: 'paidAmount',
      width: 120,
      align: 'right',
    },
    {
      title: '扣款时间',
      dataIndex: 'paidTime',
      width: 150,
    },
    {
      title: '扣款失败原因',
      dataIndex: 'failureReason',
      width: 150,
      ellipsis: true,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      width: 150,
      ellipsis: true,
    },
  ]);

  // 页面初始化
  onMounted(() => {
    console.log('详情页面已加载');
    console.log('路由参数:', route.query);
    
    if (route.query.id) {
      detailId.value = Number(route.query.id);
      paymentQuery.orderId = detailId.value;
      getDetail();
      queryPaymentPlan();
    } else {
      console.error('缺少订单ID参数');
    }
  });



  // 获取订单详情
  async function getDetail() {
    try {
      console.log('获取订单详情，ID:', detailId.value);
      SmartLoading.show();
      
      const result = await segmentOrderApi.getDetail(detailId.value);
      detail.value = result.data;
      console.log('API调用成功，获取到详情数据:', result.data);
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取订单详情失败');
    } finally {
      SmartLoading.hide();
    }
  }

  // 查询收款计划
  async function queryPaymentPlan() {
    try {
      console.log('查询收款计划');
      
      const result = await segmentOrderApi.queryPaymentPlan(paymentQuery);
      paymentPlanList.value = result.data.list;
      paymentTotal.value = result.data.total;
      console.log('收款计划API调用成功');
    } catch (error) {
      console.error('查询收款计划失败:', error);
      message.error('查询收款计划失败');
    }
  }

  // 线下收款
  function showOfflinePayment() {
    offlinePaymentVisible.value = true;
  }

  // 线下收款确认
  async function handleOfflinePaymentConfirm(paymentData) {
    try {
      SmartLoading.show();
      
      // 调用API处理线下收款
      const requestParams = {
        installmentDeductId: paymentData.installmentDeductId || 0,
        remark: paymentData.remark || ''
      };
      
      const res = await segmentOrderApi.offlinePayment(detailId.value, requestParams);
      
      if (res.ok) {
        message.success('提交成功');
        
        // 刷新收款计划
        queryPaymentPlan();
        
        // 关闭弹窗
        offlinePaymentVisible.value = false;
      } else {
        message.error(res.msg || '提交失败');
      }
    } catch (e) {
      console.error('提交失败:', e);
      message.error(e.message || '提交失败');
    } finally {
      SmartLoading.hide();
    }
  }

  // 线下收款取消
  function handleOfflinePaymentCancel() {
    offlinePaymentVisible.value = false;
  }

  // 取消单期代扣
  function showCancelSingleDeduction() {
    cancelDeductionVisible.value = true;
  }

  // 取消单期代扣确认
  async function handleCancelDeductionConfirm(cancelData) {
    try {
      SmartLoading.show();
      // 这里应该调用API处理取消单期代扣
      // await segmentOrderApi.cancelSingleDeduction(cancelData);
      
      // 模拟处理
      console.log('取消单期代扣数据:', cancelData);
      
      // 更新对应期数的状态为已取消
      const periodIndex = paymentPlanList.value.findIndex(item => item.periodNumber === cancelData.periodNumber);
      if (periodIndex !== -1) {
        paymentPlanList.value[periodIndex].paymentStatus = 3; // 3表示已取消
        paymentPlanList.value[periodIndex].remark = cancelData.remark;
      }
      
      message.success('取消单期代扣成功');
      
      // 刷新收款计划
      queryPaymentPlan();
    } catch (e) {
      console.error('取消单期代扣失败:', e);
      message.error('取消单期代扣失败');
    } finally {
      SmartLoading.hide();
    }
  }

  // 取消单期代扣取消
  function handleCancelDeductionCancel() {
    cancelDeductionVisible.value = false;
  }

  // 获取订单状态文本
  function getOrderStatusText(status) {
    const statusMap = {
      'WAIT_AUTHORIZ': '待授权',
      'EXECUTION_CONTRACT': '履约中',
      'ENDED': '已结束',
      'CANCELED': '已取消',
      'TERMINATED': '已解约',
      'PAUSED': '已暂停'
    };
    return statusMap[status] || '未知';
  }

  // 获取支付状态文本 - API字段名 deductStatus
  // 扣款状态:[WAIT_AUTHORIZ:待授权; PENDING_DEDUCT:待扣款; PAID:已扣款; DEDUCT_FAIL:扣款失败; PAUSED:已暂停; TERMINATED:已解约; CANCELED:已取消]
  function getPaymentStatusText(status) {
    const statusMap = {
      'WAIT_AUTHORIZ': '待授权',
      'PENDING_DEDUCT': '待扣款',
      'PAID': '已扣款', 
      'DEDUCT_FAIL': '扣款失败',
      'PAUSED': '已暂停',
      'TERMINATED': '已解约',
      'CANCELED': '已取消'
    };
    return statusMap[status] || status || '未知';
  }

  // 获取状态颜色
  function getStatusColor(status) {
    const colorMap = {
      'WAIT_AUTHORIZ': 'default',   // 待授权 - 灰色
      'EXECUTION_CONTRACT': 'blue', // 履约中 - 蓝色
      'ENDED': 'green',             // 已结束 - 绿色
      'CANCELED': 'default',        // 已取消 - 灰色
      'TERMINATED': 'red',          // 已解约 - 红色
      'PAUSED': 'warning'           // 已暂停 - 黄色
    };
    return colorMap[status] || 'default';
  }

  // 获取支付状态颜色 - API字段名 deductStatus
  // 扣款状态:[WAIT_AUTHORIZ:待授权; PENDING_DEDUCT:待扣款; PAID:已扣款; DEDUCT_FAIL:扣款失败; PAUSED:已暂停; TERMINATED:已解约; CANCELED:已取消]
  function getPaymentStatusColor(status) {
    const colorMap = {
      'WAIT_AUTHORIZ': 'default',
      'PENDING_DEDUCT': 'blue',
      'PAID': 'green',
      'DEDUCT_FAIL': 'red',
      'PAUSED': 'warning',
      'TERMINATED': 'red',
      'CANCELED': 'default'
    };
    return colorMap[status] || 'default';
  }

  // 获取解约类型文本
  function getTerminateTypeText(type) {
    const typeMap = {
      'NORMAL_TERMINATED': '商家解约',
      'APPLY_TERMINATED': '用户申请',
    };
    return typeMap[type] || '未知';
  }

  // 获取扣款周期文本
  function getDeductPeriodTypeText(type) {
    const typeMap = {
      'DEDUCT_BY_MONTH': '按月',
      'DEDUCT_BY_WEEK': '按周',
      'DEDUCT_BY_DAY': '按日',
      'DEDUCT_BY_QUARTER': '按季度',
      'DEDUCT_BY_YEAR': '按年',
    };
    return typeMap[type] || type || '-';
  }



  // 格式化金额
  function formatMoney(value) {
    return dataFormatter.formatMoney(value);
  }

  // 格式化手机号
  function maskPhone(phone) {
    return dataFormatter.maskPhone(phone);
  }
</script>

<style lang="less" scoped>
  :deep(.ant-descriptions-item-content) {
    flex: 1;
    overflow: hidden;
  }

  :deep(.ant-descriptions-item-label) {
    font-weight: 500;
    color: #262626;
    width: 120px;
  }

  :deep(.ant-descriptions-item-content) {
    color: #595959;
  }

  .smart-table-btn-block {
    margin-bottom: 16px;
  }
  
  .smart-query-table-page {
    margin-top: 16px;
  }

  // 设置表格行高为52px
  :deep(.ant-table-tbody > tr > td) {
    height: 52px;
  }
</style> 