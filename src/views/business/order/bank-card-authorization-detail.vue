<!--
  * 银行卡授权订单详情页面
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 18:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-card size="small" :bordered="false" :hoverable="true">
    <a-table
      size="small"
      :dataSource="orderList"
      :columns="columns"
      rowKey="orderId"
      :scroll="{ x: 2000, y: 2000 }"
      bordered
      :pagination="false"
      :showSorterTooltip="false"
      :loading="tableLoading"
      @change="onChange"
      @resizeColumn="handleResizeColumn"
    >
      <template #bodyCell="{ text, record, column }">
        <!-- 商品信息列 -->
        <template v-if="column.dataIndex === 'productInfo'">
          <div class="order-info-cell">
            <div>商品名称：{{ record.productName || '-' }}</div>
            <div class="text-gray">订单编号：
              <a @click="goToSegmentOrderDetail(record.orderId)" class="order-link">
                {{ record.orderNo || '-' }}
              </a>
            </div>
            <div class="text-gray">创建人：{{ record.actualName || '-' }}</div>
          </div>
        </template>

        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'status'">
          <a-tag :color="getOrderStatusColor(record.orderStatus)">
            {{ getOrderStatusDesc(record.orderStatus) }}
          </a-tag>
        </template>

        <!-- 用户信息列 -->
        <template v-if="column.dataIndex === 'userInfo'">
          <div class="user-info-cell">
            <div>{{ record.customerName }}</div>
            <div class="text-gray">{{ maskPhone(record.customerPhone) }}</div>
          </div>
        </template>

        <!-- 邀请码信息列 -->
        <template v-if="column.dataIndex === 'invitationCode'">
          <div class="invitation-info-cell">
            <div>邀请码：{{ record.invitationCode || '-' }}</div>
            <div>员工：{{ record.employeeName || '-' }}</div>
            <div>部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>

        <!-- 订单总金额列 -->
        <template v-if="column.dataIndex === 'totalAmount'">
          {{ formatMoney(record.orderAmount) }}
        </template>

        <!-- 分期数列 -->
        <template v-if="column.dataIndex === 'totalInstallmentCount'">
          {{ record.totalInstallmentCount ?? '-' }}
        </template>

        <!-- 扣款周期列 -->
        <template v-if="column.dataIndex === 'deductPeriodType'">
          {{ getDeductPeriodTypeDesc(record.deductPeriodType) }}
        </template>

        <!-- 前段期数列 -->
        <template v-if="column.dataIndex === 'forepartInstallmentCount'">
          {{ record.forepartInstallmentCount ?? '-' }}
        </template>

        <!-- 前段每期金额列 -->
        <template v-if="column.dataIndex === 'frontStageAmount'">
          {{ formatMoney(record.forepartEachAmount) }}
        </template>

        <!-- 后段每期金额列 -->
        <template v-if="column.dataIndex === 'backStageAmount'">
          {{ formatMoney(record.backendEachAmount) }}
        </template>

        <!-- 已扣金额列 -->
        <template v-if="column.dataIndex === 'deductedAmount'">
          {{ formatMoney(record.deductedAmount) }}
        </template>

        <!-- 未扣金额列 -->
        <template v-if="column.dataIndex === 'remainingAmount'">
          {{ formatMoney(record.remainAmount) }}
        </template>

        <!-- 是否逾期列 -->
        <template v-if="column.dataIndex === 'isOverdue'">
          <a-tag :color="record.overdueFlag === 1 ? 'error' : 'success'">
            {{ record.overdueFlag === 1 ? '是' : '否' }}
          </a-tag>
        </template>

        <!-- 逾期金额列 -->
        <template v-if="column.dataIndex === 'overdueAmount'">
          {{ formatMoney(record.overdueAmount) }}
        </template>

        <!-- 收款方式列 -->
        <template v-if="column.dataIndex === 'paymentMethod'">
          {{ getReceivePaymentTypeDesc(record.receivePaymentType) }}
        </template>

        <!-- 绑定代扣信息列 -->
        <template v-if="column.dataIndex === 'bindInfo'">
          <div class="deduction-info-cell">
            <div>当前代扣方式：{{ getDeductMethodDesc(record.deductMethod) }}</div>
            <div>代扣是否解除：{{ record.deductUnbindFlag === 1 ? '是' : '否' }}</div>
            <div>解绑时间：{{ record.deductUnbindTime || '-' }}</div>
            <div>解绑来源：{{ getDeductUnbindSourceDesc(record.deductUnbindSource) || '-' }}</div>
          </div>
        </template>

        <!-- 来源列 -->
        <template v-if="column.dataIndex === 'source'">
          {{ getAppTypeDesc(record.appType) }}
        </template>

        <!-- 备注列 -->
        <template v-if="column.dataIndex === 'remark'">
          {{ record.remark || '-' }}
        </template>

        <!-- 门店名称列 -->
        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ record.storeName || '-' }}</span>
        </template>

        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'operation'">
          <div class="smart-table-operate operation-buttons">
            <a-button type="link" size="small" @click="viewDetails(record)">详情</a-button>
          </div>
        </template>
      </template>
      </a-table>

      <div class="smart-query-table-page">
        <a-pagination
          showSizeChanger
          showQuickJumper
          show-less-items
          :pageSizeOptions="PAGE_SIZE_OPTIONS"
          :defaultPageSize="queryForm.pageSize"
          v-model:current="queryForm.pageNum"
          v-model:pageSize="queryForm.pageSize"
          :total="total"
          @change="queryData"
          @showSizeChange="queryData"
          :show-total="(total) => `共${total}条`"
        />
      </div>
    </a-card>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { dataFormatter } from '/@/utils/data-formatter';
import { segmentOrderApi } from '/@/api/business/order/segment-order-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { smartSentry } from '/@/lib/smart-sentry';
import _ from 'lodash';

const route = useRoute();
const router = useRouter();

// ---------------------------- 查询数据表单和方法 ----------------------------

const queryFormState = {
  customerBankcardId: undefined,
  pageNum: 1,
  pageSize: 10,
  sortItemList: [],
};

// 查询表单form
const queryForm = reactive(_.cloneDeep(queryFormState));
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const orderList = ref([]);
// 总数
const total = ref(0);

// 表格列配置
const columns = ref([
  {
    title: '创建时间',
    dataIndex: 'createTime',
    width: 160,
    resizable: true,
    sorter: true,
  },
  {
    title: '商品信息',
    dataIndex: 'productInfo',
    width: 300,
    resizable: true,
  },
  {
    title: '状态',
    dataIndex: 'status',
    width: 100,
    resizable: true,
    align: 'center',
  },
  {
    title: '用户信息',
    dataIndex: 'userInfo',
    width: 120,
    resizable: true,
  },
  {
    title: '邀请码',
    dataIndex: 'invitationCode',
    width: 200,
    resizable: true,
  },
  {
    title: '订单总金额',
    dataIndex: 'totalAmount',
    width: 120,
    resizable: true,
    align: 'right',
  },
  {
    title: '分期数',
    dataIndex: 'totalInstallmentCount',
    width: 80,
    resizable: true,
    align: 'center',
  },
  {
    title: '扣款周期',
    dataIndex: 'deductPeriodType',
    width: 100,
    resizable: true,
  },
  {
    title: '前段期数',
    dataIndex: 'forepartInstallmentCount',
    width: 100,
    resizable: true,
    align: 'center',
  },
  {
    title: '前段每期金额',
    dataIndex: 'frontStageAmount',
    width: 120,
    resizable: true,
    align: 'right',
  },
  {
    title: '后段每期金额',
    dataIndex: 'backStageAmount',
    width: 120,
    resizable: true,
    align: 'right',
  },
  {
    title: '已扣金额',
    dataIndex: 'deductedAmount',
    width: 100,
    resizable: true,
    align: 'right',
  },
  {
    title: '已扣期数',
    dataIndex: 'deductedCount',
    width: 100,
    resizable: true,
    align: 'center',
  },
  {
    title: '未扣金额',
    dataIndex: 'remainingAmount',
    width: 100,
    resizable: true,
    align: 'right',
  },
  {
    title: '是否逾期',
    dataIndex: 'isOverdue',
    width: 100,
    resizable: true,
    align: 'center',
  },
  {
    title: '逾期金额',
    dataIndex: 'overdueAmount',
    width: 100,
    resizable: true,
    align: 'right',
  },
  {
    title: '收款方式',
    dataIndex: 'paymentMethod',
    width: 100,
    resizable: true,
  },
  {
    title: '绑定代扣信息',
    dataIndex: 'bindInfo',
    width: 200,
    resizable: true,
  },
  {
    title: '来源',
    dataIndex: 'source',
    width: 120,
    resizable: true,
  },
  {
    title: '备注',
    dataIndex: 'remark',
    width: 150,
    resizable: true,
  },
  {
    title: '门店名称',
    dataIndex: 'storeName',
    width: 120,
    resizable: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    width: 80,
    fixed: 'right',
    resizable: true,
  },
]);

function handleResizeColumn(w, col) {
  columns.value.forEach((item) => {
    if (item.dataIndex === col.dataIndex) {
      item.width = Math.floor(w);
      // 拖动宽度标识
      item.dragAndDropFlag = true;
    }
  });
}

// 查询数据
async function queryData() {
  try {
    tableLoading.value = true;
    
    // 设置银行卡ID
    if (route.query.id) {
      queryForm.customerBankcardId = route.query.id;
    }
    
    const result = await segmentOrderApi.querySegmentOrderList(queryForm);
    
    orderList.value = result.data?.list || [];
    total.value = result.data?.total || 0;
    
  } catch (error) {
    console.error('查询关联订单列表失败:', error);
    message.error('查询订单列表失败');
  } finally {
    tableLoading.value = false;
  }
}



// 跳转到分期订单详情
const goToSegmentOrderDetail = (orderId) => {
  router.push({
    path: '/order/segment-order-detail',
    query: { id: orderId }
  });
};

// 查看详情
const viewDetails = (record) => {
  goToSegmentOrderDetail(record.orderId);
};

// ---------------------------- 表格排序 ----------------------------

function onChange(pagination, filters, sorter, { action }) {
  if (action === 'sort') {
    const { order, field } = sorter;
    let column = camelToUnderscore(field);
    let findIndex = queryForm.sortItemList.findIndex((e) => e.column === column);
    if (findIndex !== -1) {
      queryForm.sortItemList.splice(findIndex, 1);
    }
    if (order) {
      let isAsc = order !== 'ascend';
      queryForm.sortItemList.push({
        column,
        isAsc,
      });
    }
    queryData();
  }
}

function camelToUnderscore(str) {
  return str.replace(/([A-Z])/g, '_$1').toLowerCase();
}

// ---------------------------- 动态高度 ----------------------------

// 动态设置表格高度
const yHeight = ref(0);

function resetGetHeight() {
  // 表格头高度
  let tableCell = document.querySelector('.ant-table-cell');
  // 分页高度
  let page = document.querySelector('.smart-query-table-page');
  // 内容区总高度
  let box = document.querySelector('.admin-content');
  setTimeout(() => {
    let dueHeight = 24 + tableCell.offsetHeight + page.offsetHeight + 20;
    yHeight.value = box.offsetHeight - dueHeight;
  }, 100);
}

window.addEventListener(
  'resize',
  _.throttle(() => {
    resetGetHeight();
  }, 1000)
);

// ---------------------------- 数据格式化 ----------------------------

// 格式化手机号
function maskPhone(phone) {
  return dataFormatter.maskPhone(phone);
}

// 格式化金额
function formatMoney(value) {
  return dataFormatter.formatMoney(value);
}

// 枚举转换函数 - 使用与segment-order-list.vue相同的函数
function getOrderStatusDesc(value) {
  const enumMap = {
    'WAIT_AUTHORIZ': '待授权',
    'EXECUTION_CONTRACT': '履约中',
    'ENDED': '已结束',
    'TERMINATED': '已解约',
    'PAUSED': '已暂停',
    'CANCELED': '已取消'
  };
  return enumMap[value] || value || '-';
}

function getOrderStatusColor(status) {
  const colorMap = {
    'WAIT_AUTHORIZ': 'default',   // 待授权 - 灰色
    'EXECUTION_CONTRACT': 'blue', // 履约中 - 蓝色
    'ENDED': 'green',             // 已结束 - 绿色
    'CANCELED': 'default',        // 已取消 - 灰色
    'TERMINATED': 'red',          // 已解约 - 红色
    'PAUSED': 'warning',          // 已暂停 - 黄色
  };
  return colorMap[status] || 'default';
}

function getDeductPeriodTypeDesc(value) {
  const enumMap = {
    'DEDUCT_BY_MONTH': '按月',
    'DEDUCT_BY_WEEK': '按周',
    'DEDUCT_BY_DAY': '按日',
    'DEDUCT_BY_QUARTER': '按季度',
    'DEDUCT_BY_YEAR': '按年'
  };
  return enumMap[value] || value || '-';
}

function getReceivePaymentTypeDesc(value) {
  const enumMap = {
    'ZFB_ANXIN': '安心付',
    'BANK_SEGMENT': '账单付'
  };
  return enumMap[value] || value || '-';
}

function getDeductMethodDesc(value) {
  const enumMap = {
    'ANXIN_DEDUCT': '安心付代扣',
    'BANK_DEDUCT': '银行卡代扣',
    'NONE': '无'
  };
  return enumMap[value] || value || '-';
}

function getDeductUnbindSourceDesc(value) {
  const enumMap = {
    'PLACE_ORDER_UNBIND': '下单未绑卡',
    'MERCHANT_UNBIND': '商家解绑'
  };
  return enumMap[value] || value || '-';
}

function getAppTypeDesc(value) {
  const enumMap = {
    'MERCHANT': '商家独立小程序',
    'HUANLEFU': '欢乐付小程序'
  };
  return enumMap[value] || value || '-';
}

// 页面初始化
onMounted(() => {
  queryData();
  resetGetHeight();
});
</script>

<style lang="less" scoped>
.order-info-cell {
  line-height: 1.6;
}

.user-info-cell {
  line-height: 1.6;
}

.deduction-info-cell {
  line-height: 1.6;
}

.invitation-info-cell {
  line-height: 1.6;
}

.text-gray {
  color: inherit;
  font-size: inherit;
}

.operation-buttons {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  white-space: nowrap;
  align-items: center;
  gap: 8px;
  line-height: 1.2;
}

.order-link {
  color: #1890ff;
  cursor: pointer;
}

.order-link:hover {
  color: #40a9ff;
}
</style> 