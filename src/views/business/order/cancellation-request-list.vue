<!--
  * 解约申请列表
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 15:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="用户手机号" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.customerPhone" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="商品名称" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.productName" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="审核状态" class="smart-query-form-item">
        <a-select style="width: 120px" v-model:value="queryForm.afterSalesStatus" placeholder="全部" allowClear>
          <a-select-option value="WAIT_DEAL">待审核</a-select-option>
          <a-select-option value="PASS">已通过</a-select-option>
          <a-select-option value="REJECT">已拒绝</a-select-option>
          <a-select-option value="CANCEL">用户取消</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="收款方式" class="smart-query-form-item">
        <a-select style="width: 120px" v-model:value="queryForm.receivePaymentType" placeholder="全部" allowClear>
          <a-select-option value="BANK_SEGMENT">账单付</a-select-option>
          <a-select-option value="ZFB_ANXIN">安心付</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="申请日期" class="smart-query-form-item">
        <a-range-picker 
          style="width: 240px" 
          v-model:value="queryForm.applyDateRange" 
          format="YYYY-MM-DD"
          :placeholder="['开始日期', '结束日期']"
        />
      </a-form-item>

      <a-form-item label="邀请码" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.invitationCode" placeholder="请输入" @keypress="onlyNumber" />
      </a-form-item>

      <a-form-item label="最近应扣日期" class="smart-query-form-item">
        <a-range-picker 
          style="width: 240px" 
          v-model:value="queryForm.nextDeductionDateRange" 
          format="YYYY-MM-DD"
          :placeholder="['开始日期', '结束日期']"
        />
      </a-form-item>

      <a-form-item label="是否逾期" class="smart-query-form-item">
        <a-select style="width: 120px" v-model:value="queryForm.overdueFlag" placeholder="全部" allowClear>
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="订单代扣解除" class="smart-query-form-item">
        <a-select style="width: 120px" v-model:value="queryForm.cancelWithholding" placeholder="全部" allowClear>
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="门店名称" class="smart-query-form-item">
                  <StoreMultiSelect 
            v-model="queryForm.storeName" 
            width="200px"
            placeholder="请选择门店"
          />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="onExport">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.ORDER.CANCELLATION_REQUEST" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="orderId"
      :scroll="{ x: 2000, y: 2000 }"
      bordered
      :pagination="false"
      :showSorterTooltip="false"
      :loading="tableLoading"
      @change="onChange"
      @resizeColumn="handleResizeColumn"
    >

      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'orderInfo'">
          <div class="order-info-cell">
            <div>{{ record.productName || record.goodsName }}</div>
            <div class="text-gray">订单编号：
              <a @click="goToSegmentOrderDetail(record.orderId)" class="order-link">
                {{ record.orderNo || record.orderCode }}
              </a>
            </div>
            <div class="text-gray">创建人：{{ record.actualName || '-' }}</div>
            <div class="text-gray">订单金额：{{ formatMoney(record.orderAmount) }}</div>
            <div class="text-gray">收款方式：{{ getReceivePaymentTypeText(record.receivePaymentType) }}</div>
            <div class="text-gray">下单时间：{{ record.createTime }}</div>
            <div class="text-gray">申请时间：{{ record.applyTime }}</div>
          </div>
        </template>
        
        <!-- 状态列 -->
        <template v-if="column.dataIndex === 'orderStatus'">
          <a-tag :color="getOrderStatusColor(record.orderStatus)">
            {{ getOrderStatusText(record.orderStatus) }}
          </a-tag>
        </template>
        
        <template v-if="column.dataIndex === 'afterSalesStatus'">
          <a-tag :color="getCurrentStatusColor(record.afterSalesStatus)">
            {{ getCurrentStatusText(record.afterSalesStatus) }}
          </a-tag>
        </template>
        
        <!-- 用户信息列 -->
        <template v-if="column.dataIndex === 'userInfo'">
          <div class="user-info-cell">
            <div>{{ record.customerName || record.userName }}</div>
            <div class="text-gray">{{ maskPhone(record.customerPhone || record.userPhone) }}</div>
          </div>
        </template>
        
        <!-- 邀请码列 -->
        <template v-if="column.dataIndex === 'inviteCode'">
          <div class="invitation-info-cell">
            <div>邀请码：{{ record.invitationCode || '-' }}</div>
            <div>员工：{{ record.employeeName || '-' }}</div>
            <div>部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>
        
        <!-- 订单总金额列 -->
        <template v-if="column.dataIndex === 'orderAmount'">
          {{ formatMoney(record.orderAmount) }}
        </template>
        
        <!-- 最近应扣日期列 -->
        <template v-if="column.dataIndex === 'latestDeductDate'">
          {{ record.latestDeductDate || '-' }}
        </template>
        
        <!-- 分期数列 -->
        <template v-if="column.dataIndex === 'totalInstallmentCount'">
          {{ record.totalInstallmentCount }}
        </template>
        
        <!-- 扣款周期列 -->
        <template v-if="column.dataIndex === 'deductPeriodType'">
          {{ getDeductPeriodTypeText(record.deductPeriodType) }}
        </template>
        
        <!-- 前段期数列 -->
        <template v-if="column.dataIndex === 'forepartInstallmentCount'">
          {{ record.forepartInstallmentCount }}
        </template>
        
        <!-- 前段每期金额列 -->
        <template v-if="column.dataIndex === 'forepartEachAmount'">
          {{ formatMoney(record.forepartEachAmount) }}
        </template>
        
        <!-- 后段每期金额列 -->
        <template v-if="column.dataIndex === 'backendEachAmount'">
          {{ formatMoney(record.backendEachAmount) }}
        </template>
        
        <!-- 已扣金额列 -->
        <template v-if="column.dataIndex === 'deductedAmount'">
          {{ formatMoney(record.deductedAmount) }}
        </template>
        
        <!-- 已扣期数列 -->
        <template v-if="column.dataIndex === 'deductedCount'">
          {{ record.deductedCount }}
        </template>
        
        <!-- 未扣金额列 -->
        <template v-if="column.dataIndex === 'remainAmount'">
          {{ formatMoney(record.remainAmount) }}
        </template>
        
        <!-- 是否逾期列 -->
        <template v-if="column.dataIndex === 'isOverdue'">
          <a-tag :color="record.overdueFlag === 1 ? 'error' : 'success'">
            {{ record.overdueFlag === 1 ? '是' : '否' }}
          </a-tag>
        </template>
        
        <!-- 逾期期数列 -->
        <template v-if="column.dataIndex === 'orderOverdueCount'">
          {{ record.orderOverdueCount ?? '-' }}
        </template>
        
        <!-- 逾期金额列 -->
        <template v-if="column.dataIndex === 'overdueAmount'">
          {{ formatMoney(record.overdueAmount) }}
        </template>
        
        <!-- 绑定代扣信息列 -->
        <template v-if="column.dataIndex === 'deductionInfo'">
          <div class="deduction-info-cell">
            <div>当前代扣方式：{{ getDeductMethodDesc(record.deductMethod) }}</div>
            <div>代扣是否解除：{{ record.deductUnbindFlag === 1 ? '是' : '否' }}</div>
            <div>解绑时间：{{ record.deductUnbindTime || '-' }}</div>
            <div>解绑来源：{{ getDeductUnbindSourceDesc(record.deductUnbindSource) }}</div>
          </div>
        </template>
        
        <!-- 门店名称列 -->
        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ text || '-' }}</span>
        </template>
        
        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'operation'">
          <div class="smart-table-operate">
            <a-button type="link" size="small" @click="viewDetail(record)" v-privilege="'business:cancellation-request-list:viewDetail'">详情</a-button>
            <a-button 
              v-privilege="'business:cancellation-request-list:approveRequest'"
              v-if="record.afterSalesStatus === 'WAIT_DEAL'"
              type="link" 
              size="small" 
              @click="approveRequest(record)"
            >
              审核
            </a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>

  <!-- 解约详情弹窗 -->
  <CancellationRequestDetailModal
    v-model:visible="detailVisible"
    :detail-data="currentDetailData"
    @cancel="handleDetailCancel"
  />

  <!-- 解约审核弹窗 -->
  <CancellationRequestAuditModal
    v-model:visible="auditVisible"
    :audit-data="currentAuditData"
    @confirm="handleAuditConfirm"
    @cancel="handleAuditCancel"
  />
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { SearchOutlined, ReloadOutlined, ExportOutlined } from '@ant-design/icons-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { dataFormatter } from '/@/utils/data-formatter';
  import { cancellationRequestApi } from '/@/api/business/order/cancellation-request-api';
  import CancellationRequestDetailModal from './components/cancellation-request-detail-modal.vue';
  import CancellationRequestAuditModal from './components/cancellation-request-audit-modal.vue';
  import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';

  import _ from 'lodash';
  import { smartSentry } from '/@/lib/smart-sentry';
  
  const router = useRouter();
  
  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '订单信息',
      dataIndex: 'orderInfo',
      width: 300,
      resizable: true,
    },
    {
      title: '订单状态',
      dataIndex: 'orderStatus',
      width: 80,
      align: 'center',
      resizable: true,
    },
    {
      title: '审核状态',
      dataIndex: 'afterSalesStatus',
      width: 80,
      align: 'center',
      resizable: true,
    },
    {
      title: '用户信息',
      dataIndex: 'userInfo',
      width: 120,
      resizable: true,
    },
    {
      title: '邀请码',
      dataIndex: 'inviteCode',
      width: 200,
      resizable: true,
    },
    {
      title: '订单总金额',
      dataIndex: 'orderAmount',
      width: 120,
      align: 'right',
      resizable: true,
    },
    {
      title: '最近应扣日期',
      dataIndex: 'latestDeductDate',
      width: 120,
      resizable: true,
    },
    {
      title: '分期数',
      dataIndex: 'totalInstallmentCount',
      width: 80,
      align: 'center',
      resizable: true,
    },
    {
      title: '扣款周期',
      dataIndex: 'deductPeriodType',
      width: 100,
      resizable: true,
    },
    {
      title: '前段期数',
      dataIndex: 'forepartInstallmentCount',
      width: 100,
      align: 'center',
      resizable: true,
    },
    {
      title: '前段每期金额',
      dataIndex: 'forepartEachAmount',
      width: 120,
      align: 'right',
      resizable: true,
    },
    {
      title: '后段每期金额',
      dataIndex: 'backendEachAmount',
      width: 120,
      align: 'right',
      resizable: true,
    },
    {
      title: '已扣金额',
      dataIndex: 'deductedAmount',
      width: 120,
      align: 'right',
      resizable: true,
    },
    {
      title: '已扣期数',
      dataIndex: 'deductedCount',
      width: 100,
      align: 'center',
      resizable: true,
    },
    {
      title: '未扣金额',
      dataIndex: 'remainAmount',
      width: 120,
      align: 'right',
      resizable: true,
    },
    {
      title: '是否逾期',
      dataIndex: 'isOverdue',
      width: 100,
      align: 'center',
      resizable: true,
    },
    {
      title: '逾期期数',
      dataIndex: 'orderOverdueCount',
      width: 100,
      align: 'center',
      resizable: true,
    },
    {
      title: '逾期金额',
      dataIndex: 'overdueAmount',
      width: 120,
      align: 'right',
      resizable: true,
    },
    {
      title: '绑定代扣信息',
      dataIndex: 'deductionInfo',
      width: 250,
      resizable: true,
    },
    {
      title: '门店名称',
      dataIndex: 'storeName',
      resizable: true,
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 120,
      fixed: 'right',
      resizable: true,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    // API标准字段名
    customerPhone: undefined,        // 用户手机号 (原userPhone)
    productName: undefined,          // 商品名称 (原goodsName) 
    afterSalesStatus: undefined,     // 售后状态 (原auditStatus)
    receivePaymentType: undefined,   // 收款方式 (原paymentMethod)
    invitationCode: undefined,       // 邀请码 (原inviteCode)
    overdueFlag: undefined,          // 是否逾期 (原isOverdue)
    cancelWithholding: undefined,    // 代扣解除 (API查询参数名，返回结果中对应deductBindFlag)
    
    // 日期范围字段 - 前端表单使用
    applyDateRange: undefined,       // 申请日期范围
    nextDeductionDateRange: undefined, // 最近应扣日期范围（对应latestDeductDate字段）
    
    // 日期范围相关 - 内部使用，提交时转换
    applyStartDate: undefined,       // 申请开始时间
    applyEndDate: undefined,         // 申请结束时间
    
    // 基础分页参数
    pageNum: 1,
    pageSize: 10,
    searchCount: true,
    sortItemList: [],
    
    // 门店信息
    storeName: [],                        // 门店名称查询字段
  };

  // 查询表单form
  const queryForm = reactive(_.cloneDeep(queryFormState));
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  // 详情弹窗相关
  const detailVisible = ref(false);
  const currentDetailData = ref({});

  // 审核弹窗相关
  const auditVisible = ref(false);
  const currentAuditData = ref({});

  function handleResizeColumn(w, col) {
    columns.value.forEach((item) => {
      if (item.dataIndex === col.dataIndex) {
        item.width = Math.floor(w);
        // 拖动宽度标识
        item.dragAndDropFlag = true;
      }
    });
  }

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, _.cloneDeep(queryFormState));
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  // 统一参数处理函数
  function prepareQueryParams() {
    const params = _.cloneDeep(queryForm);
    
    // 处理字符串类型参数：空值转null
    if (params.afterSalesStatus === '' || params.afterSalesStatus == null) {
      params.afterSalesStatus = null;
    }
    if (params.receivePaymentType === '' || params.receivePaymentType == null) {
      params.receivePaymentType = null;
    }
    
    // 处理Integer类型参数：空值转null，有值转数字
    if (params.overdueFlag !== '' && params.overdueFlag != null) {
      params.overdueFlag = parseInt(params.overdueFlag);
    } else {
      params.overdueFlag = null;
    }
    
    if (params.cancelWithholding !== '' && params.cancelWithholding != null) {
      params.cancelWithholding = parseInt(params.cancelWithholding);
    } else {
      params.cancelWithholding = null;
    }
    
    // 处理申请日期范围
    if (queryForm.applyDateRange && queryForm.applyDateRange.length === 2) {
      params.applyStartDate = queryForm.applyDateRange[0].format('YYYY-MM-DD');
      params.applyEndDate = queryForm.applyDateRange[1].format('YYYY-MM-DD');
    }
    
    // 处理最近应扣日期范围
    if (queryForm.nextDeductionDateRange && queryForm.nextDeductionDateRange.length === 2) {
      params.latestDeductStartDate = queryForm.nextDeductionDateRange[0].format('YYYY-MM-DD');
      params.latestDeductEndDate = queryForm.nextDeductionDateRange[1].format('YYYY-MM-DD');
    }
    
    // 处理门店名称查询参数（多选时逗号拼接门店ID）
    if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
      // 如果选择了门店，将ID数组用逗号拼接成字符串
      params.storeName = params.storeName.join(',');
    } else {
      // 如果没有选择门店，传 null
      params.storeName = null;
    }
    
    // 移除前端用的日期范围字段
    delete params.applyDateRange;
    delete params.nextDeductionDateRange;
    
    return params;
  }

  // 查询数据
  async function queryData() {
    try {
      tableLoading.value = true;
      
      // 使用统一的参数处理
      const requestParams = prepareQueryParams();
      
      const result = await cancellationRequestApi.queryList(requestParams);
      tableData.value = result.data.list || result.data;
      total.value = result.data.total || 0;
      
    } catch (error) {
      console.error('查询失败:', error);
      message.error('查询失败');
    } finally {
      tableLoading.value = false;
    }
  }

  // 查看详情
  async function viewDetail(record) {
    try {
      // 获取解约申请ID，优先使用API返回的ID字段
      const afterSalesId = record.afterSalesId || record.orderId;
      
      if (!afterSalesId) {
        message.error('无法获取解约申请ID');
        return;
      }
      
      SmartLoading.show();
      
      // 调用详情API
      const result = await cancellationRequestApi.getDetail(afterSalesId);
      currentDetailData.value = result.data;
      
      detailVisible.value = true;
      
    } catch (error) {
      console.error('获取详情失败:', error);
      message.error('获取详情失败');
    } finally {
      SmartLoading.hide();
    }
  }

  // 详情弹窗取消
  function handleDetailCancel() {
    detailVisible.value = false;
    currentDetailData.value = {};
  }

  // 审核申请
  function approveRequest(record) {
    // 构造审核数据
    currentAuditData.value = {
      ...record,
      applyTime: record.applyTime || '2025/06/17 13:00:00',
      terminateType: 'normal',
      applyReason: '这里是用户输入的申请原因这里是用户输入的申请原因这里是用户输入的申请原因',
      applyImages: [
        // 模拟申请图片数据，可能为空
        // { url: 'https://example.com/image1.jpg' },
        // { url: 'https://example.com/image2.jpg' }
      ],
    };
    
    auditVisible.value = true;
  }

  // 审核确认
  async function handleAuditConfirm(auditParams) {
    try {
      // 调用审核处理API
      await cancellationRequestApi.handle(auditParams);
      
      console.log('审核成功:', auditParams);
      
      message.success('审核成功');
      
      // 刷新列表
      queryData();
    } catch (error) {
      console.error('审核失败:', error);
      message.error('审核失败');
    }
  }

  // 审核弹窗取消
  function handleAuditCancel() {
    auditVisible.value = false;
    currentAuditData.value = {};
  }



  // 导出
  async function onExport() {
    message.info('导出功能开发中...');
  }

  // ---------------------------- 表格排序 ----------------------------

  function onChange(pagination, filters, sorter, { action }) {
    if (action === 'sort') {
      const { order, field } = sorter;
      let column = camelToUnderscore(field);
      let findIndex = queryForm.sortItemList.findIndex((e) => e.column === column);
      if (findIndex !== -1) {
        queryForm.sortItemList.splice(findIndex, 1);
      }
      if (order) {
        let isAsc = order !== 'ascend';
        queryForm.sortItemList.push({
          column,
          isAsc,
        });
      }
      queryData();
    }
  }

  function camelToUnderscore(str) {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase();
  }

  // ---------------------------- 动态高度 ----------------------------

  // 动态设置表格高度
  const yHeight = ref(0);

  onMounted(() => {
    queryData();
    resetGetHeight();
  });

  function resetGetHeight() {
    // 搜索部分高度
    let doc = document.querySelector('.ant-form');
    // 按钮部分高度
    let btn = document.querySelector('.smart-table-btn-block');
    // 表格头高度
    let tableCell = document.querySelector('.ant-table-cell');
    // 分页高度
    let page = document.querySelector('.smart-query-table-page');
    // 内容区总高度
    let box = document.querySelector('.admin-content');
    setTimeout(() => {
      let dueHeight = doc.offsetHeight + 10 + 24 + btn.offsetHeight + 15 + tableCell.offsetHeight + page.offsetHeight + 20;
      yHeight.value = box.offsetHeight - dueHeight;
    }, 100);
  }

  window.addEventListener(
    'resize',
    _.throttle(() => {
      resetGetHeight();
    }, 1000)
  );

  // 获取订单状态文本
  function getOrderStatusText(status) {
    const statusMap = {
      'WAIT_AUTHORIZ': '待授权',
      'EXECUTION_CONTRACT': '履约中', 
      'ENDED': '已结束',
      'CANCELED': '已取消',
      'TERMINATED': '已解约',
      'PAUSED': '已暂停'
    };
    return statusMap[status] || '未知';
  }

  // 获取售后状态文本
  function getCurrentStatusText(status) {
    const statusMap = {
      'WAIT_DEAL': '待审核',
      'PASS': '已通过', 
      'REJECT': '已拒绝',
      'CANCEL': '用户取消',
    };
    return statusMap[status] || '未知';
  }

  // 获取订单状态颜色
  function getOrderStatusColor(status) {
    const colorMap = {
      'WAIT_AUTHORIZ': 'default',   // 待授权 - 灰色
      'EXECUTION_CONTRACT': 'blue', // 履约中 - 蓝色
      'ENDED': 'green',             // 已结束 - 绿色
      'CANCELED': 'default',        // 已取消 - 灰色
      'TERMINATED': 'red',          // 已解约 - 红色
      'PAUSED': 'warning',          // 已暂停 - 黄色
    };
    return colorMap[status] || 'default';
  }

  // 获取售后状态颜色
  function getCurrentStatusColor(status) {
    const colorMap = {
      'WAIT_DEAL': 'blue',     // 待审核
      'PASS': 'success',       // 已通过
      'REJECT': 'error',       // 已拒绝
      'CANCEL': 'warning',     // 用户取消
    };
    return colorMap[status] || 'default';
  }

  // 只允许输入数字
  function onlyNumber(event) {
    // 允许数字、删除键、退格键
    const char = String.fromCharCode(event.which);
    if (!/[0-9]/.test(char) && event.which !== 8 && event.which !== 46) {
      event.preventDefault();
    }
  }

  // 格式化金额
  function formatMoney(value) {
    return dataFormatter.formatMoney(value);
  }

  // 格式化手机号
  function maskPhone(phone) {
    return dataFormatter.maskPhone(phone);
  }

  // 获取收款方式文本
  function getReceivePaymentTypeText(type) {
    const typeMap = {
      'BANK_SEGMENT': '账单付',
      'ZFB_ANXIN': '安心付',
    };
    return typeMap[type] || type || '-';
  }

  // 跳转到分期订单详情
  const goToSegmentOrderDetail = (orderId) => {
    router.push({
      path: '/order/segment-order-detail',
      query: { id: orderId }
    });
  };

  // 获取扣款周期文本
  function getDeductPeriodTypeText(type) {
    const typeMap = {
      'DEDUCT_BY_MONTH': '按月',
      'DEDUCT_BY_WEEK': '按周',
      'DEDUCT_BY_DAY': '按日',
      'DEDUCT_BY_QUARTER': '按季度',
      'DEDUCT_BY_YEAR': '按年',
    };
    return typeMap[type] || type || '-';
  }

  // 获取代扣方式文本
  function getDeductMethodDesc(value) {
    const enumMap = {
      'ANXIN_DEDUCT': '安心付代扣',
      'BANK_DEDUCT': '银行卡代扣',
      'NONE': '无'
    };
    return enumMap[value] || value || '-';
  }

  // 获取解绑来源文本
  function getDeductUnbindSourceDesc(value) {
    const enumMap = {
      'PLACE_ORDER_UNBIND': '下单未绑卡',
      'MERCHANT_UNBIND': '商家解绑'
    };
    return enumMap[value] || value || '-';
  }
</script>

<style lang="less" scoped>

  // .smart-query-form {
  //   margin-bottom: 16px;
  // }

  // .smart-query-form-btn {
  //   text-align: left;
  // }

  // .smart-query-table-page {
  //   margin-top: 16px;
  // }

  // .smart-table-btn-block {
  //   margin-bottom: 16px;
  // }

  .order-info-cell {
    line-height: 1.6;
  }

  .user-info-cell {
    line-height: 1.6;
  }

  .deduction-info-cell {
    line-height: 1.6;
  }

  .text-gray {
    color: inherit;
    font-size: inherit;
  }

  .order-link {
    color: #1890ff;
    cursor: pointer;
  }

  .order-link:hover {
    color: #40a9ff;
  }

  .invitation-info-cell {
    line-height: 1.6;
  }

  /* 筛选区域卡片圆角样式 */
  .smart-query-form {
    border-radius: 4px;
  }

</style> 