<!--
  * 分段订单列表
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2024-01-01 00:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="订单号" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.orderNo" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="用户姓名" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.customerName" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="用户手机号" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.customerPhone" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="下单日期" class="smart-query-form-item">
        <a-range-picker
          :value="[queryForm.createStartDate, queryForm.createEndDate]"
          @update:value="(dates) => { queryForm.createStartDate = dates?.[0] || null; queryForm.createEndDate = dates?.[1] || null; }"
          :placeholder="['开始日期', '结束日期']"
          style="width: 280px"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item label="解约时间" class="smart-query-form-item">
        <a-range-picker
          :value="[queryForm.auditTimeStart, queryForm.auditTimeEnd]"
          @update:value="(dates) => { queryForm.auditTimeStart = dates?.[0] || null; queryForm.auditTimeEnd = dates?.[1] || null; }"
          :placeholder="['开始日期', '结束日期']"
          style="width: 280px"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item label="订单状态" name="orderStatus" class="smart-query-form-item">
        <a-select v-model:value="queryForm.orderStatus" style="width: 120px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="WAIT_AUTHORIZ">待授权</a-select-option>
          <a-select-option value="EXECUTION_CONTRACT">履约中</a-select-option>
          <a-select-option value="ENDED">已结束</a-select-option>
          <a-select-option value="TERMINATED">已解约</a-select-option>
          <a-select-option value="PAUSED">已暂停</a-select-option>
          <a-select-option value="CANCELED">已取消</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="创建人" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.qrCreateBy" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="商品名称" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.productName" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="邀请码" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.invitationCode" placeholder="请输入" @keypress="onlyNumber" />
      </a-form-item>

      <a-form-item label="是否逾期" class="smart-query-form-item">
        <a-select v-model:value="queryForm.overdueFlag" style="width: 120px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="1">是</a-select-option>
          <a-select-option value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="收款方式" class="smart-query-form-item">
        <a-select v-model:value="queryForm.receivePaymentType" style="width: 120px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="BANK_SEGMENT">账单付</a-select-option>
          <a-select-option value="ZFB_ANXIN">安心付</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="代扣解除" class="smart-query-form-item">
        <a-select v-model:value="queryForm.cancelWithholding" style="width: 120px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="1">是</a-select-option>
          <a-select-option value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="代扣方式" class="smart-query-form-item">
        <a-select v-model:value="queryForm.deductMethod" style="width: 120px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="BANK_DEDUCT">银行卡代扣</a-select-option>
          <a-select-option value="ANXIN_DEDUCT">安心付代扣</a-select-option>
          <a-select-option value="NONE">无</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="代扣解绑日期" class="smart-query-form-item">
        <a-range-picker
          :value="[queryForm.removeDeductTimeStartDate, queryForm.removeDeductTimeEndDate]"
          @update:value="(dates) => { queryForm.removeDeductTimeStartDate = dates?.[0] || null; queryForm.removeDeductTimeEndDate = dates?.[1] || null; }"
          :placeholder="['开始日期', '结束日期']"
          style="width: 280px"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
        />
      </a-form-item>

      <a-form-item label="解绑来源" class="smart-query-form-item">
        <a-select v-model:value="queryForm.deductUnbindSource" style="width: 200px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="ORDER_UNBIND">下单未绑卡</a-select-option>
          <a-select-option value="MERCHANT_UNBIND">商家解绑</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="订单来源" class="smart-query-form-item">
        <a-select v-model:value="queryForm.appType" style="width: 150px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="MERCHANT">商家独立小程序</a-select-option>
          <a-select-option value="HUANLEFU">欢乐付小程序</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="是否有合同" class="smart-query-form-item">
        <a-select v-model:value="queryForm.contractExist" style="width: 120px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="1">是</a-select-option>
          <a-select-option value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="门店名称" class="smart-query-form-item">
        <StoreMultiSelect 
          v-model="queryForm.storeName" 
          width="200px"
          placeholder="请选择门店"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 统计信息 begin ----------->
    <div class="order-statistics">
      <div class="statistics-text">
        订单数：<strong class="statistics-number">{{ formatCount(statistics.totalCount) }}</strong>
        <a-divider type="vertical" />
        订单总金额：<strong class="statistics-number">{{ formatAmount(statistics.totalAmount) }}</strong>
        <a-divider type="vertical" />
        已扣金额：<strong class="statistics-number">{{ formatAmount(statistics.deductedAmount) }}</strong>
        <a-divider type="vertical" />
        未扣金额：<strong class="statistics-number">{{ formatAmount(statistics.pendingAmount) }}</strong>
        <a-divider type="vertical" />
        逾期金额：<strong class="statistics-number">{{ formatAmount(statistics.overdueAmount) }}</strong>
      </div>
    </div>
    <!---------- 统计信息 end ----------->

    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="onExportOrders" v-privilege="'business:segment-order-list:export'">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.ORDER.SEGMENT_ORDER" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="orderId"
      :scroll="{ x: 2000, y: 2000 }"
      bordered
      :pagination="false"
      :showSorterTooltip="false"
      @change="onChange"
      @resizeColumn="handleResizeColumn"
    >

      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'orderInfo'">
          <div class="order-info-cell">
            <div>商品名称：{{ record.productName || '-' }}</div>
            <div class="text-gray">订单编号：
              <a @click="goToSegmentOrderDetail(record.orderId)" class="order-link">
                {{ record.orderNo || '-' }}
              </a>
            </div>
            <div class="text-gray">创建人：{{ record.actualName || '-' }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'orderStatus'">
          <a-tag :color="getOrderStatusColor(text)">
            {{ getOrderStatusDesc(text) }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'userInfo'">
          <div class="user-info-cell">
            <div>{{ record.customerName }}</div>
            <div class="text-gray">{{ maskPhone(record.customerPhone) }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'invitationCode'">
          <div class="invitation-info-cell">
            <div>邀请码：{{ record.invitationCode || '-' }}</div>
            <div>员工：{{ record.employeeName || '-' }}</div>
            <div>部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'orderAmount'">
          {{ formatMoney(text) }}
        </template>
        <template v-if="column.dataIndex === 'deductPeriodType'">
          <span>{{ getDeductPeriodTypeDesc(text) }}</span>
        </template>
        <template v-if="column.dataIndex === 'forepartInstallmentCount'">
          <span>{{ text || '-' }}</span>
        </template>
        <template v-if="column.dataIndex === 'forepartEachAmount'">
          {{ formatMoney(text) }}
        </template>
        <template v-if="column.dataIndex === 'backendEachAmount'">
          {{ formatMoney(text) }}
        </template>
        <template v-if="column.dataIndex === 'deductedCount'">
          <span>{{ text || '-' }}</span>
        </template>
        <template v-if="column.dataIndex === 'deductedAmount'">
          {{ formatMoney(text) }}
        </template>
        <template v-if="column.dataIndex === 'remainAmount'">
          {{ formatMoney(text) }}
        </template>
        <template v-if="column.dataIndex === 'overdueFlag'">
          <a-tag :color="text === 1 ? 'error' : 'success'">
            {{ text === 1 ? '是' : '否' }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'overdueAmount'">
          {{ formatMoney(text) }}
        </template>
        <template v-if="column.dataIndex === 'receivePaymentType'">
          <span>{{ getReceivePaymentTypeDesc(text) }}</span>
        </template>
        <template v-if="column.dataIndex === 'deductionInfo'">
          <div class="deduction-info-cell">
            <div>当前代扣方式：{{ getDeductMethodDesc(record.deductMethod) }}</div>
            <div>代扣是否解绑：{{ record.deductUnbindFlag ? '是' : '否' }}</div>
            <div>解绑时间：{{ record.deductUnbindTime || '-' }}</div>
            <div>解绑来源：{{ getDeductUnbindSourceDesc(record.deductUnbindSource) || '-' }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'appType'">
          <span>{{ getAppTypeDesc(text) }}</span>
        </template>
        <template v-if="column.dataIndex === 'remark'">
          <span>{{ text ? text : '' }}</span>
        </template>
        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ text || '-' }}</span>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <div class="smart-table-operate operation-buttons">
            <a-button @click="viewOrderDetail(record)" type="link" v-privilege="'business:segment-order-list:viewOrderDetail'">详情</a-button>
            <a-button v-privilege="'business:segment-order-list:downloadContract'"
              v-if="record.electronicContractFlag === 1" 
              @click="downloadContract(record)" 
              type="link"
            >下载合同</a-button>
            <a-button v-privilege="'business:segment-order-list:confirmTerminate'"
              v-if="record.orderStatus === 'EXECUTION_CONTRACT'" 
              @click="confirmTerminate(record)" 
              type="link"
            >解约</a-button>
            <a-button v-privilege="'business:segment-order-list:showTerminationPaymentQrModal'"
              v-if="record.orderStatus === 'EXECUTION_CONTRACT'" 
              @click="showTerminationPaymentQrModal(record)" 
              type="link"
            >解约收款码</a-button>
            <a-button v-privilege="'business:segment-order-list:showRemarkModal'"
             @click="showRemarkModal(record)" type="link">备注</a-button>
            <a-button 
              v-privilege="'business:segment-order-list:showDelayDeductModal'"
              v-if="record.orderStatus === 'EXECUTION_CONTRACT'" 
              @click="showDelayDeductModal(record)" 
              type="link"
            >延期</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>

  <!-- 解约弹窗 -->
  <TerminateOrderModal
    v-model:visible="terminateModalVisible"
    :order-info="currentOrderInfo"
    @confirm="handleTerminateConfirm"
    @cancel="handleTerminateCancel"
  />

  <!-- 备注弹窗 -->
  <RemarkModal
    v-model:visible="remarkModalVisible"
    :order-info="currentRemarkOrderInfo"
    @confirm="handleRemarkConfirm"
    @cancel="handleRemarkCancel"
  />

  <!-- 延期扣款弹窗 -->
  <DelayDeductModal
    v-model:visible="delayDeductModalVisible"
    :order-info="currentDelayDeductOrderInfo"
    @confirm="handleDelayDeductConfirm"
    @cancel="handleDelayDeductCancel"
  />

  <!-- 解约收款码弹窗 -->
  <TerminationPaymentQrModal
    ref="terminationPaymentQrModalRef"
  />
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { segmentOrderApi } from '/@/api/business/order/segment-order-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';

  import { ORDER_PERMISSION_CODES } from '/@/constants/business/order/order-const';
  import { SearchOutlined, ReloadOutlined, ExportOutlined } from '@ant-design/icons-vue';
  import { dataFormatter } from '/@/utils/data-formatter';
  import _ from 'lodash';
  import TerminateOrderModal from './components/terminate-order-modal.vue';
  import RemarkModal from './components/remark-modal.vue';
  import DelayDeductModal from './components/delay-deduct-modal.vue';
  import TerminationPaymentQrModal from './components/termination-payment-qr-modal.vue';
  import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';

  // ---------------------------- 路由实例 ----------------------------
  const router = useRouter();

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '创建时间',
      dataIndex: 'createTime',
      resizable: true,
      width: 170,
    },
    {
      title: '订单信息',
      dataIndex: 'orderInfo',
      width: 300,
      resizable: true,
    },
    {
      title: '状态',
      dataIndex: 'orderStatus',
      resizable: true,
      width: 100,
    },
    {
      title: '用户信息',
      dataIndex: 'userInfo',
      resizable: true,
      width: 150,
    },
    {
      title: '邀请码',
      dataIndex: 'invitationCode',
      resizable: true,
      width: 200,
    },
    {
      title: '订单总金额',
      dataIndex: 'orderAmount',
      resizable: true,
      width: 120,
    },
    {
      title: '分期数',
      dataIndex: 'totalInstallmentCount',
      resizable: true,
      width: 80,
    },
    {
      title: '扣款周期',
      dataIndex: 'deductPeriodType',
      resizable: true,
      width: 100,
    },
    {
      title: '前段期数',
      dataIndex: 'forepartInstallmentCount',
      resizable: true,
      width: 100,
    },
    {
      title: '前段每期金额',
      dataIndex: 'forepartEachAmount',
      resizable: true,
      width: 120,
    },
    {
      title: '后段每期金额',
      dataIndex: 'backendEachAmount',
      resizable: true,
      width: 120,
    },
    {
      title: '已扣金额',
      dataIndex: 'deductedAmount',
      resizable: true,
      width: 100,
    },
    {
      title: '已扣期数',
      dataIndex: 'deductedCount',
      resizable: true,
      width: 100,
    },
    {
      title: '未扣金额',
      dataIndex: 'remainAmount',
      resizable: true,
      width: 100,
    },
    {
      title: '是否逾期',
      dataIndex: 'overdueFlag',
      resizable: true,
      width: 100,
    },
    {
      title: '逾期金额',
      dataIndex: 'overdueAmount',
      resizable: true,
      width: 100,
    },
    {
      title: '收款方式',
      dataIndex: 'receivePaymentType',
      resizable: true,
      width: 120,
    },
    {
      title: '绑定代扣信息',
      dataIndex: 'deductionInfo',
      resizable: true,
      width: 250,
    },
    {
      title: '来源',
      dataIndex: 'appType',
      resizable: true,
      width: 100,
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      resizable: true,
      width: 150,
    },
    {
      title: '门店名称',
      dataIndex: 'storeName',
      resizable: true,
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 220,
      fixed: 'right',
      resizable: true,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  // 获取当月1号和今天的日期
  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
  
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };
  
  const defaultOrderDateRange = [
    formatDate(firstDayOfMonth),
    formatDate(today)
  ];

  const queryFormState = {
    // 基础分页参数
    pageNum: 1,                           // 页码
    pageSize: 10,                         // 每页数量
    searchCount: true,                    // 是否查询总条数
    sortItemList: [],                     // 排序字段集合
    
    // 订单基本信息
    orderNo: null,                        // 订单编号
    customerName: null,                   // 用户姓名
    customerPhone: null,                  // 用户手机号
    orderStatus: null,                    // 订单状态: [ALL:所有; CANCELED:已取消; WAIT_AUTHORIZ:待授权; EXECUTION_CONTRACT:履约中; ENDED:已结束; TERMINATED:已解约; PAUSED:已暂停]
    
    // 时间范围查询
    createStartDate: formatDate(firstDayOfMonth),  // 下单开始时间
    createEndDate: formatDate(today),              // 下单结束时间
    auditTimeStart: null,                 // 审核时间开始
    auditTimeEnd: null,                   // 审核时间结束
    
    // 订单详细信息
    qrCreateBy: null,                     // 创建人
    productName: null,                    // 商品名称
    invitationCode: null,                 // 邀请码
    appType: null,                        // 订单来源: [MERCHANT:商家独立小程序, HUANLEFU:欢乐付小程序]
    
    // 状态标记
    overdueFlag: null,                    // 是否逾期: [0:否, 1:是]
    contractExist: null,                  // 是否有合同: [0:否, 1:是]
    
    // 代扣相关
    cancelWithholding: null,              // 代扣解除: [0:否, 1:是]
    removeDeductTimeStartDate: null,      // 代扣解绑开始时间
    removeDeductTimeEndDate: null,        // 代扣解绑结束时间
    deductUnbindSource: null,             // 解绑来源: [1:下单未绑卡, 2:商家解绑]
    
    // 收款和代扣方式
    receivePaymentType: null,             // 收款方式: [ZFB_ANXIN:安心付, BANK_SEGMENT:账单付]
    deductMethod: null,                   // 代扣方式: [ANXIN_DEDUCT:安心付代扣, BANK_DEDUCT:银行卡代扣, NONE:无]
    
    // 门店信息
          storeName: [],                        // 门店名称查询字段
  };

  // 查询表单form
  const queryForm = reactive(_.cloneDeep(queryFormState));
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);
  // 已删除批量选择功能
  // 统计数据
  const statistics = ref({
    totalCount: 0,
    totalAmount: 0,
    deductedAmount: 0,
    pendingAmount: 0,
    overdueAmount: 0,
  });

  // 解约弹窗相关
  const terminateModalVisible = ref(false);
  const currentOrderInfo = ref({});
  
  // 备注弹窗相关
  const remarkModalVisible = ref(false);
  const currentRemarkOrderInfo = ref({});
  
  // 延期扣款弹窗相关
  const delayDeductModalVisible = ref(false);
  const currentDelayDeductOrderInfo = ref({});
  
  // 解约收款码弹窗相关
  const terminationPaymentQrModalRef = ref(null);

  function handleResizeColumn(w, col) {
    columns.value.forEach((item) => {
      if (item.dataIndex === col.dataIndex) {
        item.width = Math.floor(w);
        // 拖动宽度标识
        item.dragAndDropFlag = true;
      }
    });
  }

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, _.cloneDeep(queryFormState));
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      // 使用统一的参数处理函数
      const params = prepareQueryParams();

      // 并行调用两个接口：列表数据和统计数据
      const [listResult, statisticsResult] = await Promise.all([
        segmentOrderApi.querySegmentOrderList(params),     // 调用 /orderInstallment/queryPage
        segmentOrderApi.querySegmentOrderStatistics(params) // 调用 /orderInstallment/queryPageTotal
      ]);

      // 处理列表数据
      tableData.value = listResult.data.list;
      total.value = listResult.data.total;
      
      // 处理统计数据
      statistics.value = {
        totalCount: statisticsResult.data.orderCount || 0,
        totalAmount: parseFloat(statisticsResult.data.totalOrderAmount || '0'),
        deductedAmount: parseFloat(statisticsResult.data.totalDeductedAmount || '0'),
        pendingAmount: parseFloat(statisticsResult.data.totalRemainAmount || '0'),
        overdueAmount: parseFloat(statisticsResult.data.totalOverdueAmount || '0'),
      };
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }



  onMounted(queryData);

  // ---------------------------- 参数处理方法 ----------------------------
  
  /**
   * 统一处理查询参数 - 确保所有下拉框选择"全部"时传null
   */
  function prepareQueryParams() {
    const params = { ...queryForm };
    
    // 字符串类型参数 - 空值转换为null
    if (params.orderStatus === null || params.orderStatus === undefined || params.orderStatus === '') {
      params.orderStatus = null;
    }
    
    if (params.receivePaymentType === null || params.receivePaymentType === undefined || params.receivePaymentType === '') {
      params.receivePaymentType = null;
    }
    
    if (params.deductMethod === null || params.deductMethod === undefined || params.deductMethod === '') {
      params.deductMethod = null;
    }
    
    // Integer类型参数 - 有值时转换为数字，空值保持null
    if (params.overdueFlag !== null && params.overdueFlag !== undefined && params.overdueFlag !== '') {
      params.overdueFlag = parseInt(params.overdueFlag);
    } else {
      params.overdueFlag = null;
    }
    
    if (params.cancelWithholding !== null && params.cancelWithholding !== undefined && params.cancelWithholding !== '') {
      params.cancelWithholding = parseInt(params.cancelWithholding);
    } else {
      params.cancelWithholding = null;
    }
    
    if (params.deductUnbindSource === null || params.deductUnbindSource === undefined || params.deductUnbindSource === '') {
      params.deductUnbindSource = null;
    }
    
    if (params.appType === null || params.appType === undefined || params.appType === '') {
      params.appType = null;
    }
    
    if (params.contractExist !== null && params.contractExist !== undefined && params.contractExist !== '') {
      params.contractExist = parseInt(params.contractExist);
    } else {
      params.contractExist = null;
    }
    
    // 处理门店名称查询参数（多选时逗号拼接门店ID）
    if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
      // 如果选择了门店，将ID数组用逗号拼接成字符串
      params.storeName = params.storeName.join(',');
    } else {
      // 如果没有选择门店，传 null
      params.storeName = null;
    }
    
    return params;
  }

  // ---------------------------- 操作方法 ----------------------------

  // 已删除批量选择功能



  // 已删除批量删除功能

  // 跳转到分期订单详情
  const goToSegmentOrderDetail = (orderId) => {
    router.push({
      path: '/order/segment-order-detail',
      query: { id: orderId }
    });
  };

  // 查看订单详情
  function viewOrderDetail(record) {
    router.push({
      path: '/order/segment-order-detail',
      query: { id: record.orderId },
    });
  }

  // 预览合同
  async function downloadContract(record) {
    try {
      SmartLoading.show();
      // 获取合同预览链接
      const response = await segmentOrderApi.getContractPreviewUrl(record.orderId);
      if (response.data) {
        // 在新标签页中打开PDF预览
        window.open(response.data, '_blank');
      } else {
        message.error('获取合同预览链接失败');
      }
    } catch (e) {
      smartSentry.captureError(e);
      message.error('获取合同预览链接失败');
    } finally {
      SmartLoading.hide();
    }
  }

  // 显示解约弹窗
  function confirmTerminate(record) {
    currentOrderInfo.value = {
      orderId: record.orderId,
      orderNo: record.orderNo,
      createTime: record.createTime,
      productName: record.productName,
      orderAmount: record.orderAmount,
      customerPhone: record.customerPhone,
      customerName: record.customerName,
    };
    terminateModalVisible.value = true;
  }

  // 解约订单确认
  async function handleTerminateConfirm(terminateResult) {
    // 弹窗组件内部已经处理了API调用和成功提示
    // 这里只需要处理成功后的操作
    if (terminateResult.success) {
      // 刷新列表数据
      queryData();
    }
  }

  // 解约弹窗取消
  function handleTerminateCancel() {
    terminateModalVisible.value = false;
  }

  // 显示备注弹窗
  function showRemarkModal(record) {
    currentRemarkOrderInfo.value = {
      orderId: record.orderId,
      orderNo: record.orderNo,
      remark: record.remark,
    };
    remarkModalVisible.value = true;
  }

  // 备注确认
  async function handleRemarkConfirm(remarkResult) {
    // 弹窗组件内部已经处理了API调用和成功提示
    // 这里只需要处理成功后的操作
    if (remarkResult.success) {
      // 刷新列表数据
      queryData();
    }
  }

  // 备注弹窗取消
  function handleRemarkCancel() {
    remarkModalVisible.value = false;
  }

  // 显示延期扣款弹窗
  function showDelayDeductModal(record) {
    currentDelayDeductOrderInfo.value = {
      orderId: record.orderId,
      orderNo: record.orderNo,
      createTime: record.createTime,
      productName: record.productName,
      totalAmount: record.orderAmount,
      userPhone: record.customerPhone,
      userName: record.customerName,
    };
    delayDeductModalVisible.value = true;
  }

  // 延期扣款确认
  async function handleDelayDeductConfirm(delayResult) {
    // 弹窗组件内部已经处理了API调用和成功提示
    // 这里只需要处理成功后的操作
    if (delayResult.success) {
      // 刷新列表数据
      queryData();
    }
  }

  // 延期扣款弹窗取消
  function handleDelayDeductCancel() {
    delayDeductModalVisible.value = false;
  }

  // 显示解约收款码弹窗
  function showTerminationPaymentQrModal(record) {
    if (terminationPaymentQrModalRef.value) {
      terminationPaymentQrModalRef.value.show({
        orderId: record.orderId,
        orderNo: record.orderNo,
        customerName: record.customerName,
        customerPhone: record.customerPhone,
      });
    }
  }

  // 导出订单
  async function onExportOrders() {
    message.info('导出功能开发中...');
  }

  function onChange(pagination, filters, sorter, { action }) {
    if (action === 'sort') {
      const { order, field } = sorter;
      let column = camelToUnderscore(field);
      let findIndex = queryForm.sortItemList.findIndex((e) => e.column === column);
      if (findIndex !== -1) {
        queryForm.sortItemList.splice(findIndex, 1);
      }
      if (order) {
        let isAsc = order !== 'ascend';
        queryForm.sortItemList.push({
          column,
          isAsc,
        });
      }
      queryData();
    }
  }

  function camelToUnderscore(str) {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase();
  }

  // 动态设置表格高度
  const yHeight = ref(0);
  onMounted(() => {
    resetGetHeight();
  });

  function resetGetHeight() {
    // 搜索部分高度
    let doc = document.querySelector('.ant-form');
    // 按钮部分高度
    let btn = document.querySelector('.smart-table-btn-block');
    // 表格头高度
    let tableCell = document.querySelector('.ant-table-cell');
    // 分页高度
    let page = document.querySelector('.smart-query-table-page');
    // 内容区总高度
    let box = document.querySelector('.admin-content');
    setTimeout(() => {
      let dueHeight = doc.offsetHeight + 10 + 24 + btn.offsetHeight + 15 + tableCell.offsetHeight + page.offsetHeight + 20 + 50; // 加50给统计信息
      yHeight.value = box.offsetHeight - dueHeight;
    }, 100);
  }

  window.addEventListener(
    'resize',
    _.throttle(() => {
      resetGetHeight();
    }, 1000)
  );

  // 只允许输入数字
  function onlyNumber(event) {
    // 允许数字、删除键、退格键
    const char = String.fromCharCode(event.which);
    if (!/[0-9]/.test(char) && event.which !== 8 && event.which !== 46) {
      event.preventDefault();
    }
  }

  function formatMoney(value) {
    return dataFormatter.formatMoney(value);
  }

  // 格式化统计数字，添加千分位
  function formatAmount(amount) {
    if (!amount && amount !== 0) return '0.00';
    
    // 统一显示两位小数（金额格式）
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  // 格式化订单数量（不显示小数位）
  function formatCount(count) {
    if (!count && count !== 0) return '0';
    return Number(count).toLocaleString('zh-CN');
  }

  function maskPhone(phone) {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  // 枚举转换函数
  function getOrderStatusDesc(value) {
    const enumMap = {
      'WAIT_AUTHORIZ': '待授权',
      'EXECUTION_CONTRACT': '履约中',
      'ENDED': '已结束',
      'TERMINATED': '已解约',
      'PAUSED': '已暂停',
      'CANCELED': '已取消'
    };
    return enumMap[value] || value || '-';
  }

  function getDeductPeriodTypeDesc(value) {
    const enumMap = {
      'DEDUCT_BY_MONTH': '按月',
      'DEDUCT_BY_WEEK': '按周',
      'DEDUCT_BY_DAY': '按日',
      'DEDUCT_BY_QUARTER': '按季度',
      'DEDUCT_BY_YEAR': '按年'
    };
    return enumMap[value] || value || '-';
  }

  function getReceivePaymentTypeDesc(value) {
    const enumMap = {
      'ZFB_ANXIN': '安心付',
      'BANK_SEGMENT': '账单付'
    };
    return enumMap[value] || value || '-';
  }

  function getDeductMethodDesc(value) {
    const enumMap = {
      'ANXIN_DEDUCT': '安心付代扣',
      'BANK_DEDUCT': '银行卡代扣',
      'NONE': '无'
    };
    return enumMap[value] || value || '-';
  }

  function getDeductUnbindSourceDesc(value) {
    const enumMap = {
      'PLACE_ORDER_UNBIND': '下单未绑卡',
      'MERCHANT_UNBIND': '商家解绑'
    };
    return enumMap[value] || value || '-';
  }

  function getAppTypeDesc(value) {
    const enumMap = {
      'MERCHANT': '商家独立小程序',
      'HUANLEFU': '欢乐付小程序'
    };
    return enumMap[value] || value || '-';
  }

  // 获取订单状态颜色
  function getOrderStatusColor(status) {
    const colorMap = {
      'WAIT_AUTHORIZ': 'default',   // 待授权 - 灰色
      'EXECUTION_CONTRACT': 'blue', // 履约中 - 蓝色
      'ENDED': 'green',             // 已结束 - 绿色
      'CANCELED': 'default',        // 已取消 - 灰色
      'TERMINATED': 'red',          // 已解约 - 红色
      'PAUSED': 'warning',          // 已暂停 - 黄色
    };
    return colorMap[status] || 'default';
  }
</script> 

<style scoped>
.order-info-cell {
  line-height: 1.6;
}

.user-info-cell {
  line-height: 1.6;
}

.deduction-info-cell {
  line-height: 1.6;
}

.text-gray {
  color: inherit;
  font-size: inherit;
}

.order-statistics {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.statistics-text {
  font-size: inherit;
  color: inherit;
  line-height: 1.5;
}

.operation-buttons {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  white-space: nowrap;
  align-items: center;
  gap: 8px;
  line-height: 1.2;
}

.order-link {
  color: #1890ff;
  cursor: pointer;
}

.order-link:hover {
  color: #40a9ff;
}

.statistics-number {
  color: #1890ff;
}

.invitation-info-cell {
  line-height: 1.6;
}

/* 筛选区域卡片圆角样式 */
.smart-query-form {
  border-radius: 4px;
}

</style> 