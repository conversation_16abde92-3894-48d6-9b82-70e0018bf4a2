<!--
  * 订单备注弹窗
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2024-01-01 00:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-modal
    :open="visible"
    title="备注"
    width="600px"
    :mask-closable="false"
    :keyboard="false"
    @cancel="handleCancel"
    centered
  >
    <div style="padding: 20px 0 10px 0;">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
      >
        <a-form-item name="remark" label="备注内容">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入备注"  
            :maxlength="100"
            :rows="4"
            show-count
          />
        </a-form-item>
      </a-form>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { segmentOrderApi } from '/@/api/business/order/segment-order-api';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderInfo: {
    type: Object,
    default: () => ({
      orderId: '',
      orderNo: '',
      remark: '',
    }),
  },
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 表单引用
const formRef = ref();
const confirmLoading = ref(false);

// 表单数据
const formData = reactive({
  remark: '',
});

// 表单验证规则
const rules = {
  remark: [
    { required: true, message: '请输入备注内容', trigger: 'blur' },
    { min: 1, max: 100, message: '备注内容长度为1-100个字符', trigger: 'blur' },
  ],
};

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 监听订单信息变化，更新表单数据
watch(() => props.orderInfo, (newVal) => {
  if (newVal && props.visible) {
    formData.remark = newVal.remark || '';
  }
}, { deep: true });

// 重置表单
function resetForm() {
  formData.remark = props.orderInfo?.remark || '';
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 取消操作
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 确认操作
async function handleConfirm() {
  try {
    await formRef.value.validate();
    
    confirmLoading.value = true;
    
    // 调用备注API
    const response = await segmentOrderApi.addRemark(props.orderInfo.orderId, {
      remark: formData.remark,
    });
    
    if (response.ok) {
      message.success('提交成功');
      
      // 触发确认事件，传递备注结果
      emit('confirm', {
        success: true,
        orderId: props.orderInfo.orderId,
        remark: formData.remark,
      });
      
      // 关闭弹窗
      emit('update:visible', false);
    } else {
      message.error(response.msg || '备注提交失败');
    }
    
  } catch (error) {
    console.error('备注提交失败:', error);
    if (error.errorFields) {
      message.error('请完善必填信息');
    } else {
      message.error('备注提交失败：' + (error.message || '网络错误'));
    }
  } finally {
    confirmLoading.value = false;
  }
}
</script>

<style scoped lang="less">
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 8px;
}
</style> 