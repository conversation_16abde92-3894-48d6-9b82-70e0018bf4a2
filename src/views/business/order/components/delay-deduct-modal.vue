<!--
  * 延期扣款弹窗
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2024-01-01 00:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-modal
    :open="visible"
    title="延期扣款"
    width="800px"
    :mask-closable="false"
    :keyboard="false"
    @cancel="handleCancel"
    centered
  >
    <div style="padding: 20px 0 10px 0;">
      <div class="delay-modal-content">
        <!-- 延期信息 -->
        <div class="section">
          <h3 class="section-title">延期信息</h3>
          
          <div class="delay-info">
            <a-row :gutter="[24, 16]">
              <a-col :span="12">
                <div class="info-item">
                  <span class="label">近期扣款日期：</span>
                  <span class="value">{{ deductDayInfo.latestDeductDay || '-' }}</span>
                </div>
              </a-col>
            </a-row>
          </div>

          <a-form
            ref="formRef"
            :model="formData"
            :rules="rules"
            layout="vertical"
            class="delay-form"
          >
            <a-row :gutter="24">
              <a-col :span="12">
                <a-form-item name="delayDay" label="延期天数">
                  <a-input-number
                    v-model:value="formData.delayDay"
                    placeholder="请输入延期天数"
                    :min="1"
                    :max="180"
                    style="width: 100%"
                    @blur="handleDelayDayBlur"
                    @change="calculateDelayedDate"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="延期后日期">
                  <div class="delayed-date-display">{{ delayedDate || '-' }}</div>
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>

          <div class="delay-tip">
            <span style="color: #1890ff;">*提示：</span>
            <span style="color: #1890ff;">
              延期后，所有未扣款期数的应扣日期将进行顺延（如本期应扣日期为6月1日，延期10天，则未来每期扣款日期将顺延到每月11日，
              延期后，将不可再恢复，请谨慎操作
            </span>
          </div>
        </div>

        <!-- 订单信息 -->
        <div class="section">
          <h3 class="section-title">订单信息</h3>
          <div class="order-info">
            <a-row :gutter="[24, 16]">
              <a-col :span="12">
                <div class="info-item">
                  <span class="label">订单编号：</span>
                  <span class="value long-text" :title="orderInfo.orderNo">{{ orderInfo.orderNo || '-' }}</span>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="info-item">
                  <span class="label">创建时间：</span>
                  <span class="value">{{ orderInfo.createTime || '-' }}</span>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="info-item">
                  <span class="label">商品名称：</span>
                  <span class="value long-text" :title="orderInfo.productName">{{ orderInfo.productName || '-' }}</span>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="info-item">
                  <span class="label">订单金额：</span>
                  <span class="value">{{ formatMoney(orderInfo.totalAmount) || '-' }}</span>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="info-item">
                  <span class="label">用户手机号：</span>
                  <span class="value">{{ maskPhone(orderInfo.userPhone) || '-' }}</span>
                </div>
              </a-col>
              <a-col :span="12">
                <div class="info-item">
                  <span class="label">用户名称：</span>
                  <span class="value">{{ orderInfo.userName || '-' }}</span>
                </div>
              </a-col>
            </a-row>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import { segmentOrderApi } from '/@/api/business/order/segment-order-api';
import { dataFormatter } from '/@/utils/data-formatter';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderInfo: {
    type: Object,
    default: () => ({
      orderId: '',
      orderNo: '',
      createTime: '',
      productName: '',
      totalAmount: 0,
      userPhone: '',
      userName: '',
    }),
  },
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 表单引用
const formRef = ref();
const confirmLoading = ref(false);

// 扣款日期信息
const deductDayInfo = ref({
  installmentNo: null,
  latestDeductDay: '',
});

// 表单数据
const formData = reactive({
  delayDay: null,
});

// 延期后日期
const delayedDate = ref('');

// 表单验证规则
const rules = {
  delayDay: [
    { required: true, message: '请输入延期天数', trigger: 'blur' },
    { type: 'number', min: 1, max: 180, message: '延期天数必须在1-180之间', trigger: 'blur' },
  ],
};

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
    loadDeductDayInfo();
  }
});

// 重置表单
function resetForm() {
  formData.delayDay = null;
  delayedDate.value = '';
  deductDayInfo.value = {
    installmentNo: null,
    latestDeductDay: '',
  };
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 加载扣款日期信息
async function loadDeductDayInfo() {
  if (!props.orderInfo.orderId) return;
  
  try {
    const response = await segmentOrderApi.getDeductDay(props.orderInfo.orderId);
    if (response.ok && response.data) {
      deductDayInfo.value = response.data;
    }
  } catch (error) {
    console.error('获取扣款日期失败:', error);
    message.error('获取扣款日期失败');
  }
}

// 处理延期天数失焦事件，验证范围
function handleDelayDayBlur() {
  if (formData.delayDay !== null) {
    if (formData.delayDay < 1 || formData.delayDay > 180) {
      formData.delayDay = null;
      delayedDate.value = '';
      message.warning('延期天数必须在1-180之间');
    }
  }
}

// 计算延期后日期
function calculateDelayedDate() {
  if (formData.delayDay && deductDayInfo.value.latestDeductDay) {
    const latestDate = new Date(deductDayInfo.value.latestDeductDay);
    const delayedDateObj = new Date(latestDate);
    delayedDateObj.setDate(latestDate.getDate() + formData.delayDay);
    
    // 格式化为 YYYY/MM/DD
    const year = delayedDateObj.getFullYear();
    const month = String(delayedDateObj.getMonth() + 1).padStart(2, '0');
    const day = String(delayedDateObj.getDate()).padStart(2, '0');
    delayedDate.value = `${year}/${month}/${day}`;
  } else {
    delayedDate.value = '';
  }
}

// 取消操作
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 确认操作
async function handleConfirm() {
  try {
    await formRef.value.validate();
    
    confirmLoading.value = true;
    
    // 调用延期扣款API
    const params = {
      installmentNo: deductDayInfo.value.installmentNo,
      delayDay: formData.delayDay,
    };
    
    const response = await segmentOrderApi.delayDeduct(props.orderInfo.orderId, params);
    
    if (response.ok) {
      message.success('延期成功');
      
      // 触发确认事件
      emit('confirm', {
        success: true,
        orderId: props.orderInfo.orderId,
        delayDay: formData.delayDay,
        delayedDate: delayedDate.value,
      });
      
      // 关闭弹窗
      emit('update:visible', false);
    } else {
      message.error(response.msg || '延期失败');
    }
    
  } catch (error) {
    console.error('延期失败:', error);
    if (error.errorFields) {
      message.error('请完善必填信息');
    } else {
      message.error('延期失败：' + (error.message || '网络错误'));
    }
  } finally {
    confirmLoading.value = false;
  }
}

// 格式化金额
function formatMoney(value) {
  return dataFormatter.formatMoney(value);
}

// 手机号脱敏
function maskPhone(phone) {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}
</script>

<style scoped lang="less">
.delay-modal-content {
  .section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 16px;
      border-left: 4px solid #1890ff;
      padding-left: 12px;
    }
  }
  
  .delay-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
    
    .delayed-date-display {
      padding: 6px 11px;
      background-color: #f5f5f5;
      border: 1px solid #d9d9d9;
      border-radius: 6px;
      color: #262626;
      font-size: 14px;
      font-weight: 500;
      min-height: 32px;
      display: flex;
      align-items: center;
    }
  }
  
  .delay-info {
    background: #fafafa;
    padding: 16px;
    border-radius: 6px;
    margin-bottom: 16px;
    
    .info-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 8px;
      
      .label {
        color: #666;
        font-size: 14px;
        min-width: 120px;
        flex-shrink: 0;
        padding-top: 1px;
      }
      
      .value {
        color: #262626;
        font-size: 14px;
        font-weight: 500;
        flex: 1;
        word-break: break-all;
        line-height: 1.4;
      }
    }
  }
  
  .order-info {
    background: #fafafa;
    padding: 16px;
    border-radius: 6px;
    
    .info-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 8px;
      
      .label {
        color: #666;
        font-size: 14px;
        min-width: 85px;
        flex-shrink: 0;
        padding-top: 1px;
      }
      
      .value {
        color: #262626;
        font-size: 14px;
        font-weight: 500;
        flex: 1;
        word-break: break-all;
        line-height: 1.4;
        
        &.long-text {
          max-width: calc(100% - 85px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: help;
        }
      }
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 8px;
}

.delay-tip {
  margin: 16px 0 0 0;
  padding: 12px;
  background-color: #e6f7ff;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.5;
}
</style> 