<!--
  * 线下收款弹窗
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 10:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-modal
    :open="visible"
    title="线下收款"
    width="600px"
    :mask-closable="false"
    :keyboard="false"
    @cancel="handleCancel"
  >
    <div class="offline-payment-content">
      <!-- 提示信息 -->
      <div class="notice-text">
        线下收款在「收支明细」也会产生记录，同时账户余额也会正常扣取平台手续费
      </div>

      <!-- 表单 -->
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        class="offline-payment-form"
      >
        <a-form-item name="installmentDeductId" label="结算期数">
          <a-select 
            v-model:value="formData.installmentDeductId" 
            placeholder="请选择"
            :options="periodOptions"
          />
        </a-form-item>
        
        <a-form-item name="remark" label="备注">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入"
            :maxlength="100"
            :rows="4"
            show-count
          />
          <div class="remark-tip">
            在「收支明细」备注字段可见
          </div>
        </a-form-item>
      </a-form>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { message } from 'ant-design-vue';
import { segmentOrderApi } from '/@/api/business/order/segment-order-api';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderInfo: {
    type: Object,
    default: () => ({
      orderId: '',
    }),
  },
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 表单引用
const formRef = ref();
const confirmLoading = ref(false);

// 可选期数列表
const installmentOptions = ref([]);

// 表单数据
const formData = reactive({
  installmentDeductId: undefined,
  remark: '',
});

// 表单验证规则
const rules = {
  installmentDeductId: [
    { required: true, message: '请选择结算期数', trigger: 'change' },
  ],
  remark: [
    { required: true, message: '请输入备注', trigger: 'blur' },
    { min: 1, max: 100, message: '备注长度为1-100个字符', trigger: 'blur' },
  ],
};

// 计算可选择的期数选项
const periodOptions = computed(() => {
  return installmentOptions.value.map(item => ({
    value: item.installmentDeductId,
    label: `第${item.installmentNo}期`,
  }));
});

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
    loadInstallmentOptions();
  }
});

// 重置表单
function resetForm() {
  Object.assign(formData, {
    installmentDeductId: undefined,
    remark: '',
  });
  installmentOptions.value = [];
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 加载可选期数
async function loadInstallmentOptions() {
  console.log('开始加载可选期数，orderId:', props.orderInfo.orderId);
  
  if (!props.orderInfo.orderId) {
    console.error('orderId 为空，无法加载可选期数');
    return;
  }
  
  try {
    const response = await segmentOrderApi.getOfflineInstallmentOptions(props.orderInfo.orderId);
    if (response.ok && response.data) {
      installmentOptions.value = response.data;
      console.log('成功设置可选期数:', installmentOptions.value);
    } else {
      console.error('API响应失败:', response);
      message.error(response.msg || '获取可选期数失败');
    }
  } catch (error) {
    console.error('获取可选期数失败:', error);
    message.error('获取可选期数失败');
  }
}

// 取消操作
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 确认操作
async function handleConfirm() {
  try {
    await formRef.value.validate();
    
    const paymentData = {
      installmentDeductId: formData.installmentDeductId,
      remark: formData.remark,
    };
    
    // 触发确认事件，让父组件处理API调用
    emit('confirm', paymentData);
    
  } catch (error) {
    console.error('表单验证失败:', error);
    if (error.errorFields) {
      message.error('请完善必填信息');
    }
  }
}
</script>

<style scoped lang="less">
.offline-payment-content {
  .notice-text {
    background: #f6f8fa;
    padding: 12px 16px;
    border-radius: 6px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 24px;
  }
  
  .offline-payment-form {
    .ant-form-item {
      margin-bottom: 20px;
    }
    
    .remark-tip {
      color: #999;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 