<!--
  * 解约订单弹窗
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2024-01-01 00:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-modal
    :open="visible"
    title="解约"
    width="800px"
    :mask-closable="false"
    :keyboard="false"
    @cancel="handleCancel"
    centered
  >
    <div style="padding: 20px 0;">
      <div class="terminate-modal-content">
      <!-- 解约信息 -->
      <div class="section">
        <h3 class="section-title">解约信息</h3>
        <a-form
          ref="formRef"
          :model="formData"
          :rules="rules"
          layout="vertical"
          class="terminate-form"
        >
          <a-row :gutter="24">
            <a-col :span="12">
              <a-form-item label="解约类型">
                <span class="terminate-type-text">商家解约</span>
              </a-form-item>
            </a-col>
          </a-row>
          
          <a-form-item name="terminateReason" label="解约原因">
            <a-textarea
              v-model:value="formData.terminateReason"
              placeholder="请输入解约原因"
              :maxlength="200"
              :rows="3"
              show-count
            />
          </a-form-item>

          <a-form-item name="terminatePictures" label="解约图片">
            <div class="upload-container">
              <!-- 已上传的图片 -->
              <div v-for="(image, index) in uploadedImages" :key="index" class="uploaded-image-item">
                <img :src="image.url" alt="解约图片" @click="previewImage(image.url)" />
                <div class="image-remove" @click="removeImage(index)" :style="{ pointerEvents: confirmLoading ? 'none' : 'auto' }">
                  <close-outlined />
                </div>
              </div>
              <!-- 上传按钮 -->
              <div 
                v-if="uploadedImages.length < 6" 
                class="upload-btn" 
                :class="{ disabled: uploading || confirmLoading }" 
                @click="triggerFileInput"
              >
                <div class="upload-icon">
                  <plus-outlined v-if="!uploading" />
                  <loading-outlined v-if="uploading" class="spinning" />
                </div>
                <div class="upload-text">{{ uploading ? '上传中...' : '点击上传' }}</div>
              </div>
              <!-- 隐藏的文件输入框 -->
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                multiple
                style="display: none"
                @change="handleFileChange"
                :disabled="uploading || confirmLoading"
              />
            </div>
            <div class="field-tip">支持.jpg .png格式，最多上传6张图片</div>
          </a-form-item>
        </a-form>
      </div>

      <!-- 订单信息 -->
      <div class="section">
        <h3 class="section-title">订单信息</h3>
        <div class="order-info">
          <a-row :gutter="[24, 16]">
            <a-col :span="12">
              <div class="info-item">
                <span class="label">订单编号：</span>
                <span class="value long-text" :title="orderInfo.orderNo">{{ orderInfo.orderNo }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span class="value">{{ orderInfo.createTime }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="info-item">
                <span class="label">商品名称：</span>
                <span class="value long-text" :title="orderInfo.productName">{{ orderInfo.productName }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="info-item">
                <span class="label">订单金额：</span>
                <span class="value">{{ orderInfo.orderAmount }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="info-item">
                <span class="label">用户手机号：</span>
                <span class="value">{{ orderInfo.customerPhone }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="info-item">
                <span class="label">用户名称：</span>
                <span class="value">{{ orderInfo.customerName }}</span>
              </div>
            </a-col>
          </a-row>
        </div>
      </div>
    </div>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">确定</a-button>
      </div>
    </template>
  </a-modal>

  <!-- 图片预览模态框 -->
  <a-modal
    v-model:open="imageModalVisible"
    title="图片预览"
    :footer="null"
    width="600px"
    centered
  >
    <div class="image-preview">
      <a-image
        :src="currentImageUrl"
        :preview="false"
        style="width: 100%"
      />
    </div>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, CloseOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { fileApi } from '/@/api/support/file-api';
import { segmentOrderApi } from '/@/api/business/order/segment-order-api';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  orderInfo: {
    type: Object,
    default: () => ({
      orderId: '',
      orderNo: '',
      createTime: '',
      productName: '',
      orderAmount: '',
      customerPhone: '',
      customerName: '',
    }),
  },
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 表单引用
const formRef = ref();
const confirmLoading = ref(false);

// 表单数据
const formData = reactive({
  terminateReason: '',
  terminatePictures: '',
});

// 图片上传相关
const fileInput = ref(null);
const uploadedImages = ref([]);
const imageModalVisible = ref(false);
const currentImageUrl = ref('');
const uploading = ref(false);

// 表单验证规则
const rules = {
  terminateReason: [
    { required: true, message: '请输入解约原因', trigger: 'blur' },
    { min: 1, max: 200, message: '解约原因长度为1-200个字符', trigger: 'blur' },
  ],
};

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 重置表单
function resetForm() {
  Object.assign(formData, {
    terminateReason: '',
    terminatePictures: '',
  });
  uploadedImages.value = [];
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 取消操作
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 确认操作
async function handleConfirm() {
  try {
    await formRef.value.validate();
    
    confirmLoading.value = true;
    
    // 调用解约API
    const response = await segmentOrderApi.merchantTerminated(props.orderInfo.orderId, {
      auditRemark: formData.terminateReason,
      auditPictures: formData.terminatePictures,
    });
    
    if (response.ok) {
      message.success('解约操作成功');
      
      // 触发确认事件，传递解约结果
      emit('confirm', {
        success: true,
        orderId: props.orderInfo.orderId,
        auditRemark: formData.terminateReason,
        auditPictures: formData.terminatePictures,
      });
      
      // 关闭弹窗
      emit('update:visible', false);
    } else {
      message.error(response.msg || '解约操作失败');
    }
    
  } catch (error) {
    console.error('解约操作失败:', error);
    if (error.errorFields) {
      message.error('请完善必填信息');
    } else {
      message.error('解约操作失败：' + (error.message || '网络错误'));
    }
  } finally {
    confirmLoading.value = false;
  }
}

// 图片上传相关函数
function triggerFileInput() {
  if (!uploading.value && !confirmLoading.value) {
    fileInput.value?.click();
  }
}

async function handleFileChange(event) {
  const files = event.target.files;
  if (!files || files.length === 0) return;
  
  // 检查是否超过最大数量限制
  const remainingSlots = 6 - uploadedImages.value.length;
  const filesToProcess = Array.from(files).slice(0, remainingSlots);
  
  uploading.value = true;
  
  try {
    for (const file of filesToProcess) {
      if (!validateFile(file)) continue;
      
      // 创建预览图片
      const imageData = await createImageData(file);
      
      // 上传文件
      const url = await uploadFile(file);
      
      // 添加到已上传列表
      uploadedImages.value.push({
        url: url,
        name: file.name,
        preview: imageData.url
      });
    }
    
    // 更新表单数据
    updateFormPicturesData();
    message.success('图片上传成功');
    
  } catch (error) {
    console.error('图片上传失败:', error);
    message.error('图片上传失败');
  } finally {
    uploading.value = false;
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  }
}

function validateFile(file) {
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return false;
  }
  
  // 验证文件大小（限制为5MB）
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    message.error('文件大小不能超过5MB');
    return false;
  }
  
  return true;
}

function createImageData(file) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      resolve({
        url: e.target.result,
        name: file.name,
        size: file.size,
        file: file
      });
    };
    reader.readAsDataURL(file);
  });
}

async function uploadFile(file, folder = 1) {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await fileApi.uploadFile(formData, folder);
    return response.data.fileUrl || '';
  } catch (error) {
    console.error('文件上传失败:', error);
    throw new Error('文件上传失败');
  }
}

function removeImage(index) {
  uploadedImages.value.splice(index, 1);
  updateFormPicturesData();
}

function updateFormPicturesData() {
  // 将图片URL用逗号分隔拼接成字符串
  formData.terminatePictures = uploadedImages.value.map(img => img.url).join(',');
}

function previewImage(imageUrl) {
  currentImageUrl.value = imageUrl;
  imageModalVisible.value = true;
}
</script>

<style scoped lang="less">
.terminate-modal-content {
  .section {
    margin-bottom: 24px;
    
    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #262626;
      margin-bottom: 16px;
      border-left: 4px solid #1890ff;
      padding-left: 12px;
    }
  }
  
  .terminate-form {
    .ant-form-item {
      margin-bottom: 16px;
    }
    
    .terminate-type-text {
      color: #262626;
      font-size: 14px;
      font-weight: 500;
    }
  }
  
  .order-info {
    background: #fafafa;
    padding: 16px;
    border-radius: 6px;
    
    .info-item {
      display: flex;
      align-items: flex-start;
      margin-bottom: 8px;
      
      .label {
        color: #666;
        font-size: 14px;
        min-width: 85px;
        flex-shrink: 0;
        padding-top: 1px;
      }
      
      .value {
        color: #262626;
        font-size: 14px;
        font-weight: 500;
        flex: 1;
        word-break: break-all;
        line-height: 1.4;
        
        &.long-text {
          max-width: calc(100% - 85px);
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          cursor: help;
        }
      }
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.uploaded-image-item {
  position: relative;
  width: 104px;
  height: 104px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  display: inline-block;
}

.uploaded-image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-remove {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
}

.image-remove:hover {
  background: rgba(0, 0, 0, 0.7);
}

.upload-btn {
  width: 104px;
  height: 104px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
}

.upload-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.upload-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.upload-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.field-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}

.image-preview {
  text-align: center;
}
</style> 