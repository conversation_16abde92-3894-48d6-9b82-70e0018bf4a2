<!--
  * 解约申请详情弹窗
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 15:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-modal
    :open="visible"
    title="解约详情"
    width="800px"
    centered
    :mask-closable="true"
    :keyboard="false"
    @cancel="handleCancel"
    :footer="null"
  >
    <div style="padding: 20px 0;">
      <!-- 申请时间 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">申请时间</div>
        <div>{{ detailData.createTime || '-' }}</div>
      </div>
      
      <!-- 解约类型 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">解约类型</div>
        <div>{{ getTerminateTypeText((detailData.orderAfterSalesVO || detailData).terminatedType) }}</div>
      </div>
      
      <!-- 申请原因 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">申请原因</div>
        <div style="line-height: 1.6;">{{ (detailData.orderAfterSalesVO || detailData).terminatedReason || '-' }}</div>
      </div>
      
      <!-- 申请上传图片 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">申请上传图片</div>
        <div v-if="parseImageString((detailData.orderAfterSalesVO || detailData).pictures).length > 0" style="display: flex; gap: 12px;">
          <a-image-preview-group>
            <a-image
              v-for="(image, index) in parseImageString((detailData.orderAfterSalesVO || detailData).pictures)"
              :key="index"
              :width="120"
              :height="120"
              :src="image.url"
              :alt="`申请图片${index + 1}`"
              style="object-fit: cover; border-radius: 4px; border: 1px solid #d9d9d9;"
            />
          </a-image-preview-group>
        </div>
        <div v-else style="color: #999;">暂无图片</div>
      </div>
      
      <!-- 审核结果 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">审核结果</div>
        <div>
          <a-tag :color="getAuditStatusColor((detailData.orderAfterSalesVO || detailData).afterSalesStatus)">
            {{ getAuditStatusText((detailData.orderAfterSalesVO || detailData).afterSalesStatus) || '-' }}
          </a-tag>
        </div>
      </div>
      
      <!-- 审核备注 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">审核备注</div>
        <div>{{ (detailData.orderAfterSalesVO || detailData).auditRemark || '-' }}</div>
      </div>
      
      <!-- 审核图片 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">审核图片</div>
        <div v-if="parseImageString((detailData.orderAfterSalesVO || detailData).auditPictures).length > 0" style="display: flex; gap: 12px;">
          <a-image-preview-group>
            <a-image
              v-for="(image, index) in parseImageString((detailData.orderAfterSalesVO || detailData).auditPictures)"
              :key="index"
              :width="120"
              :height="120"
              :src="image.url"
              :alt="`审核图片${index + 1}`"
              style="object-fit: cover; border-radius: 4px; border: 1px solid #d9d9d9;"
            />
          </a-image-preview-group>
        </div>
        <div v-else style="color: #999;">暂无图片</div>
      </div>
      
      <!-- 审核人 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">审核人</div>
        <div>{{ detailData.employeeName || '-' }}</div>
      </div>
      
      <!-- 审核时间 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">审核时间</div>
        <div>{{ (detailData.orderAfterSalesVO || detailData).auditTime || '-' }}</div>
      </div>
    </div>

    <template #footer>
      <a-button @click="handleCancel">关闭</a-button>
    </template>


  </a-modal>
</template>

<script setup>
import { watch } from 'vue';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  detailData: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'cancel']);

// 监听弹窗显示状态
watch(() => props.visible, (newVal) => {
  if (newVal) {
    console.log('解约详情数据:', props.detailData);
  }
});

// 解析图片字符串为数组
function parseImageString(imageStr) {
  if (!imageStr) return [];
  
  if (typeof imageStr === 'string') {
    return imageStr.split(',').filter(url => url.trim()).map(url => ({ url: url.trim() }));
  }
  
  if (Array.isArray(imageStr)) {
    return imageStr;
  }
  
  return [];
}

// 取消操作
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 获取解约类型文本
function getTerminateTypeText(type) {
  const typeMap = {
    'NORMAL_TERMINATED': '商家解约',
    'APPLY_TERMINATED': '用户申请',
  };
  return typeMap[type] || '未知';
}

// 获取审核状态文本
function getAuditStatusText(status) {
  const statusMap = {
    'WAIT_DEAL': '待审核',
    'PASS': '已通过',
    'REJECT': '已拒绝',
    'CANCEL': '用户取消',
  };
  return statusMap[status] || '-';
}

// 获取审核状态颜色
function getAuditStatusColor(status) {
  const colorMap = {
    'WAIT_DEAL': 'processing',
    'PASS': 'success', 
    'REJECT': 'error',
    'CANCEL': 'warning',
  };
  return colorMap[status] || 'default';
}
</script>

<style scoped>
</style> 