<!--
  * 分段订单表单
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 10:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-drawer
    :title="form.orderId ? '编辑分段订单' : '添加分段订单'"
    :width="600"
    :open="visible"
    :body-style="{ paddingBottom: '80px' }"
    @close="onClose"
    :destroyOnClose="true"
  >
    <a-form ref="formRef" :model="form" :rules="rules" :label-col="{ span: 6 }">
      <!-- 基本信息 -->
      <a-divider>基本信息</a-divider>
      
      <a-form-item label="商品名称" name="goodsName">
        <a-input v-model:value="form.goodsName" placeholder="请输入商品名称" />
      </a-form-item>
      
      <a-form-item label="用户姓名" name="userName">
        <a-input v-model:value="form.userName" placeholder="请输入用户姓名" />
      </a-form-item>
      
      <a-form-item label="用户手机号" name="userPhone">
        <a-input v-model:value="form.userPhone" placeholder="请输入用户手机号" />
      </a-form-item>
      
      <a-form-item label="订单状态" name="orderStatus">
        <SmartEnumSelect enum-name="ORDER_STATUS_ENUM" v-model:value="form.orderStatus" />
      </a-form-item>
      
      <!-- 金额信息 -->
      <a-divider>金额信息</a-divider>
      
      <a-form-item label="订单总金额" name="totalAmount">
        <a-input-number
          v-model:value="form.totalAmount"
          :min="0"
          :precision="2"
          style="width: 100%"
          placeholder="请输入订单总金额"
          @change="calculateSegmentAmount"
        />
      </a-form-item>
      
      <a-form-item label="扣款周期" name="deductionCycle">
        <a-select v-model:value="form.deductionCycle" placeholder="请选择扣款周期">
          <a-select-option value="按月">按月</a-select-option>
          <a-select-option value="按季度">按季度</a-select-option>
          <a-select-option value="按半年">按半年</a-select-option>
          <a-select-option value="按年">按年</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="分期数" name="installmentCount">
        <a-input-number
          v-model:value="form.installmentCount"
          :min="1"
          :max="36"
          style="width: 100%"
          placeholder="请输入分期数"
          @change="calculateSegmentAmount"
        />
      </a-form-item>
      
      <!-- 分段设置 -->
      <a-divider>分段设置</a-divider>
      
      <a-form-item label="前段分期数" name="frontPeriodCount">
        <a-input-number
          v-model:value="form.frontPeriodCount"
          :min="0"
          :max="form.installmentCount"
          style="width: 100%"
          placeholder="请输入前段分期数"
          @change="calculateSegmentAmount"
        />
      </a-form-item>
      
      <a-form-item label="前段每期金额" name="frontPeriodAmount">
        <a-input-number
          v-model:value="form.frontPeriodAmount"
          :min="0"
          :precision="2"
          style="width: 100%"
          placeholder="请输入前段每期金额"
          @change="calculateTotalAmount"
        />
      </a-form-item>
      
      <a-form-item label="后段每期金额" name="backPeriodAmount">
        <a-input-number
          v-model:value="form.backPeriodAmount"
          :min="0"
          :precision="2"
          style="width: 100%"
          placeholder="请输入后段每期金额"
          @change="calculateTotalAmount"
        />
      </a-form-item>
      
      <!-- 其他信息 -->
      <a-divider>其他信息</a-divider>
      
      <a-form-item label="邀请码" name="inviteCode">
        <a-input v-model:value="form.inviteCode" placeholder="请输入邀请码（可选）" />
      </a-form-item>
      
      <a-form-item label="员工" name="employeeId">
        <EmployeeSelect v-model:value="form.employeeId" placeholder="请选择员工（可选）" />
      </a-form-item>
      
      <a-form-item label="收款方式" name="paymentMethod">
        <a-select v-model:value="form.paymentMethod" placeholder="请选择收款方式">
          <a-select-option value="auto_deduction">自动扣款</a-select-option>
          <a-select-option value="manual_payment">手动付款</a-select-option>
          <a-select-option value="offline_payment">线下收款</a-select-option>
        </a-select>
      </a-form-item>
      
      <a-form-item label="备注" name="remark">
        <a-textarea
          v-model:value="form.remark"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </a-form-item>
    </a-form>

    <!-- 底部按钮 -->
    <div class="drawer-footer">
      <a-button style="margin-right: 8px" @click="onClose">取消</a-button>
      <a-button type="primary" @click="onSubmit">提交</a-button>
    </div>
  </a-drawer>
</template>

<script setup>
  import { ref, nextTick, reactive, computed } from 'vue';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { segmentOrderApi } from '/@/api/business/order/segment-order-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import SmartEnumSelect from '/@/components/framework/smart-enum-select/index.vue';
  import EmployeeSelect from '/@/components/system/employee-select/index.vue';
  import { ORDER_STATUS_ENUM } from '/@/constants/business/order/order-const';
  import _ from 'lodash';

  const emit = defineEmits(['refresh']);

  // 组件引用
  const formRef = ref();
  const visible = ref(false);

  // 表单默认值
  const formDefault = {
    orderId: undefined,
    goodsName: '',
    userName: '',
    userPhone: '',
    orderStatus: ORDER_STATUS_ENUM.PENDING.value,
    totalAmount: undefined,
    deductionCycle: '按月',
    installmentCount: 12,
    frontPeriodCount: 1,
    frontPeriodAmount: undefined,
    backPeriodAmount: undefined,
    inviteCode: '',
    employeeId: undefined,
    paymentMethod: 'auto_deduction',
    remark: '',
  };

  // 表单数据
  let form = reactive({ ...formDefault });

  // 表单验证规则
  const rules = {
    goodsName: [{ required: true, message: '商品名称不能为空' }],
    userName: [{ required: true, message: '用户姓名不能为空' }],
    userPhone: [
      { required: true, message: '用户手机号不能为空' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号格式' }
    ],
    orderStatus: [{ required: true, message: '请选择订单状态' }],
    totalAmount: [{ required: true, message: '订单总金额不能为空' }],
    deductionCycle: [{ required: true, message: '请选择扣款周期' }],
    installmentCount: [{ required: true, message: '分期数不能为空' }],
    frontPeriodCount: [{ required: true, message: '前段分期数不能为空' }],
    frontPeriodAmount: [{ required: true, message: '前段每期金额不能为空' }],
    backPeriodAmount: [{ required: true, message: '后段每期金额不能为空' }],
    paymentMethod: [{ required: true, message: '请选择收款方式' }],
  };

  // 计算分段金额
  function calculateSegmentAmount() {
    if (!form.totalAmount || !form.installmentCount || !form.frontPeriodCount) {
      return;
    }
    
    const frontTotal = form.frontPeriodCount * (form.frontPeriodAmount || 0);
    const backPeriodCount = form.installmentCount - form.frontPeriodCount;
    
    if (backPeriodCount > 0) {
      const remainingAmount = form.totalAmount - frontTotal;
      form.backPeriodAmount = Math.round((remainingAmount / backPeriodCount) * 100) / 100;
    }
  }

  // 根据分段金额计算总金额
  function calculateTotalAmount() {
    if (!form.frontPeriodCount || !form.frontPeriodAmount || !form.backPeriodAmount) {
      return;
    }
    
    const frontTotal = form.frontPeriodCount * form.frontPeriodAmount;
    const backPeriodCount = (form.installmentCount || 0) - form.frontPeriodCount;
    const backTotal = backPeriodCount * form.backPeriodAmount;
    
    form.totalAmount = Math.round((frontTotal + backTotal) * 100) / 100;
  }

  // 显示表单
  function showModal(rowData) {
    Object.assign(form, formDefault);
    if (rowData && !_.isEmpty(rowData)) {
      Object.assign(form, rowData);
    }
    
    visible.value = true;
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  }

  // 关闭表单
  function onClose() {
    Object.assign(form, formDefault);
    visible.value = false;
  }

  // 提交表单
  function onSubmit() {
    formRef.value
      .validate()
      .then(async () => {
        SmartLoading.show();
        try {
          if (form.orderId) {
            await segmentOrderApi.update(form);
            message.success('修改成功');
          } else {
            await segmentOrderApi.add(form);
            message.success('添加成功');
          }
          onClose();
          emit('refresh');
        } catch (error) {
          smartSentry.captureError(error);
        } finally {
          SmartLoading.hide();
        }
      })
      .catch(() => {
        message.error('请检查表单数据');
      });
  }

  defineExpose({ showModal });
</script>

<style scoped lang="less">
  .drawer-footer {
    position: absolute;
    right: 0;
    bottom: 0;
    width: 100%;
    border-top: 1px solid #e9e9e9;
    padding: 10px 16px;
    background: #fff;
    text-align: right;
    z-index: 1;
  }

  :deep(.ant-divider) {
    margin: 16px 0 24px 0;
    font-weight: 500;
  }
</style> 