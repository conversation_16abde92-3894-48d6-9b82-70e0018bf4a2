<!--
  * 解约申请审核弹窗
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 15:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-modal
    :open="visible"
    title="解约申请审核"
    width="800px"
    centered
    :mask-closable="false"
    :keyboard="false"
    @cancel="handleCancel"
  >
    <div style="padding: 20px 0;">
      <!-- 申请时间 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">申请时间</div>
        <div>{{ auditData.createTime || '-' }}</div>
      </div>
      
      <!-- 解约类型 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">解约类型</div>
        <div>{{ getTerminateTypeText((auditData.orderAfterSalesVO || auditData).terminatedType) }}</div>
      </div>
      
      <!-- 申请原因 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">申请原因</div>
        <div style="line-height: 1.6;">{{ (auditData.orderAfterSalesVO || auditData).terminatedReason || '-' }}</div>
      </div>
      
      <!-- 申请上传图片 -->
      <div style="margin-bottom: 24px;">
        <div style="color: #666; margin-bottom: 8px;">申请上传图片</div>
        <div v-if="parseImageString((auditData.orderAfterSalesVO || auditData).pictures).length > 0" style="display: flex; gap: 12px;">
          <a-image-preview-group>
            <a-image
              v-for="(image, index) in parseImageString((auditData.orderAfterSalesVO || auditData).pictures)"
              :key="index"
              :width="120"
              :height="120"
              :src="image.url"
              :alt="`申请图片${index + 1}`"
              style="object-fit: cover; border-radius: 4px; border: 1px solid #d9d9d9;"
            />
          </a-image-preview-group>
        </div>
        <div v-else style="color: #999;">暂无图片</div>
      </div>

      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
      >
        <!-- 审核结果 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;"><span style="color: red;">*</span> 审核结果</div>
          <a-form-item name="auditResultType" style="margin-bottom: 0;">
            <a-radio-group v-model:value="formData.auditResultType">
              <a-radio :value="0">通过</a-radio>
              <a-radio :value="1">驳回</a-radio>
            </a-radio-group>
          </a-form-item>
        </div>
        
        <!-- 审核备注 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;"><span style="color: red;">*</span> 审核备注</div>
          <a-form-item name="auditRemark" style="margin-bottom: 0;">
            <a-textarea
              v-model:value="formData.auditRemark"
              placeholder="请输入审核备注"
              :auto-size="{ minRows: 4, maxRows: 6 }"
              :maxlength="200"
              show-count
            />
          </a-form-item>
        </div>
        
        <!-- 上传审核图片 -->
        <div style="margin-bottom: 24px;">
          <div style="color: #666; margin-bottom: 8px;">上传审核图片</div>
          <a-form-item style="margin-bottom: 0;">
            <div class="upload-container">
              <!-- 已上传的图片 -->
              <a-image-preview-group v-if="uploadedImages.length > 0">
                <div v-for="(image, index) in uploadedImages" :key="index" class="uploaded-image-item">
                  <a-image :src="image.url" alt="审核图片" style="width: 100%; height: 100%; object-fit: cover;" />
                  <div class="image-remove" @click="removeImage(index)" :style="{ pointerEvents: confirmLoading ? 'none' : 'auto' }">
                    <CloseOutlined />
                  </div>
                </div>
              </a-image-preview-group>
              <!-- 上传按钮 -->
              <div 
                v-if="uploadedImages.length < 6" 
                class="upload-btn" 
                :class="{ disabled: uploading || confirmLoading }" 
                @click="triggerFileInput"
              >
                <div class="upload-icon">
                  <PlusOutlined v-if="!uploading" />
                  <LoadingOutlined v-if="uploading" class="spinning" />
                </div>
                <div class="upload-text">{{ uploading ? '上传中...' : '点击上传' }}</div>
              </div>
              <!-- 隐藏的文件输入框 -->
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                multiple
                style="display: none"
                @change="handleFileChange"
                :disabled="uploading || confirmLoading"
              />
            </div>
            <div class="field-tip">支持.jpg .png格式，最多上传6张图片</div>
          </a-form-item>
        </div>
      </a-form>
    </div>

    <!-- 底部按钮 -->
    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">确定</a-button>
      </div>
    </template>


  </a-modal>
</template>

<script setup>
import { ref, reactive, watch } from 'vue';
import { message } from 'ant-design-vue';
import { PlusOutlined, CloseOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { fileApi } from '/@/api/support/file-api';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  auditData: {
    type: Object,
    default: () => ({}),
  },
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 表单引用
const formRef = ref();
const confirmLoading = ref(false);
// 文件输入框引用
const fileInput = ref();

// 图片上传相关
const uploadedImages = ref([]);
const uploading = ref(false);

// 表单数据
const formData = reactive({
  auditResultType: undefined,     // 审核结果类型: 0通过 1驳回
  auditRemark: '',               // 审核备注
  auditPictures: '',             // 审核图片URL，逗号分隔
});

// 表单验证规则
const rules = {
  auditResultType: [
    { required: true, message: '请选择审核结果', trigger: 'change' },
  ],
  auditRemark: [
    { required: true, message: '请输入审核备注', trigger: 'blur' },
  ],
};

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 重置表单
function resetForm() {
  Object.assign(formData, {
    auditResultType: undefined,
    auditRemark: '',
    auditPictures: '',
  });
  uploadedImages.value = [];
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 取消操作
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 确认操作
async function handleConfirm() {
  try {
    await formRef.value.validate();
    
    confirmLoading.value = true;
    
    // 构造提交参数
    const submitParams = {
      afterSalesId: props.auditData.afterSalesId || props.auditData.orderId,
      auditResultType: formData.auditResultType,
      auditRemark: formData.auditRemark,
      auditPictures: formData.auditPictures
    };
    
    console.log('提交审核参数:', submitParams);
    
    // 触发确认事件
    emit('confirm', submitParams);
    
    // 关闭弹窗
    emit('update:visible', false);
    
  } catch (error) {
    console.log('表单验证失败:', error);
  } finally {
    confirmLoading.value = false;
  }
}

// 触发文件选择
function triggerFileInput() {
  if (!uploading.value && !confirmLoading.value) {
    fileInput.value?.click();
  }
}

// 处理文件选择
async function handleFileChange(event) {
  const files = event.target.files;
  if (!files || files.length === 0) return;
  
  // 检查是否超过最大数量限制
  const remainingSlots = 6 - uploadedImages.value.length;
  const filesToProcess = Array.from(files).slice(0, remainingSlots);
  
  uploading.value = true;
  
  try {
    for (const file of filesToProcess) {
      if (!validateFile(file)) continue;
      
      // 创建预览图片
      const imageData = await createImageData(file);
      
      // 上传文件
      const url = await uploadFile(file);
      
      // 添加到已上传列表
      uploadedImages.value.push({
        url: url,
        name: file.name,
        preview: imageData.url
      });
    }
    
    // 更新表单数据
    updateAuditPictures();
    message.success('图片上传成功');
    
  } catch (error) {
    console.error('图片上传失败:', error);
    message.error('图片上传失败');
  } finally {
    uploading.value = false;
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
  }
}

// 验证文件
function validateFile(file) {
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return false;
  }
  
  // 验证文件大小（限制为5MB）
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    message.error('文件大小不能超过5MB');
    return false;
  }
  
  return true;
}

// 创建图片数据
function createImageData(file) {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => {
      resolve({
        url: e.target.result,
        name: file.name,
        size: file.size,
        file: file
      });
    };
    reader.readAsDataURL(file);
  });
}

// 上传文件
async function uploadFile(file, folder = 1) {
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await fileApi.uploadFile(formData, folder);
    return response.data.fileUrl || '';
  } catch (error) {
    console.error('文件上传失败:', error);
    throw new Error('文件上传失败');
  }
}

// 删除图片
function removeImage(index) {
  uploadedImages.value.splice(index, 1);
  updateAuditPictures();
  message.success('图片删除成功');
}

// 更新图片URL字符串
function updateAuditPictures() {
  // 将图片URL用逗号分隔拼接成字符串
  formData.auditPictures = uploadedImages.value.map(img => img.url).join(',');
}

// 解析图片字符串为数组
function parseImageString(imageStr) {
  if (!imageStr) return [];
  
  if (typeof imageStr === 'string') {
    return imageStr.split(',').filter(url => url.trim()).map(url => ({ url: url.trim() }));
  }
  
  if (Array.isArray(imageStr)) {
    return imageStr;
  }
  
  return [];
}

// 获取解约类型文本
function getTerminateTypeText(type) {
  const typeMap = {
    'NORMAL_TERMINATED': '商家解约',
    'APPLY_TERMINATED': '用户申请',
  };
  return typeMap[type] || '未知';
}
</script>

<style scoped>
.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.uploaded-image-item {
  position: relative;
  width: 104px;
  height: 104px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  display: inline-block;
}

.uploaded-image-item :deep(.ant-image) {
  width: 100%;
  height: 100%;
  display: flex;
}

.uploaded-image-item :deep(.ant-image-img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 6px;
}

.image-remove {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
}

.image-remove:hover {
  background: rgba(0, 0, 0, 0.7);
}

.upload-btn {
  width: 104px;
  height: 104px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
}

.upload-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.upload-btn.disabled {
  opacity: 0.6;
  cursor: not-allowed;
  pointer-events: none;
}

.upload-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.field-tip {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
  line-height: 1.4;
}
</style> 