<!--
  * 取消单期代扣弹窗
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 10:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-modal
    :open="visible"
    title="取消单期代扣"
    width="600px"
    :mask-closable="false"
    :keyboard="false"
    @cancel="handleCancel"
  >
    <div class="cancel-deduction-content">
      <!-- 提示信息 -->
      <div class="notice-text">
        该操作仅取消代扣，不影响扣款计划状态，仍然可用"在线支付"、"线下收款"等方式收款
      </div>

      <!-- 表单 -->
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        layout="vertical"
        class="cancel-deduction-form"
      >
        <a-form-item name="periodNumber" label="取消指定期数">
          <a-select 
            v-model:value="formData.periodNumber" 
            placeholder="请选择"
            :options="periodOptions"
          />
        </a-form-item>
        
        <a-form-item name="remark" label="备注">
          <a-textarea
            v-model:value="formData.remark"
            placeholder="请输入"
            :maxlength="15"
            :rows="4"
            show-count
          />
          <div class="remark-tip">
            在「订单详情」、「分段明细」备注字段可见
          </div>
        </a-form-item>
      </a-form>
    </div>

    <!-- 底部操作按钮 -->
    <template #footer>
      <div class="modal-footer">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleConfirm" :loading="confirmLoading">确定</a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { ref, reactive, watch, computed } from 'vue';
import { message } from 'ant-design-vue';

// 定义组件属性
const props = defineProps({
  visible: {
    type: Boolean,
    default: false,
  },
  paymentPlanList: {
    type: Array,
    default: () => [],
  },
});

// 定义组件事件
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

// 表单引用
const formRef = ref();
const confirmLoading = ref(false);

// 表单数据
const formData = reactive({
  periodNumber: undefined,
  remark: '',
});

// 表单验证规则
const rules = {
  periodNumber: [
    { required: true, message: '请选择取消指定期数', trigger: 'change' },
  ],
  remark: [
    { required: true, message: '请输入备注', trigger: 'blur' },
    { min: 1, max: 15, message: '备注长度为1-15个字符', trigger: 'blur' },
  ],
};

// 计算可选择的期数选项（非"已扣款"的期数）
const periodOptions = computed(() => {
  return props.paymentPlanList
    .filter(item => item.paymentStatus !== 2) // 2表示已扣款
    .map(item => ({
      value: item.periodNumber,
      label: `第${item.periodNumber}期`,
    }));
});

// 监听弹窗显示状态，重置表单
watch(() => props.visible, (newVal) => {
  if (newVal) {
    resetForm();
  }
});

// 重置表单
function resetForm() {
  Object.assign(formData, {
    periodNumber: undefined,
    remark: '',
  });
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 取消操作
function handleCancel() {
  emit('update:visible', false);
  emit('cancel');
}

// 确认操作
async function handleConfirm() {
  try {
    await formRef.value.validate();
    
    confirmLoading.value = true;
    
    const cancelData = {
      periodNumber: formData.periodNumber,
      remark: formData.remark,
    };
    
    // 触发确认事件
    emit('confirm', cancelData);
    
    // 关闭弹窗
    emit('update:visible', false);
    
  } catch (error) {
    console.log('表单验证失败:', error);
  } finally {
    confirmLoading.value = false;
  }
}
</script>

<style scoped lang="less">
.cancel-deduction-content {
  .notice-text {
    background: #f6f8fa;
    padding: 12px 16px;
    border-radius: 6px;
    color: #666;
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 24px;
  }
  
  .cancel-deduction-form {
    .ant-form-item {
      margin-bottom: 20px;
    }
    
    .remark-tip {
      color: #999;
      font-size: 12px;
      margin-top: 4px;
    }
  }
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style> 