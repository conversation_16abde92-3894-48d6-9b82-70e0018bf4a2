<!--
  * 解约收款码弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 18:00:00
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    title="解约收款码"
    v-model:open="visible"
    :width="600"
    :footer="null"
    :destroyOnClose="true"
    @cancel="handleClose"
    class="termination-payment-modal"
    wrapClassName="termination-payment-modal-wrap"
  >
    <div class="termination-payment-container">
      <!-- 解约信息 -->
      <div class="termination-info">
        <h4>解约信息</h4>
        
        <a-row :gutter="16">
          <a-col :span="12">
            <a-form-item label="违约金" required>
              <a-input
                v-model:value="formData.penaltyFee"
                placeholder="请输入"
                suffix="元"
                @input="calculateTotal"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="课时费" required>
              <a-input
                v-model:value="formData.sessionFee"
                placeholder="请输入"
                suffix="元"
                @input="calculateTotal"
              />
            </a-form-item>
          </a-col>
        </a-row>

        <div class="total-amount">
          <span>费用合计：<strong>{{ totalAmount }}</strong></span>
        </div>
      </div>

      <!-- 解约收款码 -->
      <div class="qr-code-section">
        <div class="section-header">
          <span>解约收款码</span>
          <a-space>
            <!-- 如果没有二维码，显示"生成收款码"按钮 -->
            <a-button 
              v-if="!qrCodeImageUrl" 
              @click="generateQrCode" 
              type="primary" 
              size="small" 
              :loading="loading"
            >
              生成收款码
            </a-button>
            
            <!-- 如果已有二维码，显示"重新生成"和"下载二维码"按钮 -->
            <template v-else>
              <a-button @click="generateQrCode" type="primary" size="small" :loading="loading">
                重新生成
              </a-button>
              <a-button @click="downloadQrCode" size="small">
                下载二维码
              </a-button>
            </template>
          </a-space>
        </div>

        <div class="qr-code-area">
          <a-spin :spinning="loading">
            <div v-if="qrCodeImageUrl" class="qr-code-wrapper">
              <img :src="qrCodeImageUrl" alt="解约收款二维码" class="qr-code-image" />
            </div>
            <div v-else class="qr-code-placeholder">
              <a-empty description="请先填写费用信息并生成二维码" />
            </div>
          </a-spin>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, computed, onUnmounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { menuReservationApi } from '/@/api/business/order/menu-reservation-api';

  // ----------------------- 数据定义 ---------------------------
  const visible = ref(false);
  const loading = ref(false);
  
  const orderInfo = reactive({
    installmentOrderId: '',
    orderNo: '',
    customerName: '',
    customerPhone: ''
  });

  const formData = reactive({
    penaltyFee: '',
    sessionFee: ''
  });

  const qrCodeImageUrl = ref('');
  const qrCodeNumber = ref('');

  // ----------------------- 计算属性 ---------------------------
  const totalAmount = computed(() => {
    const penalty = parseFloat(formData.penaltyFee) || 0;
    const course = parseFloat(formData.sessionFee) || 0;
    const total = penalty + course;
    return total.toFixed(2);
  });

  // ----------------------- 公共函数 ---------------------------

  /**
   * 显示弹窗
   */
  function show(record) {
    orderInfo.installmentOrderId = record.installmentOrderId || record.orderId;
    orderInfo.orderNo = record.orderNo;
    orderInfo.customerName = record.customerName;
    orderInfo.customerPhone = record.customerPhone;
    
    // 重置表单数据
    formData.penaltyFee = '';
    formData.sessionFee = '';
    qrCodeImageUrl.value = '';
    qrCodeNumber.value = '';
    
    visible.value = true;
  }

  /**
   * 计算总金额
   */
  function calculateTotal() {
    // 总金额由计算属性自动计算
  }

  /**
   * 生成二维码
   */
  async function generateQrCode() {
    if (!formData.penaltyFee && !formData.sessionFee) {
      message.warning('请先填写违约金或课时费');
      return;
    }

    if (parseFloat(totalAmount.value) <= 0) {
      message.warning('费用金额必须大于0');
      return;
    }

    loading.value = true;
    try {
      // 准备请求参数，根据接口定义调整参数
      const params = {
        installmentOrderId: orderInfo.installmentOrderId,
        penaltyFee: parseFloat(formData.penaltyFee) || 0,
        sessionFee: parseFloat(formData.sessionFee) || 0,
        receiptType: 'TERMINATION_PAYMENT_CODE' // 解约收款码类型
      };

      // 调用API获取解约收款二维码
      const response = await menuReservationApi.getTerminationPaymentQrCode(params);
      console.log('解约收款码API响应:', response);
      console.log('响应数据类型:', typeof response.data);
      console.log('是否为Blob:', response.data instanceof Blob);
      
      if (response.data && response.data instanceof Blob) {
        // 清理之前的URL
        if (qrCodeImageUrl.value && qrCodeImageUrl.value.startsWith('blob:')) {
          URL.revokeObjectURL(qrCodeImageUrl.value);
        }
        
        // 创建新的blob URL
        qrCodeImageUrl.value = URL.createObjectURL(response.data);
        
        // 生成随机通道码（实际应该从API返回）
        qrCodeNumber.value = Math.random().toString().slice(2, 8).padStart(6, '0');
        
        message.success('解约收款码生成成功');
        console.log('解约收款码生成成功');
      } else {
        // 如果不是Blob数据，说明API返回了错误信息
        qrCodeImageUrl.value = '';
        console.log('API返回错误信息:', response.data);
        // 从response.data.msg中获取错误信息，axios将响应数据放在data字段中
        const errorMsg = response?.data?.msg || '生成解约收款码失败';
        console.log('提取的错误信息:', errorMsg);
        message.warning(errorMsg);
      }
    } catch (e) {
      console.log('请求异常:', e);
      console.log('异常响应:', e?.response);
      smartSentry.captureError(e);
      qrCodeImageUrl.value = '';
      
      // 特殊情况：如果异常对象是 application/json 类型的 Blob，需要读取其内容
      if (e instanceof Blob && e.type === 'application/json') {
        try {
          const text = await e.text();
          const errorData = JSON.parse(text);
          console.log('从Blob中解析的错误数据:', errorData);
          const errorMsg = errorData?.msg || '生成解约收款码失败';
          message.error(errorMsg);
        } catch (parseError) {
          console.log('解析Blob错误:', parseError);
          message.error('生成解约收款码失败');
        }
      } else {
        // 普通的HTTP请求异常
        const errorMsg = e?.response?.data?.msg || e?.message || '生成解约收款码失败';
        message.error(errorMsg);
      }
    } finally {
      loading.value = false;
    }
  }

  /**
   * 下载二维码
   */
  function downloadQrCode() {
    if (!qrCodeImageUrl.value) {
      message.warning('暂无二维码可下载');
      return;
    }

    try {
      const link = document.createElement('a');
      link.href = qrCodeImageUrl.value;
      link.download = `解约收款码_${orderInfo.orderNo}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success('二维码下载成功');
    } catch (e) {
      message.error('下载失败');
      smartSentry.captureError(e);
    }
  }

  /**
   * 关闭弹窗
   */
  function handleClose() {
    // 清理blob URL，避免内存泄漏
    if (qrCodeImageUrl.value && qrCodeImageUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(qrCodeImageUrl.value);
    }
    
    visible.value = false;
    qrCodeImageUrl.value = '';
    qrCodeNumber.value = '';
  }

  // ----------------------- 生命周期 ---------------------------
  onUnmounted(() => {
    // 组件销毁时清理blob URL，避免内存泄漏
    if (qrCodeImageUrl.value && qrCodeImageUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(qrCodeImageUrl.value);
    }
  });

  // ----------------------- 暴露给父组件的方法 ---------------------------
  defineExpose({
    show
  });
</script>

<style>
/* 全局样式，覆盖最外层 Modal 容器 */
.termination-payment-modal-wrap {
  background-color: transparent !important;
}

.termination-payment-modal-wrap .ant-modal {
  background-color: transparent !important;
}

.termination-payment-modal-wrap .ant-modal-content {
  background-color: #F2FAFE !important;
  border: none !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.termination-payment-modal-wrap .ant-modal-header {
  background-color: #F2FAFE !important;
  border-bottom: none !important;
}

.termination-payment-modal-wrap .ant-modal-body {
  background-color: #F2FAFE !important;
  padding: 0 !important;
}

.termination-payment-modal-wrap .ant-modal-footer {
  background-color: #F2FAFE !important;
  border-top: none !important;
}
</style>

<style scoped>
.termination-payment-container {
  padding: 24px;
  background-color: #F2FAFE;
}

.termination-info {
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #E0F4F3;
}

.termination-info h4 {
  margin-bottom: 16px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.total-amount {
  margin-top: 16px;
  text-align: right;
  font-size: 16px;
  color: #262626;
}

.total-amount strong {
  color: #1890ff;
  font-size: 18px;
}

.qr-code-section {
  text-align: center;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #262626;
}

.qr-code-area {
  margin-bottom: 20px;
}

.qr-code-wrapper {
  display: inline-block;
  position: relative;
}

.qr-code-image {
  width: 300px;
  height: auto;
  max-height: 400px;
  border-radius: 8px;
  object-fit: contain;
}



.qr-code-placeholder {
  width: 300px;
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
}

/* 表单样式调整 */
:deep(.ant-form-item) {
  margin-bottom: 16px;
}

:deep(.ant-form-item-label) {
  font-weight: 500;
}

:deep(.ant-input) {
  border-radius: 6px;
}
</style> 