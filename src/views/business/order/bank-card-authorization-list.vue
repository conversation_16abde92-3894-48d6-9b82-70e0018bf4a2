<!--
  * 银行卡授权列表
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27 16:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="用户姓名" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.realName" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="用户手机号" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.customerPhone" placeholder="请输入" />
      </a-form-item>

      <a-form-item label="授权状态" class="smart-query-form-item">
        <a-select style="width: 120px" v-model:value="queryForm.bindStatus" placeholder="全部" allowClear>
          <a-select-option value="BINDED">授权中</a-select-option>
          <a-select-option value="UNBIND">已解绑</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="授权卡类型" class="smart-query-form-item">
        <a-select style="width: 120px" v-model:value="queryForm.bankCardType" placeholder="全部" allowClear>
          <a-select-option value="DEBIT_CARD">储蓄卡</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="解绑时间" class="smart-query-form-item">
        <a-range-picker 
          style="width: 240px" 
          v-model:value="queryForm.unbindTimeRange" 
          format="YYYY/MM/DD"
          :placeholder="['开始日期', '结束日期']"
        />
      </a-form-item>

      <a-form-item label="门店名称" class="smart-query-form-item">
                  <StoreMultiSelect 
            v-model="queryForm.storeName" 
            width="200px"
            placeholder="请选择门店"
          />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="onExport">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.ORDER.BANK_CARD_AUTHORIZATION" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="customerBankcardId"
      :scroll="{ x: 1800, y: 2000 }"
      bordered
      :pagination="false"
      :showSorterTooltip="false"
      :loading="tableLoading"
      @change="onChange"
      @resizeColumn="handleResizeColumn"
    >

      <template #bodyCell="{ text, record, column }">
        <!-- 用户姓名列 -->
        <template v-if="column.dataIndex === 'realName'">
          {{ record.realName }}
        </template>
        
        <!-- 用户手机号列 -->
        <template v-if="column.dataIndex === 'customerPhone'">
          {{ maskPhone(record.customerPhone) }}
        </template>
        
        <!-- 履约中订单列 -->
        <template v-if="column.dataIndex === 'orderCount'">
          {{ record.orderCount }}
        </template>
        
        <!-- 授权状态列 -->
        <template v-if="column.dataIndex === 'bindStatus'">
          <a-tag :color="getBindStatusColor(record.bindStatus)">
            {{ getBindStatusText(record.bindStatus) }}
          </a-tag>
        </template>
        
        <!-- 授权卡类型列 -->
        <template v-if="column.dataIndex === 'bankCardType'">
          {{ getBankCardTypeText(record.bankCardType) }}
        </template>
        
        <!-- 授权银行卡列 -->
        <template v-if="column.dataIndex === 'bankCardNo'">
          {{ maskBankCard(record.bankCardNo) }}
        </template>
        
        <!-- 银行卡绑定手机号列 -->
        <template v-if="column.dataIndex === 'reservedPhone'">
          {{ maskPhone(record.reservedPhone) }}
        </template>
        
        <!-- 授权时间列 -->
        <template v-if="column.dataIndex === 'bindTime'">
          {{ record.bindTime }}
        </template>
        
        <!-- 解绑人列 -->
        <template v-if="column.dataIndex === 'actualName'">
          {{ record.actualName || '-' }}
        </template>
        
        <!-- 解绑时间列 -->
        <template v-if="column.dataIndex === 'unBindTime'">
          {{ record.unBindTime || '-' }}
        </template>
        
        <!-- 门店名称列 -->
        <template v-if="column.dataIndex === 'storeName'">
          {{ record.storeName || '-' }}
        </template>
        
        <!-- 操作列 -->
        <template v-if="column.dataIndex === 'operation'">
          <div class="smart-table-operate">
            <a-button type="link" size="small" @click="viewOrders(record)" v-privilege="'business:bank-card-authorization-list:viewOrders'">查看订单</a-button>
            <a-button type="link" size="small" @click="unbindCard(record)" v-privilege="'business:bank-card-authorization-list:unbindCard'">解绑</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { message, Modal } from 'ant-design-vue';
  import { SearchOutlined, ReloadOutlined, ExportOutlined } from '@ant-design/icons-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { dataFormatter } from '/@/utils/data-formatter';
  import { bankCardAuthorizationApi } from '/@/api/business/order/bank-card-authorization-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
  import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';

  import _ from 'lodash';
  import { smartSentry } from '/@/lib/smart-sentry';

  const router = useRouter();

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '用户姓名',
      dataIndex: 'realName',
      width: 120,
      resizable: true,
    },
    {
      title: '用户手机号',
      dataIndex: 'customerPhone',
      width: 140,
      resizable: true,
    },
    {
      title: '履约中订单',
      dataIndex: 'orderCount',
      width: 120,
      align: 'center',
      resizable: true,
    },
    {
      title: '授权状态',
      dataIndex: 'bindStatus',
      width: 100,
      align: 'center',
      resizable: true,
    },
    {
      title: '授权卡类型',
      dataIndex: 'bankCardType',
      width: 120,
      resizable: true,
    },
    {
      title: '授权银行卡',
      dataIndex: 'bankCardNo',
      width: 180,
      resizable: true,
    },
    {
      title: '银行卡绑定手机号',
      dataIndex: 'reservedPhone',
      width: 160,
      resizable: true,
    },
    {
      title: '授权时间',
      dataIndex: 'bindTime',
      width: 180,
      resizable: true,
      sorter: true,
    },
    {
      title: '解绑人',
      dataIndex: 'actualName',
      width: 120,
      resizable: true,
    },
    {
      title: '解绑时间',
      dataIndex: 'unBindTime',
      width: 180,
      resizable: true,
      sorter: true,
    },
    {
      title: '门店名称',
      dataIndex: 'storeName',
      resizable: true,
      width: 150,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 150,
      fixed: 'right',
      resizable: true,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  const queryFormState = {
    realName: undefined,
    customerPhone: undefined,
    bindStatus: undefined,
    bankCardType: undefined,
    unbindTimeRange: undefined,
    unBindTimeStart: undefined,
    unBindTimeEnd: undefined,
    pageNum: 1,
    pageSize: 10,
    sortItemList: [],
    
    // 门店信息
    storeName: [],                        // 门店名称查询字段
  };

  // 查询表单form
  const queryForm = reactive(_.cloneDeep(queryFormState));
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);

  function handleResizeColumn(w, col) {
    columns.value.forEach((item) => {
      if (item.dataIndex === col.dataIndex) {
        item.width = Math.floor(w);
        // 拖动宽度标识
        item.dragAndDropFlag = true;
      }
    });
  }

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, _.cloneDeep(queryFormState));
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  // 统一参数处理函数
  function prepareQueryParams() {
    const params = _.cloneDeep(queryForm);
    
    // 处理字符串类型参数：空值转null
    if (params.bindStatus === '' || params.bindStatus == null) {
      params.bindStatus = null;
    }
    if (params.bankCardType === '' || params.bankCardType == null) {
      params.bankCardType = null;
    }
    
    // 处理解绑时间范围
    if (queryForm.unbindTimeRange && queryForm.unbindTimeRange.length === 2) {
      params.unBindTimeStart = queryForm.unbindTimeRange[0].format('YYYY-MM-DD');
      params.unBindTimeEnd = queryForm.unbindTimeRange[1].format('YYYY-MM-DD');
    } else {
      params.unBindTimeStart = undefined;
      params.unBindTimeEnd = undefined;
    }
    delete params.unbindTimeRange;
    
    // 处理门店名称查询参数（多选时逗号拼接门店ID）
    if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
      // 如果选择了门店，将ID数组用逗号拼接成字符串
      params.storeName = params.storeName.join(',');
    } else {
      // 如果没有选择门店，传 null
      params.storeName = null;
    }
    
    return params;
  }

  // 查询数据
  async function queryData() {
    try {
      tableLoading.value = true;
      
      // 使用统一的参数处理
      const requestParams = prepareQueryParams();
      
      const result = await bankCardAuthorizationApi.queryList(requestParams);
      tableData.value = result.data.list || [];
      total.value = result.data.total || 0;
      
    } catch (error) {
      console.error('查询失败:', error);
      message.error('查询失败');
    } finally {
      tableLoading.value = false;
    }
  }

  // ---------------------------- 操作功能 ----------------------------

  // 查看订单
  function viewOrders(record) {
    // 跳转到银行卡授权订单详情页面
    router.push({
      path: '/order/bank-card-authorization-detail',
      query: { id: record.customerBankcardId }
    });
  }

  // 解绑银行卡
  function unbindCard(record) {
    Modal.confirm({
      title: '提示',
      content: `确定要解绑用户【${record.realName}】的银行卡吗？`,
      okText: '解绑',
      okType: 'danger',
      onOk: async () => {
        try {
          SmartLoading.show();
          await bankCardAuthorizationApi.unbind(record.customerBankcardId);
          message.success('解绑成功');
          queryData();
        } catch (error) {
          console.error('解绑失败:', error);
          message.error('解绑失败');
        } finally {
          SmartLoading.hide();
        }
      }
    });
  }

  // 导出
  async function onExport() {
    message.info('导出功能开发中...');
  }

  // ---------------------------- 表格排序 ----------------------------

  function onChange(pagination, filters, sorter, { action }) {
    if (action === 'sort') {
      const { order, field } = sorter;
      let column = camelToUnderscore(field);
      let findIndex = queryForm.sortItemList.findIndex((e) => e.column === column);
      if (findIndex !== -1) {
        queryForm.sortItemList.splice(findIndex, 1);
      }
      if (order) {
        let isAsc = order !== 'ascend';
        queryForm.sortItemList.push({
          column,
          isAsc,
        });
      }
      queryData();
    }
  }

  function camelToUnderscore(str) {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase();
  }

  // ---------------------------- 动态高度 ----------------------------

  // 动态设置表格高度
  const yHeight = ref(0);

  onMounted(() => {
    queryData();
    resetGetHeight();
  });

  function resetGetHeight() {
    // 搜索部分高度
    let doc = document.querySelector('.ant-form');
    // 按钮部分高度
    let btn = document.querySelector('.smart-table-btn-block');
    // 表格头高度
    let tableCell = document.querySelector('.ant-table-cell');
    // 分页高度
    let page = document.querySelector('.smart-query-table-page');
    // 内容区总高度
    let box = document.querySelector('.admin-content');
    setTimeout(() => {
      let dueHeight = doc.offsetHeight + 10 + 24 + btn.offsetHeight + 15 + tableCell.offsetHeight + page.offsetHeight + 20;
      yHeight.value = box.offsetHeight - dueHeight;
    }, 100);
  }

  window.addEventListener(
    'resize',
    _.throttle(() => {
      resetGetHeight();
    }, 1000)
  );

  // ---------------------------- 数据格式化 ----------------------------

  // 获取绑定状态文本
  function getBindStatusText(status) {
    const statusMap = {
      'BINDED': '授权中',
      'UNBIND': '已解绑',
    };
    return statusMap[status] || '未知';
  }

  // 获取绑定状态颜色
  function getBindStatusColor(status) {
    const colorMap = {
      'BINDED': 'success',
      'UNBIND': 'default',
    };
    return colorMap[status] || 'default';
  }

  // 获取银行卡类型文本
  function getBankCardTypeText(type) {
    const typeMap = {
      'DEBIT_CARD': '储蓄卡',
    };
    return typeMap[type] || type || '未知';
  }

  // 格式化手机号
  function maskPhone(phone) {
    return dataFormatter.maskPhone(phone);
  }

  // 格式化银行卡号
  function maskBankCard(cardNumber) {
    if (!cardNumber) return '-';
    if (cardNumber.length < 8) return cardNumber; // 卡号太短时直接返回
    
    const firstFour = cardNumber.substring(0, 4);
    const lastFour = cardNumber.substring(cardNumber.length - 4);
    const maskLength = Math.max(8, cardNumber.length - 8); // 中间至少8个星号
    const mask = '*'.repeat(maskLength);
    
    return `${firstFour}${mask}${lastFour}`;
  }
</script>

<style scoped>
/* 筛选区域卡片圆角样式 */
.smart-query-form {
  border-radius: 4px;
}
</style>
