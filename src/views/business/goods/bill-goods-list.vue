<!--
  * 账单付商品列表
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 16:30:00
  * @Copyright  SmartAdmin
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="商品名称" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.productName" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="上架状态" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.disabledFlag" 
          placeholder="全部"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option :value="1">已上架</a-select-option>
          <a-select-option :value="0">未上架</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="创建人" class="smart-query-form-item">
        <a-input 
          style="width: 150px" 
          v-model:value="queryForm.createUserName" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="门店名称" class="smart-query-form-item">
        <StoreMultiSelect 
          v-model="queryForm.storeName" 
          width="200px"
          placeholder="请选择门店"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button v-privilege="'business:bill:goods:list:add'" @click="addGoods" type="primary">
          <template #icon>
            <PlusOutlined />
          </template>
          添加
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator 
          v-model="columns" 
          :tableId="TABLE_ID_CONST.BUSINESS.GOODS.BILL_GOODS" 
          :refresh="queryData" 
        />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="productId"
      :scroll="{ x: 1800, y: 2000 }"
      bordered
      :pagination="false"
      :loading="tableLoading"
      :row-selection="{ selectedRowKeys: selectedRowKeyList, onChange: onSelectChange }"
      @resizeColumn="handleResizeColumn"
    >
      <template #headerCell="{ column }">
        <template v-if="column.dataIndex === 'action'">操作</template>
        <template v-else>{{ column.title }}</template>
      </template>

      <template #bodyCell="{ text, record, column }">
        <!-- 商品名称 -->
        <template v-if="column.dataIndex === 'productName'">
          <div class="goods-name-cell">
            <span>{{ text }}</span>
          </div>
        </template>

        <!-- 收款方式 -->
        <template v-if="column.dataIndex === 'receivePaymentType'">
          <span>{{ getPaymentReceiveMethodDesc(text) }}</span>
        </template>

        <!-- 付款方式 -->
        <template v-if="column.dataIndex === 'paymentType'">
          <span>{{ getPaymentMethodDesc(text) }}</span>
        </template>

        <!-- 上架状态 -->
        <template v-if="column.dataIndex === 'disabledFlag'">
          <a-tag :color="text === 1 ? 'green' : 'red'">
            {{ text === 1 ? '上架' : '下架' }}
          </a-tag>
        </template>

        <!-- 是否展示 -->
        <template v-if="column.dataIndex === 'displayFlag'">
          <span>{{ text === 1 ? '是' : '否' }}</span>
        </template>

        <!-- 展示排序 -->
        <template v-if="column.dataIndex === 'listOrder'">
          <span>{{ text || '-' }}</span>
        </template>

        <!-- 商品售价 -->
        <template v-if="column.dataIndex === 'productPrice'">
          <span>{{ text }}</span>
        </template>

        <!-- 分期总数 -->
        <template v-if="column.dataIndex === 'totalInstallmentCount'">
          <span>{{ text }}</span>
        </template>

        <!-- 扣款周期 -->
        <template v-if="column.dataIndex === 'deductPeriodType'">
          <span>{{ getDeductionCycleDesc(text) }}</span>
        </template>

        <!-- 前段期数 -->
        <template v-if="column.dataIndex === 'forepartInstallmentCount'">
          <span>{{ text }}</span>
        </template>

        <!-- 前段每期金额 -->
        <template v-if="column.dataIndex === 'forepartEachAmount'">
          <span>{{ text }}</span>
        </template>

        <!-- 后段每期金额 -->
        <template v-if="column.dataIndex === 'backendEachAmount'">
          <span>{{ text }}</span>
        </template>

        <!-- 创建时间 -->
        <template v-if="column.dataIndex === 'createTime'">
          <span style="white-space: nowrap;">{{ text }}</span>
        </template>

        <!-- 更新时间 -->
        <template v-if="column.dataIndex === 'updateTime'">
          <span style="white-space: nowrap;">{{ text }}</span>
        </template>

        <!-- 创建人 -->
        <template v-if="column.dataIndex === 'actualName'">
          <span>{{ text }}</span>
        </template>

        <!-- 门店名称 -->
        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ text || '-' }}</span>
        </template>

        <!-- 操作 -->
        <template v-if="column.dataIndex === 'action'">
          <div class="smart-table-operate">
            <a-button v-privilege="'business:bill:goods:list:edit'" @click="editGoods(record)" type="link" size="small">编辑</a-button>
            <a-button v-privilege="'business:bill:goods:list:showQrCode'" @click="showQrCode(record)" type="link" size="small">二维码</a-button>
            <a-button v-privilege="'business:bill:goods:list:delete'" @click="deleteGoods(record)" type="link" size="small">删除</a-button>
          </div>
        </template>
      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <!-- 二维码显示弹窗 -->
    <QrCodeModal 
      ref="qrCodeModalRef" 
    />
  </a-card>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { billGoodsApi } from '/@/api/business/goods/bill-goods-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
  import { 
    PAYMENT_RECEIVE_METHOD_ENUM,
    PAYMENT_METHOD_ENUM,
    DEDUCTION_CYCLE_ENUM
  } from '/@/constants/business/goods/bill-goods-const';
  import { 
    SearchOutlined, 
    ReloadOutlined, 
    PlusOutlined
  } from '@ant-design/icons-vue';
  import _ from 'lodash';
  
  // 引入子组件
  import QrCodeModal from './components/qr-code-modal.vue';
  import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';

  const router = useRouter();

  // ----------------------- 以下是reactive的基础数据定义 ---------------------------
  const queryFormState = {
    productName: '', // 商品名称 (直接使用API字段名)
    disabledFlag: '', // 上架状态 (直接使用API字段名)
    createUserName: '', // 创建人
    appType: 'MERCHANT', // 隐藏参数：应用类型
    storeName: [], // 门店名称查询字段
    pageNum: 1,
    pageSize: 10,
    searchCount: true, // API需要的参数
  };
  const queryForm = reactive({ ...queryFormState });
  
  const tableData = ref([]);
  const total = ref(0);
  const tableLoading = ref(false);
  const selectedRowKeyList = ref([]);

  // 弹窗引用
  const qrCodeModalRef = ref();

  // ----------------------- 表格显示列 ---------------------------
  const columns = ref([
    {
      title: '商品名称',
      dataIndex: 'productName',
      width: 200,
      resizable: true,
      fixed: 'left',
    },
    {
      title: '收款方式',
      dataIndex: 'receivePaymentType',
      width: 130,
      resizable: true,
    },
    {
      title: '付款方式',
      dataIndex: 'paymentType',
      width: 160,
      resizable: true,
    },
    {
      title: '上架状态',
      dataIndex: 'disabledFlag',
      width: 100,
      resizable: true,
    },
    {
      title: '是否展示',
      dataIndex: 'displayFlag',
      width: 100,
      resizable: true,
    },
    {
      title: '展示排序',
      dataIndex: 'listOrder',
      width: 100,
      resizable: true,
    },
    {
      title: '商品售价',
      dataIndex: 'productPrice',
      width: 120,
      resizable: true,
    },
    {
      title: '分期总数',
      dataIndex: 'totalInstallmentCount',
      width: 100,
      resizable: true,
    },
    {
      title: '扣款周期',
      dataIndex: 'deductPeriodType',
      width: 100,
      resizable: true,
    },
    {
      title: '前段期数',
      dataIndex: 'forepartInstallmentCount',
      width: 100,
      resizable: true,
    },
    {
      title: '前段每期金额',
      dataIndex: 'forepartEachAmount',
      width: 120,
      resizable: true,
    },
    {
      title: '后段每期金额',
      dataIndex: 'backendEachAmount',
      width: 120,
      resizable: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      width: 180,
      resizable: true,
    },
    {
      title: '更新时间',
      dataIndex: 'updateTime',
      width: 180,
      resizable: true,
    },
    {
      title: '创建人',
      dataIndex: 'actualName',
      width: 100,
      resizable: true,
    },
    {
      title: '门店名称',
      dataIndex: 'storeName',
      width: 120,
      resizable: true,
    },
    {
      title: '操作',
      dataIndex: 'action',
      fixed: 'right',
      width: 150,
    },
  ]);

  // ----------------------- 生命周期函数 ---------------------------
  onMounted(() => {
    queryData();
  });

  // ----------------------- 公共函数 ---------------------------
  
  /**
   * 查询数据
   */
  async function queryData() {
    tableLoading.value = true;
    try {
      console.log('查询参数:', queryForm);
      
      // 使用统一的参数处理
      const requestParams = prepareQueryParams();
      
      console.log('处理后的查询参数:', requestParams);
      
      // 直接调用API，使用API字段名
      let queryResult = await billGoodsApi.queryPage(requestParams);
      const { data } = queryResult;
      
      console.log('API返回数据:', data);
      
      // 直接使用API返回的数据
      tableData.value = data.list || [];
      total.value = data.total || 0;
      selectedRowKeyList.value = [];
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  /**
   * 搜索
   */
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  /**
   * 重置查询表单
   */
  function resetQuery() {
    Object.assign(queryForm, queryFormState);
    onSearch();
  }

  /**
   * 选择行
   */
  function onSelectChange(selectedRowKeys) {
    selectedRowKeyList.value = selectedRowKeys;
  }

  /**
   * 添加商品
   */
  function addGoods() {
    router.push('/goods/bill-goods-form');
  }

  /**
   * 编辑商品
   */
  function editGoods(record) {
    router.push({
      path: '/goods/bill-goods-form',
      query: { goodsId: record.productId } // 使用API字段名productId
    });
  }

  /**
   * 显示二维码
   */
  function showQrCode(record) {
    qrCodeModalRef.value.show(record);
  }

  /**
   * 删除商品
   */
  function deleteGoods(record) {
    Modal.confirm({
      title: '确认删除',
      content: `确定要删除商品"${record.productName}"吗？`,
      onOk: async () => {
        try {
          SmartLoading.show();
          await billGoodsApi.delete(record.productId); // 使用API字段名productId
          message.success('删除成功');
          queryData();
        } catch (e) {
          smartSentry.captureError(e);
        } finally {
          SmartLoading.hide();
        }
      }
    });
  }

  /**
   * 调整列宽
   */
  function handleResizeColumn(w, col) {
    col.width = w;
  }

  // ----------------------- 辅助函数 ---------------------------

  /**
   * 获取收款方式描述
   */
  function getPaymentReceiveMethodDesc(value) {
    const desc = PAYMENT_RECEIVE_METHOD_ENUM[value]?.desc || value;
    console.log('收款方式映射:', value, '->', desc);
    return desc;
  }

  /**
   * 获取付款方式描述
   */
  function getPaymentMethodDesc(value) {
    const desc = PAYMENT_METHOD_ENUM[value]?.desc || value;
    console.log('付款方式映射:', value, '->', desc);
    return desc;
  }

  /**
   * 获取扣款周期描述
   */
  function getDeductionCycleDesc(value) {
    const desc = DEDUCTION_CYCLE_ENUM[value]?.desc || value;
    console.log('扣款周期映射:', value, '->', desc);
    return desc;
  }

  /**
   * 统一参数处理函数
   */
  function prepareQueryParams() {
    const params = _.cloneDeep(queryForm);
    
    // 处理Integer类型参数：空值转null，有值转数字
    if (params.disabledFlag !== '' && params.disabledFlag != null) {
      params.disabledFlag = parseInt(params.disabledFlag);
    } else {
      params.disabledFlag = null;
    }
    
    // 处理门店名称查询参数（多选时逗号拼接门店ID）
    if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
      // 如果选择了门店，将ID数组用逗号拼接成字符串
      params.storeName = params.storeName.join(',');
    } else {
      // 如果没有选择门店，传 null
      params.storeName = null;
    }
    
    return params;
  }

</script>

<style scoped>
.goods-name-cell {
  line-height: 1.4;
}

.smart-table-operate {
  display: flex;
  gap: 8px;
  flex-wrap: nowrap;
  white-space: nowrap;
}

.smart-table-operate .ant-btn-link {
  padding: 0;
  height: auto;
}

/* 筛选区域卡片圆角样式 */
.smart-query-form {
  border-radius: 4px;
}
</style>