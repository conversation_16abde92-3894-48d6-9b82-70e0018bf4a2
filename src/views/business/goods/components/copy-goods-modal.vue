<template>
  <a-modal
    v-model:open="visible"
    title="复制其他商品"
    :width="600"
    :footer="null"
    @cancel="handleCancel"
  >
    <div style="padding: 24px 0;">
      <div style="margin-bottom: 16px; padding: 12px; background-color: #f6f8fa; border-radius: 6px; color: #666;">
        <div style="font-size: 13px;">
          📋 选择一个已有商品作为模板，将复制其所有配置信息到当前商品表单中
        </div>
      </div>
      
      <a-form
        ref="formRef"
        :model="formData"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <!-- 商品类型 -->
        <a-form-item label="商品类型" name="goodsType" :rules="[{ required: true, message: '请选择商品类型', trigger: 'change' }]">
          <a-select 
            v-model:value="formData.goodsType" 
            placeholder="请选择"
            style="width: 100%;"
            @change="onGoodsTypeChange"
          >
            <a-select-option 
              v-for="option in goodsTypeOptions" 
              :key="option.value" 
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
        </a-form-item>

        <!-- 商品名称 -->
        <a-form-item label="商品名称" name="goodsName" :rules="[{ required: true, message: '请选择商品名称', trigger: 'change' }]">
          <a-select 
            v-model:value="formData.goodsName" 
            :placeholder="goodsNameOptions.length === 0 && !loading ? '暂无可复制的商品' : '请选择'"
            style="width: 100%;"
            :disabled="!formData.goodsType"
            :loading="loading"
            @change="onGoodsNameChange"
          >
            <a-select-option 
              v-for="option in goodsNameOptions" 
              :key="option.value" 
              :value="option.value"
            >
              {{ option.label }}
            </a-select-option>
          </a-select>
          <div v-if="goodsNameOptions.length === 0 && !loading && formData.goodsType" style="color: #8c8c8c; font-size: 12px; margin-top: 4px;">
            该类型下暂无可复制的商品
          </div>
        </a-form-item>
      </a-form>

      <!-- 操作按钮 -->
      <div style="text-align: right; margin-top: 24px;">
        <a-space>
          <a-button @click="handleCancel">取消</a-button>
          <a-button type="primary" @click="handleConfirm" :loading="loading">确定</a-button>
        </a-space>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { billGoodsApi } from '/@/api/business/goods/bill-goods-api';

  // ----------------------- Props & Emits ---------------------------
  const props = defineProps({
    open: {
      type: Boolean,
      default: false
    }
  });

  const emit = defineEmits(['update:open', 'confirm']);

  // ----------------------- 数据定义 ---------------------------
  const formRef = ref();
  const loading = ref(false);
  const visible = ref(false);

  const formData = reactive({
    goodsType: '',
    goodsName: ''
  });

  // 商品类型选项
  const goodsTypeOptions = ref([
    { label: '账单付商品', value: 'BANK_SEGMENT' },
    { label: '安心付商品', value: 'ZFB_ANXIN' }
  ]);

  // 商品名称选项
  const goodsNameOptions = ref([]);

  // ----------------------- 监听Props变化 ---------------------------
  watch(() => props.open, (newVal) => {
    visible.value = newVal;
    if (newVal) {
      resetForm();
      // 显示弹窗时，默认选中第一个商品类型
      initDefaultSelection();
    }
  });

  watch(visible, (newVal) => {
    emit('update:open', newVal);
  });

  // ----------------------- 方法 ---------------------------
  
  /**
   * 重置表单
   */
  function resetForm() {
    Object.assign(formData, {
      goodsType: '',
      goodsName: ''
    });
    goodsNameOptions.value = [];
    if (formRef.value) {
      formRef.value.clearValidate();
    }
  }

  /**
   * 初始化默认选择
   */
  async function initDefaultSelection() {
    // 默认选中第一个商品类型
    if (goodsTypeOptions.value.length > 0) {
      formData.goodsType = goodsTypeOptions.value[0].value;
      
      // 自动加载商品名称并选中第一个
      await loadGoodsNamesAndSelectFirst();
    }
  }

  /**
   * 加载商品名称列表并选中第一个
   */
  async function loadGoodsNamesAndSelectFirst() {
    if (!formData.goodsType) {
      return;
    }
    
    try {
      loading.value = true;
      
      // 调用API获取可复制的商品列表
      const result = await billGoodsApi.getCopyProductList({
        receivePaymentType: formData.goodsType
      });
      
      if (result.data && Array.isArray(result.data)) {
        goodsNameOptions.value = result.data.map(item => ({
          label: item.productName,
          value: item.productId
        }));
        
        // 如果有商品，默认选中第一个
        if (goodsNameOptions.value.length > 0) {
          formData.goodsName = goodsNameOptions.value[0].value;
        }
      } else {
        goodsNameOptions.value = [];
      }
    } catch (e) {
      message.error('加载商品名称失败');
      smartSentry.captureError(e);
      goodsNameOptions.value = [];
    } finally {
      loading.value = false;
    }
  }

  /**
   * 商品类型变化
   */
  async function onGoodsTypeChange() {
    // 清空商品名称选择
    formData.goodsName = '';
    goodsNameOptions.value = [];
    
    if (!formData.goodsType) {
      return;
    }
    
    // 复用加载并选中第一个商品的逻辑
    await loadGoodsNamesAndSelectFirst();
  }

  /**
   * 商品名称变化
   */
  function onGoodsNameChange() {
    console.log('选择的商品:', formData.goodsName);
  }

  /**
   * 取消
   */
  function handleCancel() {
    visible.value = false;
  }

  /**
   * 确认
   */
  async function handleConfirm() {
    try {
      // 验证表单
      await formRef.value.validateFields();
      
      loading.value = true;
      
      // 根据选中的商品ID获取商品详情
      const productId = formData.goodsName; // goodsName 实际存储的是 productId
      const result = await billGoodsApi.getDetail(productId);
      
      if (result.data) {
        // 直接使用API返回的数据，清除productId以便作为新商品添加
        const copiedGoodsData = { 
          ...result.data,
          productId: null // 清除ID，作为新商品
        };
        
        // 触发确认事件，传递复制的数据
        emit('confirm', copiedGoodsData);
        
        message.success('商品信息复制成功');
        visible.value = false;
      } else {
        message.error('获取商品详情失败');
      }
      
    } catch (e) {
      if (e.errorFields) {
        message.error('请完善必填信息');
      } else {
        message.error('复制商品失败：' + (e.message || '网络错误'));
        smartSentry.captureError(e);
      }
    } finally {
      loading.value = false;
    }
  }
</script> 