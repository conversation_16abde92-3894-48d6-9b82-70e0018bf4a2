<!--
  * 应收明细表格组件
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 17:00:00
  * @Copyright  SmartAdmin
-->
<template>
  <div class="installment-detail-container">
    <a-table
      :dataSource="installmentList"
      :columns="columns"
      :pagination="false"
      size="small"
      rowKey="period"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'period'">
          {{ text }}
        </template>
        
        <template v-if="column.dataIndex === 'amount'">
          {{ text }}
        </template>
      </template>
    </a-table>
  </div>
</template>

<script setup>
  import { computed, ref } from 'vue';

  // ----------------------- Props ---------------------------
  const props = defineProps({
    totalInstallments: {
      type: Number,
      default: 0
    },
    frontPeriods: {
      type: Number,
      default: 0
    },
    frontPeriodAmount: {
      type: Number,
      default: 0
    },
    backPeriodAmount: {
      type: Number,
      default: 0
    },
    planList: {
      type: Array,
      default: () => []
    }
  });

  // ----------------------- 计算属性 ---------------------------

  // 分期明细列表
  const installmentList = computed(() => {
    // 优先使用API返回的planList数据
    if (props.planList && props.planList.length > 0) {
      return props.planList.map(plan => ({
        period: plan.deductPlanNo,
        amount: plan.deductAmount ? plan.deductAmount.toFixed(2) : '0.00'
      }));
    }
    
    // 回退到原来的计算逻辑
    const list = [];
    
    // 前段期数
    for (let i = 1; i <= props.frontPeriods; i++) {
      list.push({
        period: i,
        amount: props.frontPeriodAmount.toFixed(2)
      });
    }
    
    // 后段期数
    for (let i = props.frontPeriods + 1; i <= props.totalInstallments; i++) {
      list.push({
        period: i,
        amount: props.backPeriodAmount.toFixed(2)
      });
    }
    
    return list;
  });

  // ----------------------- 表格列定义 ---------------------------
  const columns = ref([
    {
      title: '分期',
      dataIndex: 'period'
    },
    {
      title: '应扣金额',
      dataIndex: 'amount'
    }
  ]);
</script>

<style scoped>
.installment-detail-container {
  padding: 0;
}
</style> 