<!--
  * 安心付商品表单页面
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 17:00:00
  * @Copyright  SmartAdmin
-->
<template>
  <div class="goods-form-container">
    <a-card :bordered="false">
      <!-- 页面标题 -->
      <template #title>
        <div class="page-header">
          <span class="page-title">{{ formData.productId ? '编辑商品' : '添加商品' }}</span>
          <!-- 复制其他商品按钮，仅添加商品时显示 -->
          <a-button v-if="!formData.productId" type="primary" @click="showCopyGoodsModal" style="margin-left: auto">
            复制其他商品
          </a-button>
        </div>
      </template>

      <a-form ref="formRef" :model="formData" :rules="rules" :label-col="{ span: 4 }" :wrapper-col="{ span: 12 }" layout="horizontal">
        <!-- 收款方式 -->
        <a-form-item label="收款方式" name="receivePaymentType">
          <a-radio-group v-model:value="formData.receivePaymentType">
            <a-radio value="BANK_SEGMENT">账单付</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 商品名称 -->
        <a-form-item label="商品名称" name="productName">
          <a-input v-model:value="formData.productName" placeholder="请输入" :maxlength="30" @blur="validateGoodsName" />
          <div class="form-tip">商品命名规范：需体现培训课程、提供服务或售卖商品内容，标题不能模糊不清。不能带有分期、贷款、兼职等违规字眼</div>
        </a-form-item>

        <!-- 商品副标题 -->
        <a-form-item label="商品副标题" name="productSubtitle">
          <a-input v-model:value="formData.productSubtitle" placeholder="请输入" :maxlength="50" />
        </a-form-item>

        <!-- 商品售价 -->
        <a-form-item label="商品售价" name="productPrice">
          <a-input-number
            v-model:value="formData.productPrice"
            :min="0"
            :controls="false"
            step="0.01"
            placeholder="请输入"
            style="width: 200px"
            @change="onGoodsPriceChange"
          />
        </a-form-item>

        <!-- 原价 -->
        <a-form-item label="原价" name="productOriginalPrice">
          <a-input-number
            v-model:value="formData.productOriginalPrice"
            :min="formData.productPrice || 0"
            :controls="false"
            step="0.01"
            placeholder="请输入"
            style="width: 200px"
          />
        </a-form-item>

        <!-- 用户端是否显示 -->
        <a-form-item label="用户端是否显示" name="displayFlag">
          <a-radio-group v-model:value="formData.displayFlag">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 是否允许全款 -->
        <a-form-item label="是否允许全款" name="allowFullpayFlag" style="display: none">
          <a-radio-group v-model:value="formData.allowFullpayFlag">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 价格展示方式 -->
        <a-form-item label="价格展示方式" name="priceDisplayType">
          <a-radio-group v-model:value="formData.priceDisplayType">
            <a-radio value="PRICE_DISPLAY_MONTH">仅展示XXX元/月</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 下单售价可编辑 -->
        <a-form-item label="下单售价可编辑" name="editPriceFlag">
          <a-radio-group v-model:value="formData.editPriceFlag" @change="onOrderEditRuleChange">
            <a-radio :value="0">不可编辑</a-radio>
            <a-radio :value="1">可编辑</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 付款方式 -->
        <a-form-item label="付款方式" name="paymentType">
          <a-radio-group v-model:value="formData.paymentType" @change="onPaymentMethodChange">
            <a-radio value="XY_SEGMENT" :disabled="formData.editPriceFlag === 1">分段 (X段+Y段)</a-radio>
            <a-radio value="EQUAL_SEGMENT">等额分段</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 下单绑卡要求 -->
        <a-form-item label="下单绑卡要求" name="orderBindBankFlag">
          <a-radio-group v-model:value="formData.orderBindBankFlag">
            <a-radio :value="1" style="line-height: 32px">必须绑定银行卡授权代扣</a-radio>
            <a-radio :value="0" style="line-height: 32px">可跳过绑定银行卡授权代扣</a-radio>
          </a-radio-group>
          <div class="form-tip">下单办卡要求仅对"账单付"产品生效，对安心付产品不生效</div>
        </a-form-item>

        <!-- 下单身份证 -->
        <a-form-item label="下单身份证是否必填" name="idNoFlag">
          <a-radio-group v-model:value="formData.idNoFlag">
            <a-radio :value="1">是</a-radio>
            <a-radio :value="0">否</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 电子合同模版 -->
        <a-form-item label="电子合同模版" name="contractTemplateId">
          <a-row>
            <a-col span="16">
              <a-select v-model:value="formData.contractTemplateId" placeholder="请选择合同模版" style="width: 100%">
                <a-select-option v-for="template in contractTemplates" :key="template.contractTemplateId" :value="template.contractTemplateId">
                  {{ template.templateName }}
                </a-select-option>
              </a-select>
            </a-col>
            <a-col span="4" style="padding-left: 8px">
              <a-button @click="previewContract">预览</a-button>
            </a-col>
          </a-row>
        </a-form-item>

        <!-- 扣款周期 -->
        <a-form-item label="扣款周期" name="deductPeriodType">
          <a-radio-group v-model:value="formData.deductPeriodType">
            <a-radio value="DEDUCT_BY_MONTH">按月</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 还款日 -->
        <a-form-item label="还款日" name="paymentDayType">
          <div style="display: flex; align-items: center; gap: 24px">
            <a-radio-group v-model:value="formData.paymentDayType" @change="onRepaymentDayTypeChange">
              <!-- 第一个选项：下单日顺延 -->
              <a-radio value="ORDER_DAY_DELAY"> 下单日顺延 </a-radio>
              <!-- 第二个选项：固定每月 -->
              <a-radio value="FIX_PAYMENT_DAY"> 固定每月 </a-radio>
            </a-radio-group>
            
            <!-- 下单日顺延的数值选择 -->
            <div v-if="formData.paymentDayType === 'ORDER_DAY_DELAY'" style="display: flex; align-items: center">
              <a-select
                v-model:value="formData.paymentDay"
                style="width: 80px; margin-right: 8px"
              >
                <a-select-option v-for="day in 90" :key="day" :value="day">{{ day }}</a-select-option>
              </a-select>
              <span>天</span>
            </div>
            
            <!-- 固定每月的数值选择 -->
            <div v-if="formData.paymentDayType === 'FIX_PAYMENT_DAY'" style="display: flex; align-items: center">
              <a-select
                v-model:value="formData.paymentDay"
                style="width: 80px; margin-right: 8px"
              >
                <a-select-option v-for="day in 28" :key="day" :value="day">{{ day }}</a-select-option>
              </a-select>
              <span>日</span>
            </div>
          </div>
        </a-form-item>

        <!-- 首期扣款规则 -->
        <a-form-item label="首期扣款规则" name="firstDeductRuleType">
          <a-radio-group v-model:value="formData.firstDeductRuleType">
            <a-radio value="WAIT_DEDUCT_DAY">等到扣款日扣款</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 可选期数规则 -->
        <a-form-item label="可选期数规则" name="optionalPeriodType">
          <a-radio-group v-model:value="formData.optionalPeriodType">
            <a-radio value="FIX_TOTAL_PERIOD">固定总期数</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 总分期数 -->
        <a-form-item label="总分期数" name="totalInstallmentCount">
          <a-input-number
            v-model:value="formData.totalInstallmentCount"
            :min="2"
            :max="24"
            :controls="true"
            style="width: 150px"
            @change="onTotalInstallmentsChange"
          />
        </a-form-item>

        <!-- 前段期数 -->
        <a-form-item label="前段期数" name="forepartInstallmentCount">
          <a-input-number
            v-model:value="formData.forepartInstallmentCount"
            :min="1"
            :max="frontPeriodsMax"
            :controls="true"
            :disabled="formData.paymentType === 'EQUAL_SEGMENT'"
            style="width: 150px"
            @change="onFrontPeriodsChange"
          />
        </a-form-item>

        <!-- 前段每期金额 -->
        <a-form-item label="前段每期金额" name="forepartEachAmount">
          <a-input-number
            v-model:value="formData.forepartEachAmount"
            :min="0.01"
            :max="formData.productPrice || 999999"
            :precision="2"
            :controls="true"
            :disabled="formData.paymentType === 'EQUAL_SEGMENT'"
            style="width: 200px"
            @change="onFrontPeriodAmountChange"
          />
        </a-form-item>

        <!-- 后段每期金额 -->
        <a-form-item label="后段每期金额" name="backendEachAmount">
          <a-space>
            <a-input :value="formatMoney(formData.backendEachAmount)" readonly style="width: 150px; text-align: center; background-color: #f5f5f5; cursor: not-allowed" />
            <a-button @click="showInstallmentDetail">应收明细</a-button>
          </a-space>
        </a-form-item>

        <!-- 商品图片 -->
        <a-form-item label="商品图片" name="productPicUrl">
          <a-upload
            name="file"
            list-type="picture-card"
            class="goods-image-uploader"
            :show-upload-list="false"
            :before-upload="beforeUpload"
            :customRequest="customRequest"
          >
            <div v-if="imageUrl" class="image-container">
              <a-image :src="imageUrl" class="uploaded-image" alt="商品图片" />
              <div class="image-overlay">
                <div class="overlay-actions">
                  <span @click.stop="removeImage">删除</span>
                </div>
              </div>
            </div>
            <div v-else class="upload-placeholder">
              <loading-outlined v-if="uploading" />
              <plus-outlined v-else />
              <div style="margin-top: 8px">{{ uploading ? '上传中...' : '点击上传' }}</div>
            </div>
          </a-upload>
          <div class="form-tip">图片建议尺寸 750*750px，图片大小不得超过 800KB，仅支持 jpg/jpeg/png 格式，最多上传1张</div>
        </a-form-item>

        <!-- 上架状态 -->
        <a-form-item label="上架状态" name="disabledFlag">
          <a-radio-group v-model:value="formData.disabledFlag">
            <a-radio :value="1">上架</a-radio>
            <a-radio :value="0">下架</a-radio>
          </a-radio-group>
        </a-form-item>

        <!-- 列表排序 -->
        <a-form-item label="列表排序" name="listOrder">
          <a-input-number v-model:value="formData.listOrder" :min="0" style="width: 150px" />
        </a-form-item>

        <!-- 商品描述 -->
        <a-form-item label="商品描述" name="remark">
          <a-textarea v-model:value="formData.remark" placeholder="请输入备注（选填），最多可输入 200 字" :rows="4" :maxlength="200" show-count />
        </a-form-item>

        <!-- 操作按钮 -->
        <a-form-item :wrapper-col="{ span: 12, offset: 4 }">
          <a-space>
            <a-button type="primary" @click="handleSubmit" :loading="loading"> 保存 </a-button>
            <a-button @click="handleCancel"> 取消 </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 应收明细弹窗 -->
    <a-modal v-model:open="installmentDetailVisible" title="应收明细" :width="600" :footer="null">
      <InstallmentDetailTable 
        :total-installments="formData.totalInstallmentCount"
        :front-periods="formData.forepartInstallmentCount"
        :front-period-amount="formData.forepartEachAmount"
        :back-period-amount="formData.backendEachAmount"
        :plan-list="installmentPlanList"
      />
    </a-modal>



    <!-- 复制其他商品弹窗 -->
    <CopyGoodsModal v-model:open="copyGoodsModalVisible" @confirm="handleCopyGoodsConfirm" />


  </div>
</template>

<script setup>
  import { ref, reactive, computed, onMounted } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { PlusOutlined, LoadingOutlined } from '@ant-design/icons-vue';
  import { billGoodsApi } from '/@/api/business/goods/anxin-goods-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { dataFormatter } from '/@/utils/data-formatter';
  import InstallmentDetailTable from './components/installment-detail-table.vue';
  import CopyGoodsModal from './components/copy-goods-modal.vue';
  import { useUserStore } from '/@/store/modules/system/user';

  const route = useRoute();
  const router = useRouter();
  const userStore = useUserStore();

  // ----------------------- 数据定义 ---------------------------
  const loading = ref(false);
  const formRef = ref();
  const installmentDetailVisible = ref(false);
  const copyGoodsModalVisible = ref(false);
  const imageUrl = ref('');
  const uploading = ref(false);
  const contractTemplates = ref([]);
  const installmentPlanList = ref([]);

  const formData = reactive({
    productId: null, // 商品ID
    receivePaymentType: 'BANK_SEGMENT', // 收款方式
    appType: 'HUANLEFU', // 隐藏参数：应用类型
    productName: '', // 商品名称
    productSubtitle: '', // 商品副标题
    productPrice: undefined, // 商品售价
    productOriginalPrice: undefined, // 商品原价
    displayFlag: 1, // 用户端是否显示（1是 0否）
    allowFullpayFlag: 0, // 是否允许全款（1是 0否）
    priceDisplayType: 'PRICE_DISPLAY_MONTH', // 价格展示方式
    editPriceFlag: 0, // 下单售价是否可编辑（1是 0否）
    paymentType: 'XY_SEGMENT', // 付款方式
    orderBindBankFlag: 1, // 下单绑卡要求（1必须绑定 0可跳过）
    idNoFlag: 1, // 下单身份证（1是 0否）
    deductPeriodType: 'DEDUCT_BY_MONTH', // 扣款周期
    paymentDayType: 'ORDER_DAY_DELAY', // 还款日类型
    paymentDay: 30, // 还款日
    firstDeductRuleType: 'WAIT_DEDUCT_DAY', // 首期扣款规则
    optionalPeriodType: 'FIX_TOTAL_PERIOD', // 可选期数规则
    totalInstallmentCount: 12, // 总分期数
    forepartInstallmentCount: 1, // 前段期数
    forepartEachAmount: 0.01, // 前段每期金额
    backendEachAmount: 0.0, // 后段每期金额
    productPicUrl: '', // 商品图片
    disabledFlag: 1, // 上架状态（1上架 0下架）
    listOrder: 100, // 列表排序
    remark: '', // 商品描述
    contractTemplateId: null, // 合同模版ID
  });

  // 表单验证规则
  const rules = {
    receivePaymentType: [{ required: true, message: '请选择收款方式', trigger: 'change' }],
    productName: [
      { required: true, message: '请输入商品名称', trigger: 'blur' },
      { min: 2, max: 30, message: '商品名称长度在2到30个字符', trigger: 'blur' },
    ],
    productSubtitle: [{ min: 2, max: 50, message: '商品副标题长度在2到50个字符', trigger: 'blur' }],
    productPrice: [
      { required: true, message: '请输入商品售价', trigger: 'blur' },
      { type: 'number', min: 0, message: '商品售价必须大于等于0', trigger: 'blur' },
    ],
    displayFlag: [{ required: true, message: '请选择用户端是否显示', trigger: 'change' }],
    allowFullpayFlag: [{ required: true, message: '请选择是否允许全款', trigger: 'change' }],
    priceDisplayType: [{ required: true, message: '请选择价格展示方式', trigger: 'change' }],
    editPriceFlag: [{ required: true, message: '请选择下单售价可编辑', trigger: 'change' }],
    paymentType: [{ required: true, message: '请选择付款方式', trigger: 'change' }],
    orderBindBankFlag: [{ required: true, message: '请选择下单绑卡要求', trigger: 'change' }],
    idNoFlag: [{ required: true, message: '请选择下单身份证', trigger: 'change' }],
    contractTemplateId: [{ required: true, message: '请选择电子合同模版', trigger: 'change' }],
    deductPeriodType: [{ required: true, message: '请选择扣款周期', trigger: 'change' }],
    paymentDayType: [{ required: true, message: '请选择还款日', trigger: 'change' }],
    firstDeductRuleType: [{ required: true, message: '请选择首期扣款规则', trigger: 'change' }],
    optionalPeriodType: [{ required: true, message: '请选择可选期数规则', trigger: 'change' }],
    totalInstallmentCount: [
      { required: true, message: '请输入总分期数', trigger: 'blur' },
      { type: 'number', min: 2, max: 24, message: '总分期数在2到24期之间', trigger: 'blur' },
    ],
    forepartInstallmentCount: [{ required: true, message: '请输入前段期数', trigger: 'blur' }],
    forepartEachAmount: [
      { required: true, message: '请输入前段每期金额', trigger: 'blur' },
      { type: 'number', min: 0.01, message: '前段每期金额必须大于0', trigger: 'blur' },
    ],

    disabledFlag: [{ required: true, message: '请选择上架状态', trigger: 'change' }],
  };

  // ----------------------- 计算属性 ---------------------------
  const frontPeriodsMax = computed(() => {
    return Math.max(1, (formData.totalInstallmentCount || 1) - 1);
  });

  // ----------------------- 生命周期 ---------------------------
  onMounted(async () => {
    // 先加载合同模版列表
    await loadContractTemplates();
    
    // 再加载商品详情（如果是编辑模式）
    const goodsId = route.query.goodsId;
    if (goodsId) {
      await loadGoodsDetail(goodsId);
    } else {
      // 新增模式下，初始化时也调用API计算分期金额
      await calculateAmountFromApi();
    }
  });

  // ----------------------- 方法 ---------------------------
  
  /**
   * 获取当前门店ID
   */
  function getCurrentStoreId() {
    const currentStore = userStore.getCurrentStore();
    if (currentStore && currentStore.id) {
      return currentStore.id;
    }
    message.warning('获取门店信息失败，请重新登录');
    return null;
  }

  /**
   * 加载合同模版列表
   */
  async function loadContractTemplates() {
    try {
      const storeId = getCurrentStoreId();
      if (!storeId) {
        return;
      }

      const result = await billGoodsApi.getContractTemplates(storeId);
      
      if (result.data && Array.isArray(result.data)) {
        contractTemplates.value = result.data;
      } else {
        contractTemplates.value = [];
      }
    } catch (error) {
      console.error('加载合同模版失败:', error);
      smartSentry.captureError(error);
      message.error('加载合同模版失败');
      contractTemplates.value = [];
    }
  }
  
  /**
   * 加载商品详情
   */
  async function loadGoodsDetail(goodsId) {
    try {
      loading.value = true;
      const result = await billGoodsApi.getDetail(goodsId);

      if (result.data) {
        // 直接将API返回的数据赋值给formData
        Object.assign(formData, result.data);
      
        // 处理合同模版匹配
        if (result.data.contractTemplateId) {
          // 在合同列表中查找匹配的合同
          const matchedTemplate = contractTemplates.value.find(
            template => template.contractTemplateId == result.data.contractTemplateId
          );
          
          if (matchedTemplate) {
            // 找到匹配的合同，选中它
            formData.contractTemplateId = matchedTemplate.contractTemplateId;
          } else {
            // 找不到匹配的合同，设置为null
            formData.contractTemplateId = null;
          }
        } else {
          // 商品详情中没有合同ID，设置为null
          formData.contractTemplateId = null;
        }
        
        // 单独处理图片显示
        if (formData.productPicUrl) {
          imageUrl.value = formData.productPicUrl;
        }
        
        // 加载完商品详情后，调用API重新计算分期金额
        await calculateAmountFromApi();
      }
    } catch (e) {
      smartSentry.captureError(e);
      message.error('加载商品详情失败');
    } finally {
      loading.value = false;
    }
  }

  /**
   * 显示复制商品弹窗
   */
  function showCopyGoodsModal() {
    copyGoodsModalVisible.value = true;
  }

  /**
   * 处理复制商品确认
   */
  function handleCopyGoodsConfirm(copiedGoodsData) {
    // 保存当前页面固定的appType
    const currentAppType = formData.appType;
    
    // 填充到当前表单
    Object.assign(formData, copiedGoodsData);
    
    // 恢复当前页面固定的appType，不被复制的数据覆盖
    formData.appType = currentAppType;
    
    // 处理合同模版匹配
    if (copiedGoodsData.contractTemplateId) {
      // 在合同列表中查找匹配的合同
      const matchedTemplate = contractTemplates.value.find(
        template => template.contractTemplateId == copiedGoodsData.contractTemplateId
      );
      
      if (matchedTemplate) {
        // 找到匹配的合同，选中它
        formData.contractTemplateId = matchedTemplate.contractTemplateId;
      } else {
        // 找不到匹配的合同，设置为null
        formData.contractTemplateId = null;
      }
    } else {
      // 复制的商品没有合同ID，设置为null
      formData.contractTemplateId = null;
    }
    
    // 单独处理图片显示（与编辑功能保持一致）
    if (formData.productPicUrl) {
      imageUrl.value = formData.productPicUrl;
    } else {
      imageUrl.value = '';
    }
    
    // 根据付款方式重新计算分期金额
    calculateAmountFromApi();
  }

  /**
   * 验证商品名称
   */
  function validateGoodsName() {
    const forbiddenWords = ['分期', '贷款', '兼职'];
    const hasFound = forbiddenWords.some((word) => formData.productName.includes(word));
    if (hasFound) {
      message.error('商品名称不能带有分期、贷款、兼职等违规字眼');
      return false;
    }
    return true;
  }

  /**
   * 商品售价变化
   */
  function onGoodsPriceChange() {
    // 调用后端API计算分期金额
    calculateAmountFromApi();
  }

  /**
   * 下单售价可编辑规则变化
   */
  function onOrderEditRuleChange() {
    if (formData.editPriceFlag === 1) {
      // 选择可编辑时，付款方式只能选择等额分段
      formData.paymentType = 'EQUAL_SEGMENT';
      onPaymentMethodChange();
    }
  }

  /**
   * 付款方式变化
   */
  function onPaymentMethodChange() {
    if (formData.paymentType === 'EQUAL_SEGMENT') {
      // 等额分段：前段期数固定为1
      formData.forepartInstallmentCount = 1;
    }
    // 调用后端API计算分期金额
    calculateAmountFromApi();
  }

  /**
   * 还款日类型改变
   */
  function onRepaymentDayTypeChange(e) {
    // 支持事件对象和直接传值
    const type = e?.target?.value || e;
    if (type) {
      formData.paymentDayType = type;
    }

    if (formData.paymentDayType === 'ORDER_DAY_DELAY') {
      formData.paymentDay = 30;
    } else {
      formData.paymentDay = 1;
    }
  }

  /**
   * 总分期数变化
   */
  function onTotalInstallmentsChange() {
    // 确保前段期数不超过限制
    if (formData.forepartInstallmentCount >= formData.totalInstallmentCount) {
      formData.forepartInstallmentCount = Math.max(1, formData.totalInstallmentCount - 1);
    }
    
    // 调用后端API计算分期金额
    calculateAmountFromApi();
  }

  /**
   * 前段期数变化
   */
  function onFrontPeriodsChange() {
    // 自动调整范围
    if (formData.forepartInstallmentCount < 1) {
      formData.forepartInstallmentCount = 1;
    }
    if (formData.forepartInstallmentCount >= formData.totalInstallmentCount) {
      formData.forepartInstallmentCount = Math.max(1, formData.totalInstallmentCount - 1);
    }
    
    // 调用后端API计算分期金额
    calculateAmountFromApi();
  }

  /**
   * 前段每期金额变化
   */
  function onFrontPeriodAmountChange() {
    // 自动调整范围
    if (formData.forepartEachAmount < 0.01) {
      formData.forepartEachAmount = 0.01;
    }
    if (formData.productPrice && formData.forepartEachAmount > formData.productPrice) {
      // 设置为合理的最大值：总价的95%，避免后段金额过小
      formData.forepartEachAmount = Math.round(formData.productPrice * 0.95 * 100) / 100;
      message.warning('前段每期金额不能超过商品总价的95%');
    }
    
    // 调用后端API计算分期金额
    calculateAmountFromApi();
  }

  /**
   * 调用后端API计算分期金额
   */
  async function calculateAmountFromApi() {
    // 检查必要的参数是否存在
    if (!formData.productPrice || !formData.totalInstallmentCount || !formData.forepartInstallmentCount) {
      return;
    }

    try {
      const params = {
        editPriceFlag: formData.editPriceFlag,
        paymentType: formData.paymentType,
        deductPeriodType: formData.deductPeriodType,
        paymentDayType: formData.paymentDayType,
        paymentDay: formData.paymentDay,
        productPrice: formData.productPrice,
        totalInstallmentCount: formData.totalInstallmentCount,
        forepartInstallmentCount: formData.forepartInstallmentCount,
        // 注意：等额分段情况下，前段每期金额传0
        forepartEachAmount: formData.paymentType === 'EQUAL_SEGMENT' ? 0 : formData.forepartEachAmount
      };

      const result = await billGoodsApi.calculateAmount(params);
      
      if (result.data) {
        // 更新表单数据
        formData.forepartInstallmentCount = result.data.forepartInstallmentCount;
        formData.forepartEachAmount = result.data.forepartEachAmount;
        formData.backendEachAmount = result.data.backendEachAmount;
        
        // 可以将扣款计划保存到组件中，供应收明细使用
        installmentPlanList.value = result.data.planList || [];
      }
    } catch (error) {
      console.error('计算分期金额失败:', error);
      // 计算失败时不显示错误提示，避免影响用户体验
    }
  }

  /**
   * 显示应收明细
   */
  function showInstallmentDetail() {
    installmentDetailVisible.value = true;
  }

  /**
   * 预览合同
   */
  function previewContract() {
    if (!formData.contractTemplateId) {
      message.warning('请先选择合同模版');
      return;
    }
    
    // 根据选中的合同模版ID查找对应的预览URL
    const selectedTemplate = contractTemplates.value.find(
      template => template.contractTemplateId === formData.contractTemplateId
    );
    
    if (selectedTemplate && selectedTemplate.previewUrl) {
      // 使用新窗口打开预览URL
      window.open(selectedTemplate.previewUrl, '_blank');
    } else {
      message.error('该合同模版暂无预览地址');
    }
  }

  /**
   * 图片上传前验证
   */
  function beforeUpload(file) {
    const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/jpg' || file.type === 'image/png';
    if (!isJpgOrPng) {
      message.error('只能上传 JPG/PNG 格式的图片!');
      return false;
    }
    const isLt800K = file.size / 1024 < 800;
    if (!isLt800K) {
      message.error('图片大小不能超过 800KB!');
      return false;
    }
    
    // 如果已经有图片，提示替换
    if (imageUrl.value) {
      message.warning('将替换当前图片');
    }
    
    return true; // 允许上传
  }

  /**
   * 自定义上传请求
   */
  async function customRequest(options) {
    uploading.value = true;
      try {
        // 调用上传API
      const response = await billGoodsApi.uploadFile(options.file);

        if (response.data && response.data.fileUrl) {
          // 上传成功，保存图片URL
        formData.productPicUrl = response.data.fileUrl;
          imageUrl.value = response.data.fileUrl;
          
          // 图片上传成功，无需额外验证
          
          message.success('图片上传成功');
          
          console.log('图片上传成功:', {
            fileId: response.data.fileId,
            fileName: response.data.fileName,
            fileUrl: response.data.fileUrl,
          fileSize: response.data.fileSize,
          });
        } else {
          message.error('图片上传失败，请重试');
        }
      } catch (error) {
        console.error('图片上传失败:', error);
        message.error('图片上传失败：' + (error.message || '网络错误'));
    } finally {
      uploading.value = false;
    }
  }

  /**
   * 移除图片
   */
  function removeImage() {
    formData.productPicUrl = '';
    imageUrl.value = '';
    
    // 图片已移除
    
    message.success('图片已移除');
  }



  /**
   * 提交表单
   */
  async function handleSubmit() {
    try {
      // 防止重复提交
      if (loading.value) {
        return;
      }

      // 验证商品名称
      if (!validateGoodsName()) {
        return;
      }
      
      await formRef.value.validate();

      loading.value = true;

      // 因为formData已经和API一致，可以直接使用
      if (formData.productId) {
        // 编辑
        await billGoodsApi.update(formData);
        message.success('编辑成功');
      } else {
        // 添加
        await billGoodsApi.add(formData);
        message.success('添加成功');
      }

      // 返回列表页
      router.back();
    } catch (e) {
      if (e.errorFields) {
        // 表单验证失败，不需要额外处理
        return;
      }
      
      // API调用失败
      console.error('保存商品失败:', e);
      smartSentry.captureError(e);
      
      // 显示用户友好的错误信息
      if (e.message) {
        message.error('保存失败：' + e.message);
      } else if (e.response?.data?.message) {
        message.error('保存失败：' + e.response.data.message);
      } else {
        message.error('保存失败，请检查网络连接或稍后重试');
      }
    } finally {
      loading.value = false;
    }
  }

  /**
   * 取消操作
   */
  function handleCancel() {
    router.back();
  }

  /**
   * 格式化金额
   */
  function formatMoney(value) {
    return dataFormatter.formatMoney(value);
  }
</script>

<style scoped>
.goods-form-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.page-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.form-tip {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.4;
}

.error-tip {
  color: #ff4d4f;
}

.goods-image-uploader .ant-upload {
  width: 104px;
  height: 104px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-placeholder {
  text-align: center;
  color: #8c8c8c;
}

  .image-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 6px;
  }

  .uploaded-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .image-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    opacity: 0;
    transition: opacity 0.3s ease;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .image-container:hover .image-overlay {
    opacity: 1;
  }

  .overlay-actions {
    display: flex;
    gap: 16px;
  }

  .overlay-actions span {
    color: white;
    cursor: pointer;
    padding: 4px 8px;
    border-radius: 4px;
    background-color: rgba(255, 255, 255, 0.2);
    transition: background-color 0.2s;
  }

  .overlay-actions span:hover {
    background-color: rgba(255, 255, 255, 0.3);
  }



:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-card-head-title) {
  padding: 16px 0;
}
</style>