<template>
  <div class="basic-info-container">
    <a-card :bordered="false" :loading="loading">
      <!-- 页面标题和操作按钮 -->
      <template #title>
        <div class="page-header">
          <span>基本信息</span>
          <a-button type="primary" @click="editInfo">编辑</a-button>
        </div>
      </template>

      <!-- 主体信息 -->
      <div class="info-section">
        <h3 class="section-title">主体信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">主体类型:</label>
            <span class="info-value">{{ getAccTypeDesc(basicInfo.accType) }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">营业执照:</label>
            <span class="info-value">
              <a-button type="link" class="link-button" @click="viewLicense">查看</a-button>
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">营业执照名称:</label>
            <span class="info-value">{{ basicInfo.businessLicenseName }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">统一社会信用代码:</label>
            <span class="info-value">{{ basicInfo.socialCreditCode }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">注册资本:</label>
            <span class="info-value">{{ formatRegisterCapital(basicInfo.registerCapital) }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">成立日期:</label>
            <span class="info-value">{{ basicInfo.establishmentDate || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">商户简称:</label>
            <span class="info-value">{{ basicInfo.storeName }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">门店Logo:</label>
            <span class="info-value">
              <a-button type="link" class="link-button" @click="viewStoreLogo" v-if="basicInfo.storeLogo">查看</a-button>
              <span v-else>未设置</span>
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">经营类目:</label>
            <span class="info-value">{{ getBusinessCategoryDesc(basicInfo.businessCategory) }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">资质照片 (原件):</label>
            <span class="info-value">
              <a-button type="link" class="link-button" @click="viewQualifications">查看</a-button>
            </span>
          </div>
        </div>
      </div>

      <a-divider />

      <!-- 法人信息 -->
      <div class="info-section">
        <h3 class="section-title">法人信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">法人身份证人像面:</label>
            <span class="info-value">
              <a-button type="link" class="link-button" @click="viewIdCardFront">查看</a-button>
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">法人身份证国徽面:</label>
            <span class="info-value">
              <a-button type="link" class="link-button" @click="viewIdCardBack">查看</a-button>
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">法人姓名:</label>
            <span class="info-value">{{ basicInfo.corporateName }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">法人身份证号:</label>
            <span class="info-value">{{ basicInfo.corporateCertId }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">法人证件有效期:</label>
            <span class="info-value">{{ basicInfo.corporateIdcardValidityPeriod || '未设置' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">法人手机号:</label>
            <span class="info-value">{{ basicInfo.corporateMobile }}</span>
          </div>
        </div>
      </div>

      <a-divider />

      <!-- 经营信息 -->
      <div class="info-section">
        <h3 class="section-title">经营信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">门头照:</label>
            <span class="info-value">
              <a-button type="link" class="link-button" @click="viewStorefront">查看</a-button>
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">内景照:</label>
            <span class="info-value">
              <a-button type="link" class="link-button" @click="viewInterior">查看</a-button>
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">门店地址:</label>
            <span class="info-value">{{ getStoreAddressDesc() }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">详细地址:</label>
            <span class="info-value">{{ basicInfo.storeAddress }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">联系电话:</label>
            <span class="info-value">{{ basicInfo.contactPhone }}</span>
          </div>
        </div>
      </div>

      <a-divider />

      <!-- 管理员信息 -->
      <div class="info-section">
        <h3 class="section-title">管理员信息</h3>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">管理员姓名:</label>
            <span class="info-value">{{ basicInfo.adminName }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">管理员手机号:</label>
            <span class="info-value">{{ basicInfo.adminPhone }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">管理员邮箱:</label>
            <span class="info-value">{{ basicInfo.adminEmail }}</span>
          </div>
        </div>
      </div>

      <!-- 图片预览模态框 -->
      <a-modal
        v-model:open="imageModalVisible"
        :title="imageModalTitle"
        :footer="null"
        width="600px"
        centered
      >
        <div class="image-preview">
          <a-image
            :src="currentImageUrl"
            :preview="false"
            style="width: 100%"
          />
        </div>
      </a-modal>

      <!-- 小程序信息编辑弹窗 -->
      <a-modal
        v-model:open="miniProgramModalVisible"
        title="商家独立小程序"
        :width="500"
        centered
        @ok="saveMiniProgramInfo"
        @cancel="cancelMiniProgramEdit"
        okText="确定"
        cancelText="取消"
        :confirm-loading="miniProgramLoading"
      >
        <div style="padding: 20px 0;">
          <a-form
            ref="miniProgramFormRef"
            :model="miniProgramForm"
            :rules="miniProgramRules"
            layout="vertical"
          >
            <a-form-item label="APPID" name="appId">
              <a-input v-model:value="miniProgramForm.appId" placeholder="请输入" />
            </a-form-item>
          </a-form>
        </div>
      </a-modal>
    </a-card>

    <!-- 小程序信息卡片 -->
    <a-card :bordered="false" style="margin-top: 16px;">
      <template #title>
        <div class="page-header">
          <span>小程序信息</span>
          <a-button type="primary" @click="editMiniProgramInfo">编辑</a-button>
        </div>
      </template>

      <!-- 小程序信息 -->
      <div class="info-section">
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">商家独立小程序:</label>
            <span class="info-value">{{ basicInfo.appId || '未设置' }}</span>
          </div>
        </div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { EditOutlined, ReloadOutlined } from '@ant-design/icons-vue';
import { basicInfoApi } from '/@/api/business/merchant-settings/basic-info-api';
import { 
  ENTITY_TYPE_ENUM, 
  MERCHANT_TYPE_ENUM, 
  BUSINESS_CATEGORY_ENUM,
  IMAGE_TYPE_ENUM 
} from '/@/constants/business/merchant-settings/basic-info-const';
import { smartSentry } from '/@/lib/smart-sentry';
import addressOptions from '/@/utils/cascader-address-options';
import { useUserStore } from '/@/store/modules/system/user';

// 路由
const router = useRouter();

// 获取用户store
const userStore = useUserStore();

// 图片预览
const imageModalVisible = ref(false);
const imageModalTitle = ref('');
const currentImageUrl = ref('');

// 小程序信息编辑弹窗
const miniProgramModalVisible = ref(false);
const miniProgramFormRef = ref(null);
const miniProgramForm = ref({
  appId: '',
});

// 小程序信息编辑弹窗加载状态
const miniProgramLoading = ref(false);

// 小程序信息编辑弹窗验证规则
const miniProgramRules = {
  appId: [
    { required: true, message: '请输入APPID', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9]{1,32}$/, message: 'APPID只能包含字母、数字，长度1-32位', trigger: 'blur' },
  ],
};

// 基本信息数据 - 完全对应API返回字段
const basicInfo = ref({
  // === API字段 - StoreVO ===
  
  // 基础信息
  storeId: 0, // integer<int64> - 门店id
  merchantId: 0, // integer<int64> - 商户id
  storeName: '', // string - 门店名称(商户简称)
  
  // 地址信息
  storeProvinceNo: '', // string - 省编码
  storeProvince: '', // string - 省名称
  storeCityNo: '', // string - 市编码
  storeCity: '', // string - 市名称
  storeCountyNo: '', // string - 县区编码
  storeCounty: '', // string - 县区名称
  storeAddress: '', // string - 详细地址
  
  // 门店图片
  storeLogo: '', // string - 门店logo
  doorPhoto: '', // string - 门头照
  interiorPhone: '', // string - 内景照
  
  // 营业执照信息
  businessLicensePic: '', // string - 营业执照
  businessLicenseName: '', // string - 营业执照名称
  socialCreditCode: '', // string - 统一社会信用代码
  
  // 经营信息
  businessCategory: '', // string - 经营类目
  qualificationDocument: 0, // integer - 资质文件
  qualificationPic: '', // string - 资质文件照片（原件）
  
  // 法人信息
  corporateIdcardFrontPic: '', // string - 法人身份证人像面
  corporateIdcardBackPic: '', // string - 法人身份证国徽面
  corporateName: '', // string - 法人姓名
  corporateCertId: '', // string - 法人身份证号
  corporateIdcardValidityPeriod: '', // string - 法人证件有效期
  corporateMobile: '', // string - 法人手机号
  
  // 管理员信息
  adminName: '', // string - 管理员姓名
  adminPhone: '', // string - 管理员手机号
  adminEmail: '', // string - 管理员邮箱
  loginPassword: '', // string - 登录密码
  contactPhone: '', // string - 联系电话
  
  // 财务信息
  corporateAccount: '', // string - 对公账户名称
  bankName: '', // string - 开户银行
  depositBankProvince: '', // string - 开户行省份
  depositBankCity: '', // string - 开户行城市
  depositBankName: '', // string - 开户行支行
  cardNo: '', // string - 账户号码
  
  // 状态信息
  certificateStatus: '', // string - 认证状态
  disabledFlag: 0, // integer - 禁用状态0否1是
  deletedFlag: 0, // integer - 是否删除0否1是
  dataType: 0, // integer
  
  accType: 0, // integer - 主体类型:1-个人,2-企业
  registerCapital: 0, // integer - 注册资本(万元)
  establishmentDate: '', // string - 成立日期
  appId: '', // string - 小程序APPID
});

// 加载状态
const loading = ref(false);

// 获取当前门店ID
const getCurrentStoreId = () => {
  const currentStore = userStore.getCurrentStore();
  if (currentStore && currentStore.id) {
    return currentStore.id;
  }
  message.warning('获取门店信息失败，请重新登录');
  return null;
};

// 生命周期
onMounted(() => {
  loadBasicInfo();
});

// 加载基本信息
const loadBasicInfo = async () => {
  try {
    loading.value = true;
    
    // 获取当前门店ID
    const currentStoreId = getCurrentStoreId();
    if (!currentStoreId) {
      return;
    }
    
    console.log('获取基本信息的门店ID:', currentStoreId);
    const result = await basicInfoApi.getStoreBasicInfo(currentStoreId);
    console.log('基本信息API响应:', result);
    
    if (result.data) {
      // 直接使用API返回的所有字段，不做映射
      Object.assign(basicInfo.value, result.data);
    }
  } catch (error) {
    console.error('加载基本信息失败:', error);
    smartSentry.captureError(error);
    if (error.response) {
      message.error(error.response.data?.msg || '加载基本信息失败');
    } else {
      message.error(error.message || '网络请求失败');
    }
  } finally {
    loading.value = false;
  }
};

// 查看图片方法
const showImage = async (title, fieldName) => {
  try {
    imageModalTitle.value = title;
    
    // 直接使用API返回的图片URL
    currentImageUrl.value = basicInfo.value[fieldName] || 'https://via.placeholder.com/500x300?text=暂无图片';
    imageModalVisible.value = true;
  } catch (error) {
    smartSentry.captureError(error);
    message.error('获取图片失败');
  }
};

// 各种查看方法
const viewLicense = () => {
  showImage('营业执照', 'businessLicensePic');
};

const viewQualifications = () => {
  showImage('资质照片', 'qualificationPic');
};

const viewIdCardFront = () => {
  showImage('身份证人像面', 'corporateIdcardFrontPic');
};

const viewIdCardBack = () => {
  showImage('身份证国徽面', 'corporateIdcardBackPic');
};

const viewStorefront = () => {
  showImage('门头照', 'doorPhoto');
};

const viewInterior = () => {
  showImage('内景照', 'interiorPhone');
};

const viewStoreLogo = () => {
  showImage('门店Logo', 'storeLogo');
};

const editInfo = () => {
  router.push('/merchant-settings/basic-info-edit');
};

const editMiniProgramInfo = () => {
  // 打开小程序信息编辑弹窗
  miniProgramModalVisible.value = true;
  // 初始化表单数据
  miniProgramForm.value = {
    appId: basicInfo.value.appId || '',
  };
};

const saveMiniProgramInfo = async () => {
  try {
    // 表单验证
    await miniProgramFormRef.value?.validate();
    
    miniProgramLoading.value = true;
    
    const currentStoreId = getCurrentStoreId();
    if (!currentStoreId) {
      message.error('获取门店ID失败，无法保存小程序信息');
      return;
    }

    const params = {
      storeId: currentStoreId,
      appId: miniProgramForm.value.appId,
    };

    // 使用新的 /store/appId 接口
    await basicInfoApi.updateStoreAppId(params);
    
    message.success('小程序信息保存成功');
    
    // 更新本地数据
    basicInfo.value.appId = miniProgramForm.value.appId;
    
    // 关闭弹窗
    miniProgramModalVisible.value = false;
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误，不处理
      return;
    }
    
    smartSentry.captureError(error);
    if (error.response) {
      message.error(error.response.data?.msg || '保存小程序信息失败');
    } else {
      message.error(error.message || '网络请求失败');
    }
  } finally {
    miniProgramLoading.value = false;
  }
};

const cancelMiniProgramEdit = () => {
  miniProgramModalVisible.value = false;
  // 清理表单验证状态
  miniProgramFormRef.value?.resetFields();
};

// 辅助函数
const getAccTypeDesc = (type) => {
  const accTypeMap = {
    1: '个人',
    2: '企业'
  };
  return accTypeMap[type] || type || '未设置';
};

const getMerchantTypeDesc = (type) => {
  return MERCHANT_TYPE_ENUM[type]?.desc || type;
};

const getBusinessCategoryDesc = (category) => {
  const businessCategoryMap = {
    '9902': '数字娱乐',
    '9910': '互联网金融',
    '9911': '保险',
    '9923': '航旅',
    '9926': '消费金融',
    '9927': '基金',
    '9928': '大宗商品现货交易',
    '9929': '收藏品交易',
    '9930': '电商B2B',
    '9931': '零售B2C',
    '9937': '代理商',
    '9938': '物流',
    '9940': '金融租赁',
    '9941': '票据',
    '9943': '教育培训',
    '9946': '公共服务及便民服务',
    '9980': '跨境服务贸易',
    '9981': '跨境货物贸易',
    '9982': '货币代兑',
    '9983': '金融机构',
    '9990': '征信',
    '10012': '有偿资讯',
    '9999': '其他'
  };
  return businessCategoryMap[category] || category || '未设置';
};

const getQualificationDocumentDesc = (document) => {
  const qualificationDocumentMap = {
    1: '《事业单位法人证书》',
    2: '《办学许可证》',
    3: '《民办非企业单位登记证书》',
    4: '《社会服务机构登记证书》',
    5: '营业执照'
  };
  return qualificationDocumentMap[document] || document || '未设置';
};

const getStoreAddressDesc = () => {
  // 直接使用API返回的地址名称
  const { storeProvince, storeCity, storeCounty } = basicInfo.value;
  if (storeProvince && storeCity && storeCounty) {
    return `${storeProvince}${storeCity}${storeCounty}`;
  }
  return '';
};

const formatRegisterCapital = (capital) => {
  if (!capital || capital === 0) {
    return '未设置';
  }
  
  // 如果数值大于等于10000，转换为万元
  if (capital >= 10000) {
    const capitalInWan = (capital / 10000).toFixed(2);
    // 去掉末尾的0和小数点
    const formatted = parseFloat(capitalInWan).toString();
    return `${formatted}万元`;
  }
  
  // 如果数值小于10000，直接显示元
  return `${capital}元`;
};
</script>

<style scoped>
.basic-info-container {
  
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.info-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  min-height: 32px;
}

.info-label {
  display: inline-block;
  color: #666;
  font-size: 14px;
  margin-right: 16px;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  font-size: 14px;
  flex: 1;
  word-break: break-all;
}

.link-button {
  padding: 0;
  height: auto;
  font-size: 14px;
}

.image-preview {
  text-align: center;
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .info-grid {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-label {
    margin-right: 12px;
  }
}
</style> 