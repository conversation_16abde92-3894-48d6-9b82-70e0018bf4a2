<template>
  <div class="basic-info-edit-container">
    <a-card title="编辑基本信息" :bordered="false">
      <a-form
        ref="formRef"
        :model="formData"
        :rules="rules"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 16 }"
        layout="horizontal"
      >
        <!-- 主体信息填写号 -->
        <div class="form-section">
          <h3 class="section-title">主体信息</h3>
          
          <!-- 主体类型 -->
          <a-form-item label="主体类型" name="accType">
            <a-select
              v-model:value="formData.accType"
              placeholder="请选择主体类型"
              style="width: 100%"
            >
              <a-select-option :value="1">个人</a-select-option>
              <a-select-option :value="2">企业</a-select-option>
            </a-select>
          </a-form-item>
          
          <!-- 营业执照 -->
          <a-form-item label="营业执照" name="businessLicensePic">
            <div class="custom-upload-container">
              <div v-if="businessLicenseImage" class="uploaded-image-item">
                <a-image 
                  :src="businessLicenseImage.url" 
                  alt="营业执照" 
                  :preview="{ mask: false }"
                />
                <div class="image-remove" @click="removeBusinessLicense">
                  <close-outlined />
                </div>
              </div>
              <div v-else class="upload-btn" @click="triggerBusinessLicenseInput">
                <div class="upload-icon">
                  <plus-outlined />
                </div>
                <div class="upload-text">上传营业执照</div>
              </div>
              <input
                ref="businessLicenseInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleBusinessLicenseChange"
              />
            </div>
            <div class="upload-tip">仅支持.jpg.png格式，图片需清晰完整</div>
          </a-form-item>

          <!-- 营业执照名称 -->
          <a-form-item label="营业执照名称" name="businessLicenseName">
            <a-input
              v-model:value="formData.businessLicenseName"
              placeholder="请输入营业执照名称"
              :maxlength="100"
            />
          </a-form-item>

          <!-- 统一社会信用代码 -->
          <a-form-item label="统一社会信用代码" name="socialCreditCode">
            <a-input
              v-model:value="formData.socialCreditCode"
              placeholder="请输入统一社会信用代码"
              :maxlength="18"
            />
          </a-form-item>

          <!-- 注册资本 -->
          <a-form-item label="注册资本" name="registerCapital">
            <a-input-number
              v-model:value="formData.registerCapital"
              placeholder="请输入注册资本"
              :min="0"
              :precision="0"
              style="width: 100%"
            />
            <div class="form-tip">
              请填写营业执照上的注册资本金额（万元）
            </div>
          </a-form-item>

          <!-- 成立日期 -->
          <a-form-item label="成立日期" name="establishmentDate">
            <a-date-picker
              v-model:value="formData.establishmentDate"
              placeholder="请选择成立日期"
              style="width: 100%"
              format="YYYY-MM-DD"
              :disabled-date="disabledEstablishmentDate"
            />
            <div class="form-tip">
              请选择企业的成立日期
            </div>
          </a-form-item>

          <!-- 门店名称 -->
          <a-form-item label="门店名称" name="storeName">
            <a-input
              v-model:value="formData.storeName"
              placeholder="请输入门店名称"
              :maxlength="50"
            />
            <div class="form-tip">
              支付宝账单中的商户名称，支付结果均会展示此处设置的简称，最多输入8个汉字
            </div>
          </a-form-item>

          <!-- 门店Logo -->
          <a-form-item label="门店Logo" name="storeLogo">
            <div class="custom-upload-container">
              <div v-if="storeLogoImage" class="uploaded-image-item">
                <a-image 
                  :src="storeLogoImage.url" 
                  alt="门店Logo" 
                  :preview="{ mask: false }"
                />
                <div class="image-remove" @click="removeStoreLogo">
                  <close-outlined />
                </div>
              </div>
              <div v-else class="upload-btn" @click="triggerStoreLogoInput">
                <div class="upload-icon">
                  <plus-outlined />
                </div>
                <div class="upload-text">上传门店Logo</div>
              </div>
              <input
                ref="storeLogoInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleStoreLogoChange"
              />
            </div>
            <div class="upload-tip">可选，建议尺寸1:1，仅支持.jpg.png格式</div>
          </a-form-item>

          <!-- 经营类目 -->
          <a-form-item label="经营类目" name="businessCategory">
            <a-select
              v-model:value="formData.businessCategory"
              placeholder="请选择经营类目"
              style="width: 100%"
            >
              <a-select-option value="9902">数字娱乐</a-select-option>
              <a-select-option value="9910">互联网金融</a-select-option>
              <a-select-option value="9911">保险</a-select-option>
              <a-select-option value="9923">航旅</a-select-option>
              <a-select-option value="9926">消费金融</a-select-option>
              <a-select-option value="9927">基金</a-select-option>
              <a-select-option value="9928">大宗商品现货交易</a-select-option>
              <a-select-option value="9929">收藏品交易</a-select-option>
              <a-select-option value="9930">电商B2B</a-select-option>
              <a-select-option value="9931">零售B2C</a-select-option>
              <a-select-option value="9937">代理商</a-select-option>
              <a-select-option value="9938">物流</a-select-option>
              <a-select-option value="9940">金融租赁</a-select-option>
              <a-select-option value="9941">票据</a-select-option>
              <a-select-option value="9943">教育培训</a-select-option>
              <a-select-option value="9946">公共服务及便民服务</a-select-option>
              <a-select-option value="9980">跨境服务贸易</a-select-option>
              <a-select-option value="9981">跨境货物贸易</a-select-option>
              <a-select-option value="9982">货币代兑</a-select-option>
              <a-select-option value="9983">金融机构</a-select-option>
              <a-select-option value="9990">征信</a-select-option>
              <a-select-option value="10012">有偿资讯</a-select-option>
              <a-select-option value="9999">其他</a-select-option>
            </a-select>
          </a-form-item>

          <!-- 资质文件 -->
          <a-form-item label="资质文件" name="qualificationDocument">
            <a-select
              v-model:value="formData.qualificationDocument"
              placeholder="请选择资质文件"
              style="width: 100%"
            >
              <a-select-option :value="1">《事业单位法人证书》</a-select-option>
              <a-select-option :value="2">《办学许可证》</a-select-option>
              <a-select-option :value="3">《民办非企业单位登记证书》</a-select-option>
              <a-select-option :value="4">《社会服务机构登记证书》</a-select-option>
              <a-select-option :value="5">营业执照</a-select-option>
            </a-select>
            <div class="form-tip">
              请根据您的实际证件类型选择对应的资质文件类型
            </div>
          </a-form-item>

          <!-- 资质照片(附件) -->
          <a-form-item label="资质文件照片（原件）" name="qualificationPic">
            <div class="custom-upload-container">
              <div v-if="qualificationPicImage" class="uploaded-image-item">
                <a-image 
                  :src="qualificationPicImage.url" 
                  alt="资质照片" 
                  :preview="{ mask: false }"
                />
                <div class="image-remove" @click="removeQualificationPic">
                  <close-outlined />
                </div>
              </div>
              <div v-else class="upload-btn" @click="triggerQualificationPicInput">
                <div class="upload-icon">
                  <plus-outlined />
                </div>
                <div class="upload-text">上传资质文件照片</div>
              </div>
              <input
                ref="qualificationPicInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleQualificationPicChange"
              />
            </div>
            <div class="upload-tip">仅支持.jpg.png格式，图片需清晰完整</div>
          </a-form-item>
        </div>

        <a-divider />

        <!-- 法人信息 -->
        <div class="form-section">
          <h3 class="section-title">法人信息</h3>
          
          <!-- 法人身份证人像面 -->
          <a-form-item label="法人身份证人像面" name="corporateIdcardFrontPic">
            <div class="custom-upload-container">
              <div v-if="idCardFrontImage" class="uploaded-image-item">
                <a-image 
                  :src="idCardFrontImage.url" 
                  alt="身份证人像面" 
                  :preview="{ mask: false }"
                />
                <div class="image-remove" @click="removeIdCardFront">
                  <close-outlined />
                </div>
              </div>
              <div v-else class="upload-btn" @click="triggerIdCardFrontInput">
                <div class="upload-icon">
                  <plus-outlined />
                </div>
                <div class="upload-text">身份证人像面</div>
              </div>
              <input
                ref="idCardFrontInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleIdCardFrontChange"
              />
            </div>
            <div class="upload-tip">仅支持.jpg.png格式，图片需清晰完整</div>
          </a-form-item>

          <!-- 法人身份证国徽面 -->
          <a-form-item label="法人身份证国徽面" name="corporateIdcardBackPic">
            <div class="custom-upload-container">
              <div v-if="idCardBackImage" class="uploaded-image-item">
                <a-image 
                  :src="idCardBackImage.url" 
                  alt="身份证国徽面" 
                  :preview="{ mask: false }"
                />
                <div class="image-remove" @click="removeIdCardBack">
                  <close-outlined />
                </div>
              </div>
              <div v-else class="upload-btn" @click="triggerIdCardBackInput">
                <div class="upload-icon">
                  <plus-outlined />
                </div>
                <div class="upload-text">身份证国徽面</div>
              </div>
              <input
                ref="idCardBackInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleIdCardBackChange"
              />
            </div>
            <div class="upload-tip">仅支持.jpg.png格式，图片需清晰完整</div>
          </a-form-item>

          <!-- 法人姓名 -->
          <a-form-item label="法人姓名" name="corporateName">
            <a-input
              v-model:value="formData.corporateName"
              placeholder="请输入法人姓名"
              :maxlength="20"
            />
          </a-form-item>

          <!-- 法人身份证号 -->
          <a-form-item label="法人身份证号" name="corporateCertId">
            <a-input
              v-model:value="formData.corporateCertId"
              placeholder="请输入法人身份证号"
              :maxlength="18"
            />
          </a-form-item>

          <!-- 法人证件有效期 -->
          <a-form-item label="法人证件有效期" name="corporateIdcardValidityPeriod">
            <a-date-picker
              v-model:value="formData.corporateIdcardValidityPeriod"
              placeholder="请选择法人证件有效期"
              style="width: 100%"
              format="YYYY-MM-DD"
              :disabled-date="disabledDate"
            />
          </a-form-item>

          <!-- 法人手机号 -->
          <a-form-item label="法人手机号" name="corporateMobile">
            <a-input
              v-model:value="formData.corporateMobile"
              placeholder="请输入法人手机号"
              :maxlength="11"
            />
          </a-form-item>

          <!-- 验证码 -->
          <a-form-item label="短信验证码" name="smsCode">
            <a-input-group compact>
              <a-input 
                style="width: calc(100% - 120px)" 
                v-model:value="formData.smsCode" 
                placeholder="请输入验证码" 
                :maxlength="6" 
              />
              <a-button 
                @click="handleGetVerificationCode" 
                class="verification-code-btn" 
                type="primary" 
                :disabled="getCodeDisabled" 
                :loading="getCodeLoading"
                style="width: 120px"
              >
                {{ getCodeText }}
              </a-button>
            </a-input-group>
          </a-form-item>
        </div>

        <a-divider />

        <!-- 经营信息填写号 -->
        <div class="form-section">
          <h3 class="section-title">经营信息</h3>
          
          <!-- 门头照 -->
          <a-form-item label="门头照" name="doorPhoto">
            <div class="custom-upload-container">
              <div v-if="storefrontImage" class="uploaded-image-item">
                <a-image 
                  :src="storefrontImage.url" 
                  alt="门头照" 
                  :preview="{ mask: false }"
                />
                <div class="image-remove" @click="removeStorefront">
                  <close-outlined />
                </div>
              </div>
              <div v-else class="upload-btn" @click="triggerStorefrontInput">
                <div class="upload-icon">
                  <plus-outlined />
                </div>
                <div class="upload-text">上传门头照</div>
              </div>
              <input
                ref="storefrontInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleStorefrontChange"
              />
            </div>
            <div class="upload-tip">仅支持.jpg.png格式，图片需清晰完整</div>
          </a-form-item>

          <!-- 内景照 -->
          <a-form-item label="内景照" name="interiorPhone">
            <div class="custom-upload-container">
              <div v-if="interiorImage" class="uploaded-image-item">
                <a-image 
                  :src="interiorImage.url" 
                  alt="内景照" 
                  :preview="{ mask: false }"
                />
                <div class="image-remove" @click="removeInterior">
                  <close-outlined />
                </div>
              </div>
              <div v-else class="upload-btn" @click="triggerInteriorInput">
                <div class="upload-icon">
                  <plus-outlined />
                </div>
                <div class="upload-text">上传内景照</div>
              </div>
              <input
                ref="interiorInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleInteriorChange"
              />
            </div>
            <div class="upload-tip">仅支持.jpg.png格式，图片需清晰完整</div>
          </a-form-item>

          <!-- 门店地址 -->
          <a-form-item label="门店地址" name="cascaderAddress">
            <a-cascader
              v-model:value="formData.cascaderAddress"
              :options="addressOptions"
              placeholder="请选择省/市/区"
              style="width: 100%"
              expand-trigger="hover"
              :show-search="true"
              :field-names="{ label: 'label', value: 'value', children: 'children' }"
              @change="handleAddressChange"
            />
          </a-form-item>

          <!-- 详细地址 -->
          <a-form-item label="详细地址" name="storeAddress">
            <a-input
              v-model:value="formData.storeAddress"
              placeholder="请输入详细地址"
              :maxlength="100"
            />
          </a-form-item>

          <!-- 联系电话 -->
          <a-form-item label="联系电话" name="contactPhone">
            <a-input
              v-model:value="formData.contactPhone"
              placeholder="请输入联系电话"
              :maxlength="20"
            />
          </a-form-item>
        </div>

        <a-divider />

        <!-- 管理员信息 -->
        <div class="form-section">
          <h3 class="section-title">管理员信息</h3>
          
          <!-- 管理员姓名 -->
          <a-form-item label="管理员姓名" name="adminName">
            <a-input
              v-model:value="formData.adminName"
              placeholder="请输入管理员姓名"
              :maxlength="20"
            />
          </a-form-item>

          <!-- 管理员手机号 -->
          <a-form-item label="管理员手机号" name="adminPhone">
            <a-input
              v-model:value="formData.adminPhone"
              placeholder="请输入管理员手机号"
              :maxlength="11"
            />
            <div class="form-tip">将作为主账号登录名</div>
          </a-form-item>

          <!-- 管理员邮箱 -->
          <a-form-item label="管理员邮箱" name="adminEmail">
            <a-input
              v-model:value="formData.adminEmail"
              placeholder="请输入管理员邮箱"
              :maxlength="50"
            />
          </a-form-item>

          <!-- 登录密码 -->
          <a-form-item label="登录密码" name="loginPassword">
            <a-input-password
              v-model:value="formData.loginPassword"
              placeholder="请输入登录密码"
              :maxlength="20"
            />
          </a-form-item>

          <!-- 重复登录密码 -->
          <a-form-item label="重复登录密码" name="confirmPassword">
            <a-input-password
              v-model:value="formData.confirmPassword"
              placeholder="请再次输入登录密码"
              :maxlength="20"
            />
          </a-form-item>
        </div>

        <!-- 操作按钮 -->
        <a-form-item :wrapper-col="{ span: 16, offset: 4 }" style="margin-top: 32px;">
          <a-space>
            <a-button type="primary" @click="handleSubmit" :loading="loading">
              保存
            </a-button>
            <a-button @click="handleCancel">
              取消
            </a-button>
          </a-space>
        </a-form-item>
      </a-form>
    </a-card>


  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { PlusOutlined, CloseOutlined } from '@ant-design/icons-vue';
import { basicInfoApi } from '/@/api/business/merchant-settings/basic-info-api';
import { fileApi } from '/@/api/support/file-api';
import { smartSentry } from '/@/lib/smart-sentry';
import { SmartLoading } from '/@/components/framework/smart-loading';
import addressOptions from '/@/utils/cascader-address-options';
import { useUserStore } from '/@/store/modules/system/user';
import { regular } from '/@/constants/regular-const';
import dayjs from 'dayjs';

const route = useRoute();
const router = useRouter();

// 获取用户store
const userStore = useUserStore();

// ----------------------- 数据定义 ---------------------------
const loading = ref(false);
const formRef = ref();

// 验证码相关状态
const getCodeLoading = ref(false);
const getCodeDisabled = ref(false);
const getCodeText = ref('获取验证码');
const countdown = ref(0);



// 图片数据
const businessLicenseImage = ref(null);
const qualificationPicImage = ref(null);
const idCardFrontImage = ref(null);
const idCardBackImage = ref(null);
const storefrontImage = ref(null);
const interiorImage = ref(null);
const storeLogoImage = ref(null);

// 文件输入框引用
const businessLicenseInput = ref(null);
const qualificationPicInput = ref(null);
const idCardFrontInput = ref(null);
const idCardBackInput = ref(null);
const storefrontInput = ref(null);
const interiorInput = ref(null);
const storeLogoInput = ref(null);

// 省市区级联数据 - 使用真实的中国行政区划数据
// addressOptions 已从 utils 文件导入

const formData = reactive({
  // 门店信息
  storeId: 0, // integer<int64> - 门店ID
  storeName: '', // string - 门店名称(商户简称)
  storeProvinceNo: '', // string - 省编码
  storeProvince: '', // string - 省名称
  storeCityNo: '', // string - 市编码
  storeCity: '', // string - 市名称
  storeCountyNo: '', // string - 县区编码
  storeCounty: '', // string - 县区名称
  storeAddress: '', // string - 详细地址
  storeLogo: '', // string - 门店logo (可选)
  doorPhoto: '', // string - 门头照
  interiorPhone: '', // string - 内景照
  
  // 主体信息
  accType: 0, // integer - 主体类型:1-个人,2-企业
  registerCapital: 0, // integer - 注册资本(万元)
  businessLicensePic: '', // string - 营业执照
  businessLicenseName: '', // string - 营业执照名称
  socialCreditCode: '', // string - 统一社会信用代码
  businessCategory: '', // string - 经营类目
  qualificationDocument: 0, // integer - 资质文件
  qualificationPic: '', // string - 资质文件照片（原件）
  establishmentDate: null, // string - 成立日期
  
  // 法人信息
  corporateIdcardFrontPic: '', // string - 法人身份证人像面
  corporateIdcardBackPic: '', // string - 法人身份证国徽面
  corporateName: '', // string - 法人姓名
  corporateCertId: '', // string - 法人身份证号
  corporateMobile: '', // string - 法人手机号
  smsUuid: '', // string - 短信验证码uuid (可选)
  smsCode: '', // string - 短信验证码 (可选)
  apiSmsCode: '', // string - API返回的短信验证码（临时解决方案）
  corporateIdcardValidityPeriod: null, // string - 法人证件有效期
  
  // 管理员信息
  adminName: '', // string - 管理员姓名
  adminPhone: '', // string - 管理员手机号
  adminEmail: '', // string - 管理员邮箱
  loginPassword: '', // string - 登录密码
  contactPhone: '', // string - 联系电话
  
  // 前端辅助字段
  confirmPassword: '', // string - 确认密码
  cascaderAddress: [] // array - 级联选择器绑定字段
});

// 表单验证规则
const rules = {
  // 门店信息
  storeName: [
    { required: true, message: '请输入商户简称', trigger: 'blur' }
  ],
  cascaderAddress: [
    { required: true, message: '请选择门店地址', trigger: 'change' }
  ],
  storeAddress: [
    { required: true, message: '请输入详细地址', trigger: 'blur' }
  ],
  
  // 主体信息
  accType: [
    { required: true, message: '请选择主体类型', trigger: 'change' }
  ],
  businessLicenseName: [
    { required: true, message: '请输入营业执照名称', trigger: 'blur' }
  ],
  socialCreditCode: [
    { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
  ],
  registerCapital: [
    { required: true, message: '请输入注册资本', trigger: 'blur' },
    { 
      validator: (rule, value) => {
        if (value && (value < 1 || value > 999999999)) {
          return Promise.reject('注册资本必须在1到999999999之间');
        }
        return Promise.resolve();
      }, 
      trigger: 'blur' 
    }
  ],
  establishmentDate: [
    { required: true, message: '请选择成立日期', trigger: 'change' }
  ],
  businessCategory: [
    { required: true, message: '请选择经营类目', trigger: 'change' }
  ],
  qualificationDocument: [
    { required: true, message: '请选择资质文件', trigger: 'change' }
  ],
  
  // 法人信息
  corporateName: [
    { required: true, message: '请输入法人姓名', trigger: 'blur' }
  ],
  corporateCertId: [
    { required: true, message: '请输入法人身份证号', trigger: 'blur' },
    { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '身份证号码格式不正确', trigger: 'blur' }
  ],
  corporateMobile: [
    { required: true, message: '请输入法人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  smsCode: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { pattern: /^\d{6}$/, message: '验证码为6位数字', trigger: 'blur' }
  ],
  corporateIdcardValidityPeriod: [
    { required: true, message: '请选择法人证件有效期', trigger: 'change' }
  ],
  
  // 管理员信息
  adminName: [
    { required: true, message: '请输入管理员姓名', trigger: 'blur' }
  ],
  adminPhone: [
    { required: true, message: '请输入管理员手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '手机号格式不正确', trigger: 'blur' }
  ],
  adminEmail: [
    { required: true, message: '请输入管理员邮箱', trigger: 'blur' },
    { pattern: regular.email, message: '邮箱格式不正确', trigger: 'blur' }
  ],
  loginPassword: [
    { min: 6, max: 20, message: '密码长度在6到20个字符', trigger: 'blur' },
    { pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{6,}$/, message: '密码必须包含大小写字母和数字', trigger: 'blur' }
  ],
  contactPhone: [
    { required: true, message: '请输入联系电话', trigger: 'blur' },
    { pattern: /^(\d{3,4}-?)?\d{7,8}$|^1[3-9]\d{9}$/, message: '请输入正确的联系电话格式', trigger: 'blur' }
  ],
  confirmPassword: [
    {
      validator: (rule, value) => {
        if (value && value !== formData.loginPassword) {
          return Promise.reject('两次输入的密码不一致');
        }
        return Promise.resolve();
      },
      trigger: 'blur'
    }
  ]
};

// ----------------------- 生命周期 ---------------------------
onMounted(() => {
  loadBasicInfo();
});

// ----------------------- 方法 ---------------------------

/**
 * 获取当前门店ID
 */
function getCurrentStoreId() {
  const currentStore = userStore.getCurrentStore();
  if (currentStore && currentStore.id) {
    return currentStore.id;
  }
  message.warning('获取门店信息失败，请重新登录');
  return null;
}

/**
 * 加载基本信息
 */
async function loadBasicInfo() {
  try {
    loading.value = true;
    
    // 获取当前门店ID
    const currentStoreId = getCurrentStoreId();
    if (!currentStoreId) {
      return;
    }
    
    console.log('加载基本信息编辑的门店ID:', currentStoreId);
    
    // 调用API获取门店基本信息
    const result = await basicInfoApi.getStoreBasicInfo(currentStoreId);
    console.log('基本信息编辑API响应:', result);
    
    if (result.data) {
      // 将API返回的数据映射到表单数据
      Object.assign(formData, result.data);
      
      // 设置级联地址选择器的值
      if (result.data.storeProvinceNo && result.data.storeCityNo && result.data.storeCountyNo) {
        formData.cascaderAddress = [
          result.data.storeProvinceNo,
          result.data.storeCityNo,
          result.data.storeCountyNo
        ];
      }
      
      // 设置图片预览数据
      if (result.data.businessLicensePic) {
        businessLicenseImage.value = { url: result.data.businessLicensePic };
      }
      if (result.data.storeLogo) {
        storeLogoImage.value = { url: result.data.storeLogo };
      }
      if (result.data.qualificationPic) {
        qualificationPicImage.value = { url: result.data.qualificationPic };
      }
      if (result.data.corporateIdcardFrontPic) {
        idCardFrontImage.value = { url: result.data.corporateIdcardFrontPic };
      }
      if (result.data.corporateIdcardBackPic) {
        idCardBackImage.value = { url: result.data.corporateIdcardBackPic };
      }
      if (result.data.doorPhoto) {
        storefrontImage.value = { url: result.data.doorPhoto };
      }
      if (result.data.interiorPhone) {
        interiorImage.value = { url: result.data.interiorPhone };
      }
      // 处理日期字段 - 如果有值则转换为dayjs对象，否则保持null
      if (result.data.corporateIdcardValidityPeriod) {
        formData.corporateIdcardValidityPeriod = dayjs(result.data.corporateIdcardValidityPeriod);
      }
      if (result.data.establishmentDate) {
        formData.establishmentDate = dayjs(result.data.establishmentDate);
      }
      
      // 清除表单验证状态，避免自动填充数据时的验证误报
      setTimeout(() => {
        formRef.value?.clearValidate();
      }, 100);
    }
  } catch (error) {
    console.error('加载基本信息失败:', error);
    smartSentry.captureError(error);
    if (error.response) {
      message.error(error.response.data?.msg || '加载基本信息失败');
    } else {
      message.error(error.message || '网络请求失败');
    }
  } finally {
    loading.value = false;
  }
}

// ----------------------- 图片上传方法 ---------------------------

/**
 * 通用文件选择处理
 */
function handleFileSelect(event) {
  const files = event.target.files;
  if (!files || files.length === 0) return null;
  
  const file = files[0];
  
  // 验证文件类型
  if (!file.type.startsWith('image/')) {
    message.error('请选择图片文件');
    return null;
  }
  
  // 验证文件大小（限制为5MB）
  const maxSize = 5 * 1024 * 1024;
  if (file.size > maxSize) {
    message.error('文件大小不能超过5MB');
    return null;
  }
  
  // 清空input值，允许重复选择同一文件
  event.target.value = '';
  
  return file;
}

/**
 * 创建图片数据对象
 */
function createImageData(file, callback) {
  const reader = new FileReader();
  reader.onload = (e) => {
    const imageData = {
      url: e.target.result,
      name: file.name,
      size: file.size,
      file: file
    };
    callback(imageData);
  };
  reader.readAsDataURL(file);
}

/**
 * 文件上传辅助函数
 */
async function uploadFile(file, folder = 1) {
  if (!file) {
    return '';
  }
  
  const formData = new FormData();
  formData.append('file', file);
  
  try {
    const response = await fileApi.uploadFile(formData, folder);
    return response.data.fileUrl || '';
  } catch (error) {
    console.error('文件上传失败:', error);
    throw new Error('文件上传失败');
  }
}

// 营业执照相关
function triggerBusinessLicenseInput() {
  businessLicenseInput.value?.click();
}

async function handleBusinessLicenseChange(event) {
  const file = handleFileSelect(event);
  if (file) {
    createImageData(file, async (imageData) => {
      businessLicenseImage.value = imageData;
      try {
        SmartLoading.show();
        const url = await uploadFile(file);
        formData.businessLicensePic = url;
        message.success('营业执照上传成功');
      } catch (error) {
        console.error('营业执照上传失败:', error);
        message.error('营业执照上传失败');
        businessLicenseImage.value = null;
        formData.businessLicensePic = '';
      } finally {
        SmartLoading.hide();
      }
    });
  }
}

function removeBusinessLicense() {
  businessLicenseImage.value = null;
  formData.businessLicensePic = '';
}

// 门店Logo相关
function triggerStoreLogoInput() {
  storeLogoInput.value?.click();
}

async function handleStoreLogoChange(event) {
  const file = handleFileSelect(event);
  if (file) {
    createImageData(file, async (imageData) => {
      storeLogoImage.value = imageData;
      try {
        SmartLoading.show();
        const url = await uploadFile(file);
        formData.storeLogo = url;
        message.success('门店Logo上传成功');
      } catch (error) {
        console.error('门店Logo上传失败:', error);
        message.error('门店Logo上传失败');
        storeLogoImage.value = null;
        formData.storeLogo = '';
      } finally {
        SmartLoading.hide();
      }
    });
  }
}

function removeStoreLogo() {
  storeLogoImage.value = null;
  formData.storeLogo = '';
}

// 资质文件照片相关
function triggerQualificationPicInput() {
  qualificationPicInput.value?.click();
}

async function handleQualificationPicChange(event) {
  const file = handleFileSelect(event);
  if (file) {
    createImageData(file, async (imageData) => {
      qualificationPicImage.value = imageData;
      try {
        SmartLoading.show();
        const url = await uploadFile(file);
        formData.qualificationPic = url;
        message.success('资质文件照片上传成功');
      } catch (error) {
        console.error('资质文件照片上传失败:', error);
        message.error('资质文件照片上传失败');
        qualificationPicImage.value = null;
        formData.qualificationPic = '';
      } finally {
        SmartLoading.hide();
      }
    });
  }
}

function removeQualificationPic() {
  qualificationPicImage.value = null;
  formData.qualificationPic = '';
}

// 身份证正面相关
function triggerIdCardFrontInput() {
  idCardFrontInput.value?.click();
}

async function handleIdCardFrontChange(event) {
  const file = handleFileSelect(event);
  if (file) {
    createImageData(file, async (imageData) => {
      idCardFrontImage.value = imageData;
      try {
        SmartLoading.show();
        const url = await uploadFile(file);
        formData.corporateIdcardFrontPic = url;
        message.success('身份证人像面上传成功');
      } catch (error) {
        console.error('身份证人像面上传失败:', error);
        message.error('身份证人像面上传失败');
        idCardFrontImage.value = null;
        formData.corporateIdcardFrontPic = '';
      } finally {
        SmartLoading.hide();
      }
    });
  }
}

function removeIdCardFront() {
  idCardFrontImage.value = null;
  formData.corporateIdcardFrontPic = '';
}

// 身份证反面相关
function triggerIdCardBackInput() {
  idCardBackInput.value?.click();
}

async function handleIdCardBackChange(event) {
  const file = handleFileSelect(event);
  if (file) {
    createImageData(file, async (imageData) => {
      idCardBackImage.value = imageData;
      try {
        SmartLoading.show();
        const url = await uploadFile(file);
        formData.corporateIdcardBackPic = url;
        message.success('身份证国徽面上传成功');
      } catch (error) {
        console.error('身份证国徽面上传失败:', error);
        message.error('身份证国徽面上传失败');
        idCardBackImage.value = null;
        formData.corporateIdcardBackPic = '';
      } finally {
        SmartLoading.hide();
      }
    });
  }
}

function removeIdCardBack() {
  idCardBackImage.value = null;
  formData.corporateIdcardBackPic = '';
}

// 门头照相关
function triggerStorefrontInput() {
  storefrontInput.value?.click();
}

async function handleStorefrontChange(event) {
  const file = handleFileSelect(event);
  if (file) {
    createImageData(file, async (imageData) => {
      storefrontImage.value = imageData;
      try {
        SmartLoading.show();
        const url = await uploadFile(file);
        formData.doorPhoto = url;
        message.success('门头照上传成功');
      } catch (error) {
        console.error('门头照上传失败:', error);
        message.error('门头照上传失败');
        storefrontImage.value = null;
        formData.doorPhoto = '';
      } finally {
        SmartLoading.hide();
      }
    });
  }
}

function removeStorefront() {
  storefrontImage.value = null;
  formData.doorPhoto = '';
}

// 内景照相关
function triggerInteriorInput() {
  interiorInput.value?.click();
}

async function handleInteriorChange(event) {
  const file = handleFileSelect(event);
  if (file) {
    createImageData(file, async (imageData) => {
      interiorImage.value = imageData;
      try {
        SmartLoading.show();
        const url = await uploadFile(file);
        formData.interiorPhone = url;
        message.success('内景照上传成功');
      } catch (error) {
        console.error('内景照上传失败:', error);
        message.error('内景照上传失败');
        interiorImage.value = null;
        formData.interiorPhone = '';
      } finally {
        SmartLoading.hide();
      }
    });
  }
}

function removeInterior() {
  interiorImage.value = null;
  formData.interiorPhone = '';
}

// ----------------------- 其他方法 ---------------------------

/**
 * 地址级联选择器变化处理
 */
function handleAddressChange(value, selectedOptions) {
  if (selectedOptions && selectedOptions.length >= 3) {
    formData.storeProvinceNo = selectedOptions[0].value;
    formData.storeProvince = selectedOptions[0].label;
    formData.storeCityNo = selectedOptions[1].value;
    formData.storeCity = selectedOptions[1].label;
    formData.storeCountyNo = selectedOptions[2].value;
    formData.storeCounty = selectedOptions[2].label;
  }
}

/**
 * 提交表单
 */
async function handleSubmit() {
  try {
    await formRef.value.validate();
    
    loading.value = true;

    // 获取当前门店ID
    const currentStoreId = getCurrentStoreId();
    if (!currentStoreId) {
      return;
    }

    // 验证关键字段是否有值
    if (!formData.storeName || formData.storeName.trim() === '') {
      message.error('请填写门店名称');
      return;
    }
    
    if (!formData.storeAddress || formData.storeAddress.trim() === '') {
      message.error('请填写详细地址');
      return;
    }

    // 构建提交数据，完全按照API文档的字段格式
    const submitData = {
      // === 必需字段 ===
      storeId: currentStoreId, // integer<int64> - 门店id (必需)
      storeName: formData.storeName.trim(), // string - 门店名称(商户简称) (必需)
      storeProvinceNo: formData.storeProvinceNo, // string - 省编码 (必需)
      storeProvince: formData.storeProvince, // string - 省名称 (必需)
      storeCityNo: formData.storeCityNo, // string - 市编码 (必需)
      storeCity: formData.storeCity, // string - 市名称 (必需)
      storeCountyNo: formData.storeCountyNo, // string - 县区编码 (必需)
      storeCounty: formData.storeCounty, // string - 县区名称 (必需)
      storeAddress: formData.storeAddress.trim(), // string - 详细地址 (必需)
      doorPhoto: formData.doorPhoto, // string - 门头照 (必需)
      interiorPhone: formData.interiorPhone, // string - 内景照 (必需)
      accType: formData.accType, // integer - 主体类型:1-个人,2-企业 (必需)
      registerCapital: formData.registerCapital, // integer - 注册资本(万元) (必需)
      establishmentDate: formData.establishmentDate ? formData.establishmentDate.format('YYYY-MM-DD') : '', // string - 成立日期 (必需)
      businessLicensePic: formData.businessLicensePic, // string - 营业执照 (必需)
      businessLicenseName: formData.businessLicenseName, // string - 营业执照名称 (必需)
      socialCreditCode: formData.socialCreditCode, // string - 统一社会信用代码 (必需)
      qualificationPic: formData.qualificationPic, // string - 资质文件照片（原件） (必需)
      corporateIdcardFrontPic: formData.corporateIdcardFrontPic, // string - 法人身份证人像面 (必需)
      corporateIdcardBackPic: formData.corporateIdcardBackPic, // string - 法人身份证国徽面 (必需)
      corporateName: formData.corporateName, // string - 法人姓名 (必需)
      corporateCertId: formData.corporateCertId, // string - 法人身份证号 (必需)
      corporateMobile: formData.corporateMobile, // string - 法人手机号 (必需)
      adminName: formData.adminName, // string - 管理员姓名 (必需)
      adminPhone: formData.adminPhone, // string - 管理员手机号 (必需)
      adminEmail: formData.adminEmail, // string - 管理员邮箱 (必需)
      loginPassword: formData.loginPassword, // string - 登录密码 (必需)
      contactPhone: formData.contactPhone, // string - 联系电话 (必需)
      corporateIdcardValidityPeriod: formData.corporateIdcardValidityPeriod ? formData.corporateIdcardValidityPeriod.format('YYYY-MM-DD') : '', // string - 法人证件有效期 (必需)
      
      // === 可选字段 ===
      storeLogo: formData.storeLogo || '', // string - 门店logo (可选)
      businessCategory: formData.businessCategory, // string - 经营类目 (可选)
      qualificationDocument: formData.qualificationDocument, // integer - 资质文件 (可选)
      smsUuid: formData.smsUuid || '', // string - 短信验证码uuid (可选)
      smsCode: formData.smsCode || '', // string - 短信验证码 (可选)
      repeatLoginPassword: formData.confirmPassword || '' // string - 重复登录密码 (可选)
    };

    console.log('表单数据:', {
      storeId: currentStoreId,
      storeName: formData.storeName,
      storeAddress: formData.storeAddress,
      storeProvinceNo: formData.storeProvinceNo,
      cascaderAddress: formData.cascaderAddress
    });
    console.log('提交数据:', submitData);
    
    // 调用更新API
    const result = await basicInfoApi.updateMerchantBasicInfo(submitData);
    console.log('更新基本信息API响应:', result);
    
    message.success('保存成功');
    
    // 返回上一页
    router.back();
  } catch (e) {
    if (e.errorFields) {
      return;
    }
    console.error('保存基本信息失败:', e);
    smartSentry.captureError(e);
    if (e.response) {
      message.error(e.response.data?.msg || '保存失败');
    } else {
      message.error(e.message || '网络请求失败');
    }
  } finally {
    loading.value = false;
  }
}

/**
 * 取消操作
 */
function handleCancel() {
  router.back();
}

/**
 * 获取验证码
 */
async function handleGetVerificationCode() {
  // 先验证手机号是否填写正确
  if (!formData.corporateMobile) {
    message.error('请先填写法人手机号');
    return;
  }
  
  if (!/^1[3-9]\d{9}$/.test(formData.corporateMobile)) {
    message.error('请填写正确的手机号格式');
    return;
  }

  try {
    getCodeLoading.value = true;
    
    // 调用发送验证码API
    const response = await basicInfoApi.sendSmsCode(formData.corporateMobile);
    
    if (response.data) {
      // 保存API返回的短信验证码信息
      formData.smsUuid = response.data.smsUuid || '';
      formData.apiSmsCode = response.data.smsCode || '';
      
      // 临时解决方案：自动填入短信验证码
      if (formData.apiSmsCode) {
        formData.smsCode = formData.apiSmsCode;
        message.success(`验证码发送成功，临时验证码：${formData.apiSmsCode}`);
      } else {
        message.success('验证码发送成功');
      }
      
      // 开始倒计时
      startCountdown();
    }
    
  } catch (error) {
    console.error('验证码发送失败:', error);
    smartSentry.captureError(error);
    message.error('发送验证码失败');
  } finally {
    getCodeLoading.value = false;
  }
}

/**
 * 开始倒计时
 */
function startCountdown() {
  getCodeDisabled.value = true;
  let countDown = 60;
  getCodeText.value = `重新获取(${countDown}s)`;
  const timer = setInterval(() => {
    if (countDown > 1) {
      countDown--;
      getCodeText.value = `重新获取(${countDown}s)`;
    } else {
      clearInterval(timer);
      getCodeDisabled.value = false;
      getCodeText.value = '获取验证码';
    }
  }, 1000);
}

/**
 * 禁用日期选择器
 */
function disabledDate(current) {
  // 禁用当前日期之前的日期
  return current && current < dayjs().startOf('day');
}

/**
 * 禁用成立日期选择器
 */
function disabledEstablishmentDate(current) {
  // 禁用当前日期之后的日期，成立日期不能是未来
  return current && current > dayjs().endOf('day');
}
</script>

<style scoped>
.basic-info-edit-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.form-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin-bottom: 16px;
  border-left: 4px solid #1890ff;
  padding-left: 12px;
}

.form-tip {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
  line-height: 1.4;
}

.upload-tip {
  font-size: 12px;
  color: #8c8c8c;
  margin-top: 4px;
}

.image-uploader .ant-upload {
  width: 104px;
  height: 104px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-placeholder {
  text-align: center;
  color: #8c8c8c;
}

:deep(.ant-form-item) {
  margin-bottom: 24px;
}

:deep(.ant-card-head-title) {
  padding: 16px 0;
}

:deep(.ant-divider) {
  margin: 32px 0;
}

.verification-code-btn {
  font-size: 13px;
  height: 32px;
  border-radius: 0 6px 6px 0;
}

.verification-code-btn:disabled {
  background-color: #f5f5f5;
  border-color: #d9d9d9;
  color: #00000040;
}

:deep(.ant-input-group) {
  display: flex;
  align-items: center;
}

:deep(.ant-input-group .ant-input) {
  border-radius: 6px 0 0 6px;
}

.custom-upload-container {
  display: inline-block;
}

.uploaded-image-item {
  position: relative;
  width: 104px;
  height: 104px;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  overflow: hidden;
  display: inline-block;
}

/* 确保Image组件填充满容器 */
.uploaded-image-item :deep(.ant-image) {
  width: 100% !important;
  height: 100% !important;
  display: block !important;
  line-height: 0 !important;
}

/* 确保img标签以centerCrop方式显示 */
.uploaded-image-item :deep(.ant-image img) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  object-position: center !important;
  transition: transform 0.3s ease;
  border-radius: 6px;
  display: block !important;
}

/* 悬停效果 */
.uploaded-image-item:hover :deep(.ant-image img) {
  transform: scale(1.05);
}

/* 重置可能的默认样式 */
.uploaded-image-item :deep(.ant-image .ant-image-img) {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
}



.image-remove {
  position: absolute;
  top: 4px;
  right: 4px;
  width: 16px;
  height: 16px;
  background: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 10px;
}

.image-remove:hover {
  background: rgba(0, 0, 0, 0.7);
}

.upload-btn {
  width: 104px;
  height: 104px;
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s;
  color: #999;
}

.upload-btn:hover {
  border-color: #1890ff;
  color: #1890ff;
}

.upload-icon {
  font-size: 20px;
  margin-bottom: 4px;
}

.upload-text {
  font-size: 12px;
}
</style>
 