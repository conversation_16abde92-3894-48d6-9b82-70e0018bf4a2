<template>
  <div class="financial-info-container">
    <a-card :bordered="false" :loading="loading">
      <!-- 对公账户信息 -->
      <div class="info-section">
        <div class="section-header">
          <h3 class="section-title">对公账户信息</h3>
          <a-button 
            type="primary" 
            @click="handleBindAccount"
          >
            编辑
          </a-button>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">账户名称:</label>
            <span class="info-value">{{ corporateAccount.accountName || '' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">开户银行:</label>
            <span class="info-value">{{ corporateAccount.bankName || '' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">账户号码:</label>
            <span class="info-value">{{ corporateAccount.cardNo || corporateAccount.accountNumber || '' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">开户行省市:</label>
            <span class="info-value">{{ getDepositBankLocation() || '' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">开户行支行:</label>
            <span class="info-value">{{ corporateAccount.depositBankName || corporateAccount.bankBranch || '' }}</span>
          </div>
        </div>
      </div>

      <a-divider />

      <!-- 宝付钱包 -->
      <div class="info-section">
        <div class="section-header">
          <h3 class="section-title">宝付钱包</h3>
          <div class="header-actions">
            <a-button 
              type="text" 
              @click="handleRefreshWallet"
              style="margin-right: 8px;"
            >
              刷新
            </a-button>
            <a-button 
              type="primary" 
              @click="handleWalletAction"
              v-if="paymentWallet.state !== 1 && paymentWallet.state !== 2"
            >
              开通
            </a-button>
          </div>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <label class="info-label">开通状态:</label>
            <span class="info-value">
              <a-tag :color="getBaofooStateColor(paymentWallet.state)">
                {{ getBaofooStateDesc(paymentWallet.state) }}
              </a-tag>
            </span>
          </div>
          <div class="info-item">
            <label class="info-label">余额:</label>
            <span class="info-value">{{ paymentWallet.balance !== null ? '¥ ' + formatAmount(paymentWallet.balance) : '' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">在途余额:</label>
            <span class="info-value">{{ paymentWallet.pendingBalance !== null ? '¥ ' + formatAmount(paymentWallet.pendingBalance) : '' }}</span>
          </div>
          <div class="info-item">
            <label class="info-label">总余额:</label>
            <span class="info-value">{{ paymentWallet.totalBalance !== null ? '¥ ' + formatAmount(paymentWallet.totalBalance) : '' }}</span>
          </div>
          <div class="info-item" v-if="paymentWallet.errorMsg && (paymentWallet.state === 0 || paymentWallet.state === -1)">
            <label class="info-label">错误说明:</label>
            <span class="info-value">{{ paymentWallet.errorMsg }}</span>
          </div>
        </div>
      </div>

      <a-divider v-if="paymentWallet.state === 1" />

      <!-- 商户报备和特约商户开通 -->
      <div class="info-section" v-if="paymentWallet.state === 1">
        <div class="dual-container">
          <!-- 商户报备 -->
          <div class="report-container">
            <div class="report-info">
              <h3 class="section-title">商户报备</h3>
              <p class="report-desc">报备成功后, 用户支付成功可显示"商户主体信息"</p>
            </div>
            <div class="report-actions">
              <!-- 未报备状态 -->
              <a-button 
                type="primary" 
                @click="handleMerchantReport"
                v-if="merchantReport.reportState === 'NONE'"
              >
                报备商户
              </a-button>
              <!-- 处理中状态 -->
              <a-button 
                type="primary" 
                loading
                v-if="merchantReport.reportState === 'PROCESSING'"
              >
                报备中
              </a-button>
              <!-- 报备失败状态 -->
              <div v-if="merchantReport.reportState === 'FAIL'" class="fail-actions">
                <a-button 
                  type="text" 
                  danger
                  @click="handleShowFailReason"
                  style="margin-right: 8px;"
                >
                  报备失败
                </a-button>
                <a-button 
                  type="primary"
                  @click="handleMerchantReport"
                >
                  重新提交
                </a-button>
              </div>
              <!-- 报备成功状态 -->
              <a-button 
                type="primary"
                ghost
                v-if="merchantReport.reportState === 'SUCCESS'"
              >
                已报备
              </a-button>
            </div>
          </div>

          <!-- 特约商户开通 -->
          <div class="special-merchant-container">
            <div class="report-info">
              <h3 class="section-title">特约商户开通</h3>
              <p class="report-desc">开通特约商户后, 代扣成功可显示"商户主体信息"</p>
            </div>
            <div class="report-actions">
              <!-- 未开通状态 -->
              <a-button 
                type="primary" 
                @click="handleSpecialMerchantApply"
                v-if="specialMerchant.status === 'NONE'"
              >
                申请开通
              </a-button>
              <!-- 申请中状态 -->
              <a-button 
                type="primary" 
                @click="handleSpecialMerchantApply"
                v-if="specialMerchant.status === 'PROCESSING'"
              >
                申请中
              </a-button>
              <!-- 申请失败状态 -->
              <div v-if="specialMerchant.status === 'FAIL'" class="fail-actions">
                <a-button 
                  type="text" 
                  danger
                  @click="handleShowSpecialMerchantFailReason"
                  style="margin-right: 8px;"
                >
                  申请失败
                </a-button>
                <a-button 
                  type="primary"
                  @click="handleSpecialMerchantApply"
                >
                  重新提交
                </a-button>
              </div>
              <!-- 开通成功状态 -->
              <a-button 
                type="primary"
                ghost
                v-if="specialMerchant.status === 'SUCCESS'"
              >
                已开通
              </a-button>
            </div>
          </div>
        </div>
      </div>
    </a-card>

    <!-- 绑定对公账户模态框 -->
    <a-modal
      v-model:open="bindAccountModalVisible"
      :title="isEditMode ? '编辑对公账户' : '绑定对公账户'"
      :confirm-loading="bindAccountLoading"
      @ok="handleBindAccountConfirm"
      @cancel="handleBindAccountCancel"
      width="600px"
    >
      <div style="padding: 20px 0;">
        <a-form
          ref="bindAccountFormRef"
          :model="bindAccountForm"
          :rules="bindAccountRules"
          layout="vertical"
        >
        <a-form-item label="对公账户名称" name="corporateAccount">
          <a-input v-model:value="bindAccountForm.corporateAccount" placeholder="请输入对公账户名称" />
        </a-form-item>
        <a-form-item label="开户银行" name="bankName">
          <a-select 
            v-model:value="bindAccountForm.bankName" 
            placeholder="请选择开户银行"
            show-search
            :filter-option="filterBankOption"
            @keydown="handleBankSelectKeydown"
          >
            <a-select-option v-for="bank in BANK_LIST" :key="bank.value" :value="bank.label">
              {{ bank.label }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="开户行省市" name="depositBankArea">
          <a-cascader
            v-model:value="bindAccountForm.depositBankArea"
            :options="addressOptions"
            placeholder="请选择开户行省市"
            style="width: 100%"
            expand-trigger="hover"
            :show-search="true"
            :field-names="{ label: 'label', value: 'value', children: 'children' }"
            @change="handleDepositBankAreaChange"
          />
        </a-form-item>
        <a-form-item label="开户行支行" name="depositBankName">
          <a-input v-model:value="bindAccountForm.depositBankName" placeholder="请输入具体开户网点名称" />
        </a-form-item>
        <a-form-item label="账户号码" name="cardNo">
          <a-input v-model:value="bindAccountForm.cardNo" placeholder="请输入银行账户号码" />
        </a-form-item>
      </a-form>
      </div>
    </a-modal>

    <!-- 特约商户开通模态框 -->
    <a-modal
      v-model:open="specialMerchantModalVisible"
      title="申请开通特约商户"
      width="600px"
      :mask-closable="false"
      :keyboard="false"
      @cancel="handleSpecialMerchantCancel"
      centered
    >
      <div style="padding: 20px 0;">
        <a-form
          ref="specialMerchantFormRef"
          :model="specialMerchantForm"
          :rules="specialMerchantRules"
          layout="vertical"
        >
          <a-form-item name="bankAccountLicensePic" label="银行开户许可证">
            <div class="upload-container">
              <!-- 已上传的图片 -->
              <div v-for="(image, index) in uploadedImages" :key="index" class="uploaded-image-item">
                <img :src="image.url" alt="银行开户许可证" @click="previewImage(image.url)" />
                <div class="image-remove" @click="removeImage(index)" :style="{ pointerEvents: specialMerchantLoading ? 'none' : 'auto' }">
                  <close-outlined />
                </div>
              </div>
              <!-- 上传按钮 -->
              <div 
                v-if="uploadedImages.length < 1" 
                class="upload-btn" 
                :class="{ disabled: uploading || specialMerchantLoading }" 
                @click="triggerFileInput"
              >
                <div class="upload-icon">
                  <plus-outlined v-if="!uploading" />
                  <loading-outlined v-if="uploading" class="spinning" />
                </div>
                <div class="upload-text">{{ uploading ? '上传中...' : '点击上传' }}</div>
              </div>
              <!-- 隐藏的文件输入框 -->
              <input
                ref="fileInput"
                type="file"
                accept="image/*"
                style="display: none"
                @change="handleFileChange"
                :disabled="uploading || specialMerchantLoading"
              />
            </div>
            <div class="field-tip">支持.jpg .png格式，最多上传1张图片</div>
          </a-form-item>
        </a-form>
      </div>

      <!-- 底部操作按钮 -->
      <template #footer>
        <div class="modal-footer">
          <a-button @click="handleSpecialMerchantCancel">取消</a-button>
          <a-button type="primary" @click="handleSpecialMerchantConfirm" :loading="specialMerchantLoading">确定</a-button>
        </div>
      </template>
    </a-modal>

    <!-- 图片预览模态框 -->
    <a-modal
      v-model:open="imageModalVisible"
      title="图片预览"
      :footer="null"
      width="600px"
      centered
    >
      <div class="image-preview">
        <a-image
          :src="currentImageUrl"
          :preview="false"
          style="width: 100%"
        />
      </div>
    </a-modal>

  </div>
</template>

<script setup>
import { ref, onMounted, reactive } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { PlusOutlined, CloseOutlined, LoadingOutlined } from '@ant-design/icons-vue';
import { financialInfoApi } from '/@/api/business/merchant-settings/financial-info-api';
import { basicInfoApi } from '/@/api/business/merchant-settings/basic-info-api';
import { fileApi } from '/@/api/support/file-api';
import { 
  VERIFICATION_STATUS_ENUM, 
  OPEN_STATUS_ENUM, 
  BANK_LIST
} from '/@/constants/business/merchant-settings/financial-info-const';
import { smartSentry } from '/@/lib/smart-sentry';
import { provinceCityOptions as addressOptions } from '/@/utils/cascader-address-options';
import { useUserStore } from '/@/store/modules/system/user';

// 页面加载状态
const loading = ref(false);

// 获取用户store
const userStore = useUserStore();

// 对公账户信息
const corporateAccount = ref({
  status: 'NOT_VERIFIED',
  accountName: '',
  bankCode: '',
  accountNumber: '',
  bankBranch: '',
  accountType: ''
});

// 支付钱包信息
const paymentWallet = ref({
  state: 3, // 宝付开户状态[1:成功; 0:失败; -1:异常; 2:开户处理中; 3:未开通]
  errorMsg: '', // 错误说明
  balance: 0, // availableBal 账簿可用余额
  pendingBalance: 0, // pendingBal 在途资金余额
  totalBalance: 0 // currBal 账簿余额
});

// 商户报备信息
const merchantReport = ref({
  reportState: 'NONE', // 报备状态[NONE: 未报备, PROCESSING:处理中, SUCCESS:成功, FAIL:失败]
  reportErrMsg: '这里是驳回原因这里是驳回原因' // 报备业务处理错误描述
});

// 特约商户开通信息
const specialMerchant = ref({
  status: 'NONE', // 特约商户状态[NONE: 未开通, PROCESSING:处理中, SUCCESS:成功, FAIL:失败]
  errorMsg: '', // 错误说明
  // 新增字段：特约商户ID，用于重新提交或查看详情
  specialMerchantId: null,
  // 银行开户许可证图片地址，用于回显
  bankAccountLicensePic: ''
});

// 绑定对公账户相关
const bindAccountModalVisible = ref(false);
const bindAccountLoading = ref(false);
const bindAccountFormRef = ref();
const isEditMode = ref(false); // 区分是绑定还是编辑模式
const bindAccountForm = reactive({
  storeId: 0,  // 门店ID，从用户store中获取
  corporateAccount: '',
  bankName: '',
  depositBankArea: [], // 开户行省市级联选择器的值
  depositBankProvince: '',
  depositBankCity: '',
  depositBankName: '',
  cardNo: '',
  // 省市编码，用于回显和提交
  depositBankProvinceNo: '',
  depositBankCityNo: ''
});

// 特约商户开通模态框相关
const specialMerchantModalVisible = ref(false);
const specialMerchantLoading = ref(false);
const specialMerchantFormRef = ref();
const specialMerchantForm = reactive({
  bankAccountLicensePic: ''
});

// 图片上传相关
const fileInput = ref(null);
const uploadedImages = ref([]);
const imageModalVisible = ref(false);
const currentImageUrl = ref('');
const uploading = ref(false);


const bindAccountRules = {
  corporateAccount: [
    { required: true, message: '请输入对公账户名称', trigger: 'blur' }
  ],
  bankName: [
    { required: true, message: '请选择开户银行', trigger: 'change' }
  ],
  depositBankArea: [
    { required: true, message: '请选择开户行省市', trigger: 'change' }
  ],
  depositBankName: [
    { required: true, message: '请输入开户行支行', trigger: 'blur' }
  ],
  cardNo: [
    { required: true, message: '请输入账户号码', trigger: 'blur' },
    { pattern: /^\d+$/, message: '账户号码只能包含数字', trigger: 'blur' }
  ]
};

// 特约商户表单验证规则
const specialMerchantRules = {
  bankAccountLicensePic: [
    { required: true, message: '请上传银行开户许可证', trigger: 'change' }
  ]
};

// 获取认证状态描述
const getVerificationStatusDesc = (status) => {
  return VERIFICATION_STATUS_ENUM[status]?.desc || '未知状态';
};

// 获取认证状态颜色
const getVerificationStatusColor = (status) => {
  return VERIFICATION_STATUS_ENUM[status]?.color || '#999';
};

// 获取开通状态描述
const getOpenStatusDesc = (status) => {
  return OPEN_STATUS_ENUM[status]?.desc || '未知状态';
};

// 获取开通状态颜色
const getOpenStatusColor = (status) => {
  return OPEN_STATUS_ENUM[status]?.color || '#999';
};

// 获取宝付开户状态描述
const getBaofooStateDesc = (state) => {
  const stateMap = {
    1: '成功',
    0: '失败',
    '-1': '异常',
    2: '开户处理中',
    3: '未开通'
  };
  return stateMap[state] || '未知状态';
};

// 获取宝付开户状态颜色
const getBaofooStateColor = (state) => {
  const colorMap = {
    1: 'success',
    0: 'error',
    '-1': 'warning',
    2: 'processing',
    3: 'default'
  };
  return colorMap[state] || 'default';
};

// 获取银行描述
const getBankDesc = (bankCode) => {
  const bank = BANK_LIST.find(item => item.value === bankCode);
  return bank?.label || '';
};

// 获取开户行地址
const getDepositBankLocation = () => {
  const province = corporateAccount.value.depositBankProvince;
  const city = corporateAccount.value.depositBankCity;
  if (province && city) {
    return `${province} ${city}`;
  }
  return '';
};

// 格式化金额
const formatAmount = (amount) => {
  if (!amount && amount !== 0) return '0.00';
  return Number(amount).toFixed(2);
};

// 获取当前门店ID
const getCurrentStoreId = () => {
  const currentStore = userStore.getCurrentStore();
  if (currentStore && currentStore.id) {
    return currentStore.id;
  }
  message.warning('获取门店信息失败，请重新登录');
  return null;
};

// 处理省市级联选择器变化
const handleDepositBankAreaChange = (values, selectedOptions) => {
  console.log('省市级联选择器变化:', { values, selectedOptions });
  
  if (selectedOptions && selectedOptions.length >= 2) {
    // 设置省市名称
    bindAccountForm.depositBankProvince = selectedOptions[0].label;
    bindAccountForm.depositBankCity = selectedOptions[1].label;
    // 保存省市编码值，供提交时使用
    bindAccountForm.depositBankProvinceNo = values[0];
    bindAccountForm.depositBankCityNo = values[1];
  } else {
    bindAccountForm.depositBankProvince = '';
    bindAccountForm.depositBankCity = '';
    bindAccountForm.depositBankProvinceNo = '';
    bindAccountForm.depositBankCityNo = '';
  }
};

// 银行搜索过滤函数
const filterBankOption = (input, option) => {
  // 如果输入为空或只包含空格，显示所有选项
  if (!input || input.trim() === '') {
    return true;
  }
  
  // 从option中获取银行名称进行匹配
  const bankName = option.value || '';
  return bankName.toLowerCase().includes(input.toLowerCase().trim());
};

// 处理银行选择下拉框按空格键消失的问题
const handleBankSelectKeydown = (event) => {
  // 只有在按空格键且已有选择值时才阻止默认行为
  if (event.code === 'Space' && bindAccountForm.bankName) {
    event.preventDefault();
    event.stopPropagation();
  }
};

// 加载财务信息
const loadFinancialInfo = async () => {
  try {
    loading.value = true;
    
    // 获取当前门店ID
    const currentStoreId = getCurrentStoreId();
    if (!currentStoreId) {
      return;
    }
    
    console.log('获取财务信息的门店ID:', currentStoreId);
    // 使用基本信息API获取门店数据
    const result = await basicInfoApi.getStoreBasicInfo(currentStoreId);
    console.log('财务信息API响应:', result);
    
    if (result.data) {
      const storeData = result.data;
      
      // 从基本信息API提取对公账户信息
      // 根据certificateStatus判断认证状态：1-已认证，0-未认证
      const isVerified = storeData.certificateStatus === 1;
      
      corporateAccount.value = {
        status: isVerified ? 'VERIFIED' : 'NOT_VERIFIED',
        accountName: storeData.corporateAccount || '',
        bankName: storeData.bankName || '',
        accountNumber: storeData.cardNo || '',
        cardNo: storeData.cardNo || '',
        depositBankProvince: storeData.depositBankProvince || '',
        depositBankCity: storeData.depositBankCity || '',
        depositBankName: storeData.depositBankName || '',
        bankBranch: storeData.depositBankName || '',
        accountType: storeData.cardNo ? 'BASIC' : '',
        // 保存省市编码，用于编辑时回显 
        depositBankProvinceNo: storeData.depositBankProvinceNo || '',
        depositBankCityNo: storeData.depositBankCityNo || ''
      };
      
      // 保存银行开户许可证图片地址，用于特约商户申请时回显
      if (storeData.bankAccountLicensePic) {
        specialMerchant.value.bankAccountLicensePic = storeData.bankAccountLicensePic;
      }
      
      // 直接获取宝付钱包余额，不再依赖certificateStatus判断
      await loadBaofooBalance(currentStoreId);
      
      // 如果宝付钱包开通状态为1（成功），获取商户报备状态
      if (paymentWallet.value.state === 1) {
        await loadMerchantReportStatus(currentStoreId);
        // 同时加载特约商户状态
        await loadSpecialMerchantStatus(currentStoreId);
      }
      
      console.log('解析后的对公账户信息:', corporateAccount.value);
      console.log('解析后的支付钱包信息:', paymentWallet.value);
      console.log('解析后的商户报备信息:', merchantReport.value);
    }
    
  } catch (error) {
    console.error('加载财务信息失败:', error);
    smartSentry.captureError(error);
    if (error.response) {
      message.error(error.response.data?.msg || '加载财务信息失败');
    } else {
      message.error(error.message || '网络请求失败');
    }
  } finally {
    loading.value = false;
  }
};

// 加载宝付钱包余额
const loadBaofooBalance = async (storeId) => {
  try {
    console.log('获取宝付钱包余额，门店ID:', storeId);
    const result = await financialInfoApi.getBaofooBalance(storeId);
    console.log('宝付钱包余额API响应:', result);
    
    if (result.code === 0 && result.ok && result.data) {
      const balanceData = result.data;
      const previousState = paymentWallet.value.state;
      
      // 更新支付钱包信息，使用新的数据结构
      paymentWallet.value = {
        state: balanceData.state !== undefined ? balanceData.state : 3, // 宝付开户状态
        errorMsg: balanceData.errorMsg || '', // 错误说明
        balance: balanceData.availableBal, // 账簿可用余额，保持null值
        pendingBalance: balanceData.pendingBal, // 在途资金余额，保持null值
        totalBalance: balanceData.currBal // 账簿余额，保持null值
      };
      
      console.log('更新后的宝付钱包信息:', paymentWallet.value);
      
      // 如果钱包状态从非成功状态变为成功状态，或者状态已经是成功状态，获取报备状态
      if (paymentWallet.value.state === 1 && (previousState !== 1 || merchantReport.value.reportState === 'NONE')) {
        console.log('钱包状态为成功，获取商户报备状态');
        await loadMerchantReportStatus(storeId);
        // 同时获取特约商户状态
        await loadSpecialMerchantStatus(storeId);
      }
    }
  } catch (error) {
    console.error('获取宝付钱包余额失败:', error);
    // 余额获取失败不影响主流程，只记录错误
    smartSentry.captureError(error);
  }
};

// 加载商户报备状态
const loadMerchantReportStatus = async (storeId) => {
  try {
    console.log('获取商户报备状态，门店ID:', storeId);
    const result = await financialInfoApi.getMerchantReportStatus(storeId);
    console.log('商户报备状态API响应:', result);
    
    if (result.code === 0 && result.data) {
      merchantReport.value = {
        reportState: result.data.reportState || 'NONE',
        reportErrMsg: result.data.reportErrMsg || ''
      };
      
      console.log('更新后的商户报备信息:', merchantReport.value);
    } else {
      // 如果接口返回失败，设置默认状态
      merchantReport.value = {
        reportState: 'NONE',
        reportErrMsg: result.msg || ''
      };
    }
  } catch (error) {
    console.error('获取商户报备状态失败:', error);
    // 报备状态获取失败不影响主流程，只记录错误，设置默认状态
    merchantReport.value = {
      reportState: 'NONE',
      reportErrMsg: ''
    };
    smartSentry.captureError(error);
  }
};

// 加载特约商户状态
const loadSpecialMerchantStatus = async (storeId) => {
  try {
    console.log('获取特约商户状态，门店ID:', storeId);
    const result = await financialInfoApi.getSpecialMerchantStatus(storeId);
    console.log('特约商户状态API响应:', result);

    if (result.code === 0 && result.ok && result.data) {
      // 根据新的接口返回值结构解析状态
      const retCode = result.data.retCode;
      let status = 'NONE';
      
      // 映射retCode到我们的状态系统
      // retCode: 1成功, 0失败, 2处理中, 3待升级
      switch (retCode) {
        case 1:
          status = 'SUCCESS';
          break;
        case 0:
          status = 'FAIL';
          break;
        case 2:
          status = 'PROCESSING';
          break;
        case 3:
        default:
          status = 'NONE';
          break;
      }
      
      specialMerchant.value = {
        ...specialMerchant.value, // 保留现有数据，特别是 bankAccountLicensePic
        status: status,
        errorMsg: result.data.errorMsg || '',
        specialMerchantId: result.data.specialMerchantId || null,
        retCode: retCode, // 保存原始状态码用于调试
        errorCode: result.data.errorCode || ''
      };
      console.log('更新后的特约商户信息:', specialMerchant.value);
    } else {
      specialMerchant.value = {
        ...specialMerchant.value, // 保留现有数据，特别是 bankAccountLicensePic
        status: 'NONE',
        errorMsg: result.msg || '',
        specialMerchantId: null,
        retCode: null,
        errorCode: ''
      };
      console.log('特约商户状态获取失败，设置默认状态:', specialMerchant.value);
    }
  } catch (error) {
    console.error('获取特约商户状态失败:', error);
    specialMerchant.value = {
      ...specialMerchant.value, // 保留现有数据，特别是 bankAccountLicensePic
      status: 'NONE',
      errorMsg: error.response?.data?.msg || error.message || '',
      specialMerchantId: null,
      retCode: null,
      errorCode: ''
    };
    smartSentry.captureError(error);
  }
};

// 处理绑定账户
const handleBindAccount = () => {
  bindAccountModalVisible.value = true;
  // 如果已有信息，回填表单
  if (corporateAccount.value.accountName) {
    isEditMode.value = true; // 设置为编辑模式
    Object.assign(bindAccountForm, {
      corporateAccount: corporateAccount.value.accountName,
      bankName: corporateAccount.value.bankName || '',
      depositBankProvince: corporateAccount.value.depositBankProvince || '',
      depositBankCity: corporateAccount.value.depositBankCity || '',
      depositBankName: corporateAccount.value.depositBankName || '',
      cardNo: corporateAccount.value.accountNumber || '',
      // 同时设置省市编码，确保数据完整
      depositBankProvinceNo: corporateAccount.value.depositBankProvinceNo || '',
      depositBankCityNo: corporateAccount.value.depositBankCityNo || ''
    });
    
    // 回填省市级联选择器 - 原生a-cascader使用字符串数组格式
    if (corporateAccount.value.depositBankProvinceNo && corporateAccount.value.depositBankCityNo) {
      bindAccountForm.depositBankArea = [
        corporateAccount.value.depositBankProvinceNo,
        corporateAccount.value.depositBankCityNo
      ];
      // ⚠️ 重要：确保编码字段也被正确设置，以防用户不重新选择省市直接提交
      bindAccountForm.depositBankProvinceNo = corporateAccount.value.depositBankProvinceNo;
      bindAccountForm.depositBankCityNo = corporateAccount.value.depositBankCityNo;
    } else {
      bindAccountForm.depositBankArea = [];
    }
  } else {
    isEditMode.value = false; // 设置为绑定模式
    // 如果没有现有信息，重置表单
    Object.assign(bindAccountForm, {
      storeId: 0,
      corporateAccount: '',
      bankName: '',
      depositBankArea: [],
      depositBankProvince: '',
      depositBankCity: '',
      depositBankName: '',
      cardNo: '',
      depositBankProvinceNo: '',
      depositBankCityNo: ''
    });
  }
};

// 确认绑定账户
const handleBindAccountConfirm = async () => {
  try {
    await bindAccountFormRef.value.validate();
    
    bindAccountLoading.value = true;
    
    // 获取当前门店ID
    const currentStoreId = getCurrentStoreId();
    if (!currentStoreId) {
      return;
    }
    
    // 准备请求参数
    const requestData = {
      storeId: currentStoreId,
      corporateAccount: bindAccountForm.corporateAccount,
      bankName: bindAccountForm.bankName,
      depositBankProvince: bindAccountForm.depositBankProvince,
      depositBankCity: bindAccountForm.depositBankCity,
      depositBankName: bindAccountForm.depositBankName,
      cardNo: bindAccountForm.cardNo,
      // 添加省市编码
      depositBankProvinceNo: bindAccountForm.depositBankProvinceNo,
      depositBankCityNo: bindAccountForm.depositBankCityNo
    };
    
    console.log('绑定对公账户请求参数:', requestData);
    const result = await financialInfoApi.bindCorporateAccount(requestData);
    console.log('绑定对公账户API响应:', result);
    if (result.code === 0 && result.ok) {
      message.success(isEditMode.value ? '编辑成功' : '绑定成功');
      bindAccountModalVisible.value = false;
      await loadFinancialInfo();
    } else {
      message.error(result.msg || (isEditMode.value ? '编辑失败' : '绑定失败'));
    }
  } catch (error) {
    console.error('绑定对公账户失败:', error);
    if (error.response) {
      message.error(error.response.data?.msg || (isEditMode.value ? '编辑失败' : '绑定失败'));
    } else {
      message.error(error.message || '网络请求失败');
    }
  } finally {
    bindAccountLoading.value = false;
  }
};

// 取消绑定账户
const handleBindAccountCancel = () => {
  bindAccountModalVisible.value = false;
  isEditMode.value = false; // 重置编辑模式状态
  bindAccountFormRef.value?.resetFields();
  // 重置表单数据
  Object.assign(bindAccountForm, {
    storeId: 0,
    corporateAccount: '',
    bankName: '',
    depositBankArea: [],
    depositBankProvince: '',
    depositBankCity: '',
    depositBankName: '',
    cardNo: '',
    depositBankProvinceNo: '',
    depositBankCityNo: ''
  });
};

// 处理开通钱包
const handleOpenWallet = () => {
  Modal.confirm({
    title: '宝付钱包',
    content: '确定要申请开通宝付钱包吗？',
    onOk: async () => {
      try {
        // 获取当前门店ID
        const currentStoreId = getCurrentStoreId();
        if (!currentStoreId) {
          return;
        }
        
        console.log('门店ID:', currentStoreId);
        
        // 调用开通宝付钱包API
        const result = await financialInfoApi.openPaymentWallet(currentStoreId);
        console.log('开通宝付钱包API响应:', result);
        
        if (result.code === 0 && result.ok) {
          message.success('申请成功');
          // 重新加载财务信息（包括钱包状态和报备状态）
          await loadFinancialInfo();
        } else {
          message.error(result.msg || '申请失败');
        }
      } catch (error) {
        console.error('申请开通宝付钱包失败:', error);
        if (error.response) {
          message.error(error.response.data?.msg || '申请开通失败');
        } else {
          message.error(error.message || '网络请求失败');
        }
      }
    }
  });
};

const handleMerchantReport = () => {
  Modal.confirm({
    title: '提示',
    content: '确定要提交报备吗？',
    onOk: async () => {
      try {
        // 获取当前门店ID
        const currentStoreId = getCurrentStoreId();
        if (!currentStoreId) {
          return;
        }
        
        // 设置为处理中状态
        merchantReport.value.reportState = 'PROCESSING';
        
        console.log('提交商户报备，门店ID:', currentStoreId);
        const result = await financialInfoApi.merchantReport(currentStoreId);
        console.log('商户报备API响应:', result);
        
        if (result.code === 0 && result.data) {
          // 处理报备状态：null表示失败，否则使用实际状态
          const reportState = result.data.reportState || (result.data.reportErrMsg ? 'FAIL' : 'NONE');
          const reportErrMsg = result.data.reportErrMsg || '';
          
          // 更新状态
          merchantReport.value = { reportState, reportErrMsg };
          
          // 显示对应消息
          const messageMap = {
            'SUCCESS': () => message.success('报备成功'),
            'FAIL': () => message.error(`报备失败${reportErrMsg ? '：' + reportErrMsg : ''}`),
            'PROCESSING': () => message.info('报备已提交，正在处理中')
          };
          
          messageMap[reportState]?.();
          console.log('报备状态已更新:', merchantReport.value);
        } else {
          merchantReport.value = { reportState: 'FAIL', reportErrMsg: result.msg || '报备失败' };
          message.error(result.msg || '报备失败');
        }
      } catch (error) {
        console.error('商户报备失败:', error);
        merchantReport.value.reportState = 'FAIL';
        merchantReport.value.reportErrMsg = error.response?.data?.msg || error.message || '网络请求失败';
        
        if (error.response) {
          message.error(error.response.data?.msg || '报备失败');
        } else {
          message.error(error.message || '网络请求失败');
        }
      }
    }
  });
};

// 显示报备失败原因
const handleShowFailReason = () => {
  Modal.info({
    title: '报备失败',
    content: merchantReport.value.reportErrMsg || '这里是驳回原因这里是驳回原因',
    okText: '我知道了'
  });
};

// 处理钱包操作（开通）
const handleWalletAction = () => {
  // 只提供开通操作
  handleOpenWallet();
};

// 处理刷新钱包信息
const handleRefreshWallet = async () => {
  try {
    // 获取当前门店ID
    const currentStoreId = getCurrentStoreId();
    if (!currentStoreId) {
      return;
    }
    
    // 重新获取宝付钱包余额（会自动触发报备状态获取）
    await loadBaofooBalance(currentStoreId);
    message.success('刷新成功');
  } catch (error) {
    console.error('刷新宝付钱包信息失败:', error);
    message.error('刷新失败');
  }
};

// 处理提现
const handleWithdraw = () => {
  // 临时调试：显示开通宝付钱包确认弹窗
  Modal.confirm({
    title: '宝付钱包',
    content: '确定要申请开通宝付钱包吗？',
    onOk: async () => {
      try {
        // 获取当前门店ID
        const currentStoreId = getCurrentStoreId();
        if (!currentStoreId) {
          return;
        }
        
        console.log('门店ID:', currentStoreId);
        
        // 调用开通宝付钱包API
        const result = await financialInfoApi.openPaymentWallet(currentStoreId);
        console.log('开通宝付钱包API响应:', result);
        
        if (result.code === 0 && result.ok) {
          message.success('开通成功');
          // 重新加载财务信息（包括钱包状态和报备状态）
          await loadFinancialInfo();
        } else {
          message.error(result.msg || '开通失败');
        }
      } catch (error) {
        console.error('开通宝付钱包失败:', error);
        if (error.response) {
          message.error(error.response.data?.msg || '开通失败');
        } else {
          message.error(error.message || '网络请求失败');
        }
      }
    }
  });
};

// 处理特约商户申请
const handleSpecialMerchantApply = () => {
  specialMerchantModalVisible.value = true;
  
  // 如果已有银行开户许可证图片，则回显，否则重置表单
  if (specialMerchant.value.bankAccountLicensePic) {
    // 回显已有图片
    Object.assign(specialMerchantForm, {
      bankAccountLicensePic: specialMerchant.value.bankAccountLicensePic
    });
    uploadedImages.value = [{ url: specialMerchant.value.bankAccountLicensePic }];
  } else {
    // 重置表单
    Object.assign(specialMerchantForm, {
      bankAccountLicensePic: ''
    });
    uploadedImages.value = []; // 清空已上传图片
  }
};

// 取消特约商户申请
const handleSpecialMerchantCancel = () => {
  specialMerchantModalVisible.value = false;
  specialMerchantFormRef.value?.resetFields();
  uploadedImages.value = []; // 清空已上传图片
};

// 处理特约商户申请确认
const handleSpecialMerchantConfirm = async () => {
  try {
    await specialMerchantFormRef.value.validate();
    
    specialMerchantLoading.value = true;
    
    // 获取当前门店ID
    const currentStoreId = getCurrentStoreId();
    if (!currentStoreId) {
      return;
    }

    // 第一步：上传银行开户许可证
    const applyRequestData = {
      storeId: currentStoreId,
      bankAccountLicensePic: specialMerchantForm.bankAccountLicensePic
    };

    console.log('上传银行开户许可证请求参数:', applyRequestData);
    const applyResult = await financialInfoApi.applySpecialMerchant(applyRequestData);
    console.log('上传银行开户许可证API响应:', applyResult);

    if (applyResult.code === 0 && applyResult.ok) {
      console.log('银行开户许可证上传成功，开始开通特约商户');
      
      // 第二步：开通特约商户
      const openResult = await financialInfoApi.openSpecialMerchant(currentStoreId);
      console.log('开通特约商户API响应:', openResult);
      
      if (openResult.code === 0 && openResult.ok) {
        message.success('申请开通特约商户提交成功');
        specialMerchantModalVisible.value = false;
        await loadFinancialInfo();
      } else {
        message.error(openResult.msg || '申请开通特约商户失败');
      }
    } else {
      message.error(applyResult.msg || '上传银行开户许可证失败');
    }
  } catch (error) {
    console.error('申请特约商户失败:', error);
    if (error.response) {
      message.error(error.response.data?.msg || '申请失败');
    } else {
      message.error(error.message || '网络请求失败');
    }
  } finally {
    specialMerchantLoading.value = false;
  }
};

// 处理图片上传
const handleFileChange = async (event) => {
  const file = event.target.files[0];
  if (!file) return;

  const isImage = file.type.startsWith('image/');
  if (!isImage) {
    message.error('请上传图片文件！');
    return;
  }

  uploading.value = true;
  try {
    const formData = new FormData();
    formData.append('file', file);
    const result = await fileApi.uploadFile(formData, 1); // 传递 folder=1
    console.log('图片上传API响应:', result);

    if (result.code === 0 && result.ok && result.data) {
      uploadedImages.value.push({ url: result.data.fileUrl }); // 使用 fileUrl 字段
      specialMerchantForm.bankAccountLicensePic = result.data.fileUrl; // 更新表单值，使用 fileUrl
      message.success('图片上传成功');
    } else {
      message.error(result.msg || '图片上传失败');
    }
  } catch (error) {
    console.error('图片上传失败:', error);
    message.error(error.response?.data?.msg || error.message || '网络请求失败');
  } finally {
    uploading.value = false;
  }
};

// 移除已上传的图片
const removeImage = (index) => {
  uploadedImages.value.splice(index, 1);
  specialMerchantForm.bankAccountLicensePic = ''; // 清空表单值
};

// 预览图片
const previewImage = (url) => {
  currentImageUrl.value = url;
  imageModalVisible.value = true;
};

// 触发文件输入框
const triggerFileInput = () => {
  if (!uploading.value && !specialMerchantLoading.value) {
    fileInput.value?.click();
  }
};

// 显示特约商户申请失败原因
const handleShowSpecialMerchantFailReason = () => {
  Modal.info({
    title: '特约商户申请失败',
    content: specialMerchant.value.errorMsg || '这里是驳回原因这里是驳回原因',
    okText: '我知道了'
  });
};


// 组件挂载时加载数据
onMounted(() => {
  loadFinancialInfo();
});
</script>

<style scoped>
.financial-info-container {
  
}

.info-section {
  margin-bottom: 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
  margin: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px 24px;
}

.info-item {
  display: flex;
  align-items: flex-start;
  min-height: 32px;
}

.info-label {
  display: inline-block;
  color: #666;
  font-size: 14px;
  margin-right: 16px;
  flex-shrink: 0;
}

.info-value {
  color: #333;
  font-size: 14px;
  flex: 1;
  word-break: break-all;
}

.link-button {
  padding: 0;
  height: auto;
  font-size: 14px;
}

.report-container {
  flex: 1; /* 让报备容器占据一半空间 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #e6f7ff;
  padding: 20px;
  border-radius: 4px;
}

.report-info .section-title {
  margin-bottom: 8px;
}

.report-desc {
  font-size: 14px;
  color: #666;
  margin-bottom: 0;
}

.report-actions {
  display: flex;
  align-items: center;
}

.fail-actions {
  display: flex;
  align-items: center;
}

.dual-container {
  display: flex;
  justify-content: space-between;
  gap: 20px; /* 调整两个容器之间的间距 */
}

.special-merchant-container {
  flex: 1; /* 让特约商户容器占据剩余空间 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #f0f9eb; /* 浅绿色背景 */
  padding: 20px;
  border-radius: 4px;
}

.upload-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  padding: 10px 0;
}

.uploaded-image-item {
  position: relative;
  width: 100px;
  height: 100px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
}

.uploaded-image-item img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
}

.image-remove {
  position: absolute;
  top: 0;
  right: 0;
  background-color: rgba(0, 0, 0, 0.5);
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 10;
}

.upload-btn {
  width: 100px;
  height: 100px;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  background-color: #fafafa;
  transition: border-color 0.3s ease;
}

.upload-btn:hover {
  border-color: #1890ff;
}

.upload-btn.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.upload-icon {
  font-size: 24px;
  color: #1890ff;
}

.upload-text {
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

.field-tip {
  font-size: 12px;
  color: #999;
  margin-top: 5px;
}

.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.image-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 400px; /* 调整预览框高度 */
}

.spinning {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式布局 */
@media (max-width: 1200px) {
  .info-grid {
    grid-template-columns: 1fr 1fr;
  }
  
  .dual-container {
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .section-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
  
  .info-label {
    margin-right: 12px;
  }
  
  .dual-container {
    flex-direction: column;
    gap: 16px;
  }
  
  .report-container,
  .special-merchant-container {
    flex: none; /* 移动端不使用flex比例 */
  }
}
</style> 