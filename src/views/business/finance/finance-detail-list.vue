<!--
  * 财务收支明细列表
  *
  * @Author:    SmartAdmin
  * @Date:      2024-01-01
  * @Copyright  SmartAdmin
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="订单号" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.orderNo" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item label="用户姓名" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.customerName" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item label="用户手机号" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.customerPhone" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item label="收款日期" class="smart-query-form-item">
        <a-range-picker 
          v-model:value="queryForm.deductTimeRange"
          format="YYYY/MM/DD"
          style="width: 240px"
        />
      </a-form-item>

      <a-form-item label="流水号" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.deductSerialNo" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item label="商品名称" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.productName" 
          placeholder="请输入" 
        />
      </a-form-item>

      <a-form-item label="收款方式" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.receivePaymentType" 
          placeholder="全部"
          allowClear
        >
          <a-select-option value="BANK_SEGMENT">账单付</a-select-option>
          <a-select-option value="ZFB_ANXIN">安心付</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="扣款状态" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.deductStatus" 
          placeholder="全部"
          allowClear
        >
          <a-select-option value="WAIT_AUTHORIZ">待授权</a-select-option>
          <a-select-option value="PENDING_DEDUCT">待扣款</a-select-option>
          <a-select-option value="PAID">已扣款</a-select-option>
          <a-select-option value="DEDUCT_FAIL">扣款失败</a-select-option>
          <a-select-option value="PAUSED">已暂停</a-select-option>
          <a-select-option value="TERMINATED">已解约</a-select-option>
          <a-select-option value="CANCELED">已取消</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="支付渠道" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.paymentChannels" 
          placeholder="全部"
          allowClear
        >
          <a-select-option value="DEDUCT">代扣</a-select-option>
          <a-select-option value="OFFLINE_PAYMENT">线下收款</a-select-option>
          <a-select-option value="SELF_PAYMENT">主动付款</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="邀请码" class="smart-query-form-item">
        <a-input 
          style="width: 160px" 
          v-model:value="queryForm.invitationCode" 
          placeholder="请输入" 
          @keypress="onlyNumber"
        />
      </a-form-item>

      <a-form-item label="下单日期" class="smart-query-form-item">
        <a-range-picker 
          v-model:value="queryForm.createTimeRange"
          format="YYYY/MM/DD"
          style="width: 240px"
        />
      </a-form-item>

      <a-form-item label="门店名称" class="smart-query-form-item">
        <StoreMultiSelect 
          v-model="queryForm.storeName" 
          width="200px"
          placeholder="请选择门店"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!-- 统计信息 -->
    <div class="finance-statistics">
      <div class="statistics-text">
        笔数：<strong class="statistics-number">{{ formatStatisticsNumber(statistics.totalCount) }}</strong>
        <a-divider type="vertical" />
        收款总金额：<strong class="statistics-number">{{ formatAmount(statistics.totalAmount) }}</strong>
      </div>
    </div>

    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="onExportFinanceDetail">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator 
          v-model="columns" 
          :tableId="TABLE_ID_CONST.BUSINESS.FINANCE.DETAIL" 
          :refresh="queryData" 
        />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="detailId"
      :scroll="{ x: 2200, y: 2000 }"
      bordered
      :pagination="false"
      :loading="tableLoading"
      @resizeColumn="handleResizeColumn"
    >
      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'orderNo'">
          <a @click="goToSegmentOrderDetail(record.orderId)" class="order-link">
            {{ text }}
          </a>
        </template>

        <template v-if="column.dataIndex === 'productName'">
          {{ text }}
        </template>

        <template v-if="column.dataIndex === 'customerPhone'">
          {{ maskPhoneNumber(text) }}
        </template>

        <template v-if="column.dataIndex === 'customerName'">
          {{ text }}
        </template>

        <template v-if="column.dataIndex === 'invitationCode'">
          <div class="invitation-info-cell">
            <div>邀请码：{{ record.invitationCode || '-' }}</div>
            <div>员工：{{ record.employeeName || '-' }}</div>
            <div>部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>

        <template v-if="column.dataIndex === 'deductSerialNo'">
          {{ text || '-' }}
        </template>

        <template v-if="column.dataIndex === 'orderAmount'">
          <span>
            {{ formatAmount(text) }}
          </span>
        </template>

        <template v-if="column.dataIndex === 'installmentNo'">
          {{ text !== null && text !== undefined ? text : '-' }}
        </template>

        <template v-if="column.dataIndex === 'receivableDate'">
          {{ text || '-' }}
        </template>

        <template v-if="column.dataIndex === 'installmentAmount'">
          <span>
            {{ formatAmount(text) }}
          </span>
        </template>

        <template v-if="column.dataIndex === 'receivePaymentType'">
          {{ getPaymentMethodText(text) }}
        </template>

        <template v-if="column.dataIndex === 'deductAmount'">
          <span>
            {{ formatAmount(text) }}
          </span>
        </template>

        <template v-if="column.dataIndex === 'discountAmount'">
          <span>
            {{ formatAmount(text) }}
          </span>
        </template>

        <template v-if="column.dataIndex === 'deductTime'">
          {{ text || '-' }}
        </template>

        <template v-if="column.dataIndex === 'createTime'">
          {{ text }}
        </template>

        <template v-if="column.dataIndex === 'deductStatus'">
          <a-tag :color="getDeductionStatusColor(text)">
            {{ getDeductionStatusText(text) }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'paymentChannels'">
          {{ getPaymentChannelText(text) }}
        </template>

        <template v-if="column.dataIndex === 'platformServiceFee'">
          <span>
            {{ formatAmount(text) }}
          </span>
        </template>

        <template v-if="column.dataIndex === 'platformAccountMethod'">
          {{ getPlatformAccountMethodText(text) }}
        </template>

        <template v-if="column.dataIndex === 'estimatedTransactionFees'">
          <span>
            {{ formatAmount(text) }}
          </span>
        </template>

        <template v-if="column.dataIndex === 'remark'">
          <div style="max-width: 200px; word-break: break-all;">
            {{ text || '-' }}
          </div>
        </template>

        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ text || '-' }}</span>
        </template>

      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共 ${total} 条数据`"
      />
    </div>

    <!-- 财务明细详情弹窗 -->
    <FinanceDetailModal 
      ref="detailModal" 
    />

    <!-- 备注编辑弹窗 -->
    <RemarkEditModal 
      ref="remarkModal" 
      @reloadList="queryData" 
    />
  </a-card>
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { 
  SearchOutlined, 
  ReloadOutlined, 
  ExportOutlined 
} from '@ant-design/icons-vue';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { financeDetailApi } from '/@/api/business/finance/finance-detail-api';
import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
import { 
  PLATFORM_ACCOUNT_METHOD_ENUM
} from '/@/constants/business/finance/finance-detail-const';
import { smartSentry } from '/@/lib/smart-sentry';
import TableOperator from '/@/components/support/table-operator/index.vue';
import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
import FinanceDetailModal from './components/finance-detail-modal.vue';
import RemarkEditModal from './components/remark-edit-modal.vue';
import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';
import _ from 'lodash';
import dayjs from 'dayjs';

const router = useRouter();

// ---------------------------- 表格列 ----------------------------

const columns = ref([
  {
    title: '订单编号',
    dataIndex: 'orderNo', // 订单编号
    width: 250,
    resizable: true
  },
  {
    title: '商品名称',
    dataIndex: 'productName', // 产品名称
    width: 200,
    resizable: true
  },
  {
    title: '用户手机号',
    dataIndex: 'customerPhone', // 客户手机号
    width: 130,
    resizable: true
  },
  {
    title: '用户姓名',
    dataIndex: 'customerName', // 客户姓名
    width: 100,
    resizable: true
  },
  {
    title: '邀请码',
    dataIndex: 'invitationCode', // 邀请码
    width: 200,
    resizable: true
  },
  {
    title: '流水号',
    dataIndex: 'deductSerialNo', // 扣款流水号
    width: 200,
    resizable: true
  },
  {
    title: '订单总金额',
    dataIndex: 'orderAmount', // 订单金额
    width: 120,
    resizable: true
  },
  {
    title: '期数',
    dataIndex: 'installmentNo', // 分期号
    width: 80,
    resizable: true
  },
  {
    title: '应收日期',
    dataIndex: 'receivableDate', // 应收日期
    width: 120,
    resizable: true
  },
  {
    title: '应收金额',
    dataIndex: 'installmentAmount', // 分期金额
    width: 100,
    resizable: true
  },
  {
    title: '收款方式',
    dataIndex: 'receivePaymentType', // 收款方式
    width: 120,
    resizable: true
  },
  {
    title: '实收金额',
    dataIndex: 'deductAmount', // 实收金额
    width: 100,
    resizable: true
  },
  {
    title: '优惠金额',
    dataIndex: 'discountAmount', // 优惠金额
    width: 100,
    resizable: true
  },
  {
    title: '收款时间',
    dataIndex: 'deductTime', // 收款时间
    width: 180,
    resizable: true
  },
  {
    title: '下单时间',
    dataIndex: 'createTime', // 下单时间
    width: 180,
    resizable: true
  },
  {
    title: '扣款状态',
    dataIndex: 'deductStatus', // 扣款状态
    width: 100,
    resizable: true
  },
  {
    title: '支付渠道',
    dataIndex: 'paymentChannels', // 支付渠道
    width: 100,
    resizable: true
  },
  {
    title: '平台服务费',
    dataIndex: 'platformServiceFee', // 平台服务费
    width: 120,
    resizable: true
  },
  {
    title: '平台入账方式',
    dataIndex: 'platformAccountMethod', // 平台入账方式
    width: 130,
    resizable: true
  },
  {
    title: '交易手续费（预估）',
    dataIndex: 'estimatedTransactionFees', // 交易手续费(预估)
    width: 150,
    resizable: true
  },
  {
    title: '备注',
    dataIndex: 'remark', // 备注
    width: 200,
    resizable: true,
    ellipsis: true
  },
  {
    title: '门店名称',
    dataIndex: 'storeName',
    width: 120,
    resizable: true
  },
]);

// ---------------------------- 查询数据表单和方法 ----------------------------

// 只允许输入数字
function onlyNumber(event) {
  // 允许数字、删除键、退格键
  const char = String.fromCharCode(event.which);
  if (!/[0-9]/.test(char) && event.which !== 8 && event.which !== 46) {
    event.preventDefault();
  }
}

// 获取本月第一天到今天的默认日期范围
function getDefaultDeductTimeRange() {
  const today = dayjs();
  const firstDayOfMonth = today.startOf('month');
  return [firstDayOfMonth, today];
}

const queryFormState = {
  orderNo: null, // 订单编号
  customerName: null, // 客户姓名
  customerPhone: null, // 客户手机号
  deductTimeRange: getDefaultDeductTimeRange(), // 收款日期范围
  deductSerialNo: null, // 扣款流水号
  productName: null, // 产品名称
  receivePaymentType: null, // 收款方式
  deductStatus: null, // 扣款状态
  paymentChannels: null, // 支付渠道
  invitationCode: null, // 邀请码
  createTimeRange: null, // 下单日期范围
  storeName: [], // 门店名称查询字段
  pageNum: 1, // 页码
  pageSize: 10, // 每页数量
  searchCount: true, // 是否查询总条数
  sortItemList: [] // 排序字段集合
};

// 查询表单form
const queryForm = reactive(_.cloneDeep(queryFormState));
// 表格加载loading
const tableLoading = ref(false);
// 表格数据
const tableData = ref([]);
// 总数
const total = ref(0);
// 统计信息
const statistics = ref({
  totalCount: 0,
  totalAmount: 0
});

function handleResizeColumn(w, col) {
  columns.value.forEach((item) => {
    if (item.dataIndex === col.dataIndex) {
      item.width = Math.floor(w);
      item.dragAndDropFlag = true;
    }
  });
}

// 重置查询条件
function resetQuery() {
  let pageSize = queryForm.pageSize;
  Object.assign(queryForm, _.cloneDeep(queryFormState));
  queryForm.pageSize = pageSize;
  queryData();
}

// 搜索
function onSearch() {
  queryForm.pageNum = 1;
  queryData();
}

// 统一参数处理函数
function prepareQueryParams() {
  const params = _.cloneDeep(queryForm);
  
  // 处理字符串类型参数：空值转null
  if (params.receivePaymentType === '' || params.receivePaymentType == null) {
    params.receivePaymentType = null;
  }
  if (params.deductStatus === '' || params.deductStatus == null) {
    params.deductStatus = null;
  }
  if (params.paymentChannels === '' || params.paymentChannels == null) {
    params.paymentChannels = null;
  }
  
  // 处理日期范围参数
  if (queryForm.deductTimeRange && queryForm.deductTimeRange.length === 2) {
    params.deductTimeStart = dayjs(queryForm.deductTimeRange[0]).format('YYYY-MM-DD');
    params.deductTimeEnd = dayjs(queryForm.deductTimeRange[1]).format('YYYY-MM-DD');
  }
  if (queryForm.createTimeRange && queryForm.createTimeRange.length === 2) {
    params.createStartDate = dayjs(queryForm.createTimeRange[0]).format('YYYY-MM-DD');
    params.createEndDate = dayjs(queryForm.createTimeRange[1]).format('YYYY-MM-DD');
  }
  delete params.deductTimeRange;
  delete params.createTimeRange;
  
  // 处理门店名称查询参数（多选时逗号拼接门店ID）
  if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
    // 如果选择了门店，将ID数组用逗号拼接成字符串
    params.storeName = params.storeName.join(',');
  } else {
    // 如果没有选择门店，传 null
    params.storeName = null;
  }
  
  return params;
}

// 查询数据
async function queryData() {
  tableLoading.value = true;
  try {
    // 使用统一的参数处理
    const params = prepareQueryParams();

    // 调用真实API
    let queryResult = await financeDetailApi.queryFinanceDetailList(params);
    let statisticsResult = await financeDetailApi.getFinanceStatistics(params);
    
    tableData.value = queryResult.data.list || [];
    total.value = queryResult.data.total || 0;
    
    // 使用统计接口返回的数据，映射字段名
    const statsData = statisticsResult.data || {};
    statistics.value = {
      totalCount: statsData.deductCount || 0, // 笔数
      totalAmount: statsData.totalDeductAmount || 0 // 收款总金额
    };
  } catch (e) {
    smartSentry.captureError(e);
  } finally {
    tableLoading.value = false;
  }
}



onMounted(queryData);

// ---------------------------- 状态显示方法 ----------------------------

// 格式化金额，添加千分符，移除¥符号
function formatAmount(amount) {
  if (!amount && amount !== 0) return '0.00';
  return Number(amount).toLocaleString('zh-CN', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });
}

// 格式化统计数字（整数），添加千分位
function formatStatisticsNumber(number) {
  if (!number && number !== 0) return '0';
  return Number(number).toLocaleString('zh-CN');
}

// 手机号脱敏处理
function maskPhoneNumber(phone) {
  if (!phone) return '-';
  
  // 去除所有非数字字符
  const cleanPhone = phone.toString().replace(/\D/g, '');
  
  // 如果不是11位手机号，直接返回原值
  if (cleanPhone.length !== 11) {
    return phone;
  }
  
  // 格式化为 137****1999
  return `${cleanPhone.substring(0, 3)}****${cleanPhone.substring(7)}`;
}

function getPaymentMethodText(method) {
  const methodMap = {
    'BANK_SEGMENT': '账单付',
    'ZFB_ANXIN': '安心付'
  };
  return methodMap[method] || method || '-';
}

function getDeductionStatusColor(status) {
  const colorMap = {
    'WAIT_AUTHORIZ': 'default',
    'PENDING_DEDUCT': 'blue',
    'PAID': 'success',
    'DEDUCT_FAIL': 'error',
    'PAUSED': 'warning',
    'TERMINATED': 'error',
    'CANCELED': 'default'
  };
  return colorMap[status] || 'default';
}

function getDeductionStatusText(status) {
  const statusMap = {
    'WAIT_AUTHORIZ': '待授权',
    'PENDING_DEDUCT': '待扣款',
    'PAID': '已扣款',
    'DEDUCT_FAIL': '扣款失败',
    'PAUSED': '已暂停',
    'TERMINATED': '已解约',
    'CANCELED': '已取消'
  };
  return statusMap[status] || status || '-';
}

function getPaymentChannelText(channel) {
  const channelMap = {
    'DEDUCT': '代扣',
    'OFFLINE_PAYMENT': '线下收款',
    'SELF_PAYMENT': '主动付款'
  };
  return channelMap[channel] || channel || '-';
}

function getPlatformAccountMethodText(method) {
  const methodEnum = Object.values(PLATFORM_ACCOUNT_METHOD_ENUM).find(item => item.value === method);
  return methodEnum ? methodEnum.label : '-';
}

// ---------------------------- 选择和批量操作 ----------------------------
// 已删除批量选择功能

// ---------------------------- 操作功能 ----------------------------

const detailModal = ref();
const remarkModal = ref();

// 查看财务明细详情
function viewFinanceDetail(record) {
  detailModal.value.showModal(record);
}

// 编辑备注
function editRemark(record) {
  remarkModal.value.showModal(record);
}

// 跳转到分期订单详情
const goToSegmentOrderDetail = (orderId) => {
  router.push({
    path: '/order/segment-order-detail',
    query: { id: orderId }
  });
};

// 查看订单详情
function viewOrderDetail(record) {
  message.info('跳转到订单详情页面');
}

// ---------------------------- 导出功能 ----------------------------

// 导出
async function onExportFinanceDetail() {
  message.info('导出功能开发中...');
}
</script>

<style scoped>
.finance-statistics {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.statistics-text {
  font-size: inherit;
  color: inherit;
  line-height: 1.5;
}

.smart-table-operate {
  display: flex;
  gap: 8px;
}

.order-link {
  color: #1890ff;
  cursor: pointer;
}

.order-link:hover {
  color: #40a9ff;
}

.statistics-number {
  color: #1890ff;
}

.invitation-info-cell {
  line-height: 1.6;
}

/* 筛选区域卡片圆角样式 */
.smart-query-form {
  border-radius: 4px;
}
</style> 