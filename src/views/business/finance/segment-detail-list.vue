<!--
  * 分段明细列表
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 16:00:00
  * @Copyright  SmartAdmin
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="订单号" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.orderNo" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="用户姓名" class="smart-query-form-item">
        <a-input 
          style="width: 150px" 
          v-model:value="queryForm.customerName" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="用户手机号" class="smart-query-form-item">
        <a-input 
          style="width: 180px" 
          v-model:value="queryForm.customerPhone" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="应收日期" class="smart-query-form-item">
        <a-range-picker 
          v-model:value="queryForm.dueDateRange"
          format="YYYY/MM/DD"
          style="width: 240px"
        />
      </a-form-item>

      <a-form-item label="商品名称" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.productName" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>
    </a-row>

    <a-row class="smart-query-form-row">
      <a-form-item label="订单状态" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.orderStatus" 
          placeholder="全部"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="WAIT_AUTHORIZ">待授权</a-select-option>
          <a-select-option value="EXECUTION_CONTRACT">履约中</a-select-option>
          <a-select-option value="ENDED">已结束</a-select-option>
          <a-select-option value="TERMINATED">已解约</a-select-option>
          <a-select-option value="PAUSED">已暂停</a-select-option>
          <a-select-option value="CANCELED">已取消</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="扣款状态" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.deductStatus" 
          placeholder="全部"
          allowClear
        >
          <a-select-option value="WAIT_AUTHORIZ">待授权</a-select-option>
          <a-select-option value="PENDING_DEDUCT">待扣款</a-select-option>
          <a-select-option value="PAID">已扣款</a-select-option>
          <a-select-option value="DEDUCT_FAIL">扣款失败</a-select-option>
          <a-select-option value="PAUSED">已暂停</a-select-option>
          <a-select-option value="TERMINATED">已解约</a-select-option>
          <a-select-option value="CANCELED">已取消</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="是否逾期" class="smart-query-form-item">
        <a-select 
          style="width: 100px" 
          v-model:value="queryForm.overdueFlag" 
          placeholder="全部"
          allowClear
        >
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="逾期天数" class="smart-query-form-item">
        <div style="display: flex; align-items: center;">
          <a-input-number
            style="width: 80px"
            v-model:value="queryForm.overdueDayStart"
            :min="0"
            placeholder="请输入"
          />
          <span style="margin: 0 8px">至</span>
          <a-input-number
            style="width: 80px"
            v-model:value="queryForm.overdueDayEnd"
            :min="0"
            placeholder="请输入"
          />
          <span style="margin-left: 8px">天</span>
        </div>
      </a-form-item>

      <a-form-item label="收款方式" class="smart-query-form-item">
        <a-select 
          style="width: 120px" 
          v-model:value="queryForm.receivePaymentType" 
          placeholder="全部"
          allowClear
        >
          <a-select-option value="BANK_SEGMENT">账单付</a-select-option>
          <a-select-option value="ZFB_ANXIN">安心付</a-select-option>
        </a-select>
      </a-form-item>
    </a-row>

    <a-row class="smart-query-form-row">
      <a-form-item label="订单代扣解除" class="smart-query-form-item">
        <a-select 
          style="width: 130px" 
          v-model:value="queryForm.deductUnbindFlag" 
          placeholder="全部"
          allowClear
        >
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="当期代扣解除" class="smart-query-form-item">
        <a-select 
          style="width: 130px" 
          v-model:value="queryForm.removeDeductFlag" 
          placeholder="全部"
          allowClear
        >
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="邀请码" class="smart-query-form-item">
        <a-input 
          style="width: 150px" 
          v-model:value="queryForm.invitationCode" 
          placeholder="请输入" 
          @keypress="onlyNumber"
        />
      </a-form-item>

      <a-form-item label="下单日期" class="smart-query-form-item">
        <a-range-picker 
          v-model:value="queryForm.orderDateRange"
          format="YYYY/MM/DD"
          style="width: 240px"
        />
      </a-form-item>

      <a-form-item label="扣款日期" class="smart-query-form-item">
        <a-range-picker 
          v-model:value="queryForm.deductionDateRange"
          format="YYYY/MM/DD"
          style="width: 240px"
        />
      </a-form-item>
    </a-row>

    <a-row class="smart-query-form-row">
      <a-form-item label="创建人" class="smart-query-form-item">
        <a-input 
          style="width: 150px" 
          v-model:value="queryForm.qrCreateBy" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="门店名称" class="smart-query-form-item">
        <StoreMultiSelect 
          v-model="queryForm.storeName" 
          width="200px"
          placeholder="请选择门店"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 统计信息 begin ----------->
    <div class="segment-statistics">
      <div class="statistics-text">
        笔数：<strong class="statistics-number">{{ formatStatisticsNumber(statistics.deductCount) }}</strong>
        <a-divider type="vertical" />
        应收总金额：<strong class="statistics-number">{{ formatAmount(statistics.totalInstallmentAmount) }}</strong>
        <a-divider type="vertical" />
        已收款总金额：<strong class="statistics-number">{{ formatAmount(statistics.totalDeductAmount) }}</strong>
      </div>
    </div>
    <!---------- 统计信息 end ----------->

    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="onExport">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator 
          v-model="columns" 
          :tableId="TABLE_ID_CONST.BUSINESS.FINANCE.SEGMENT_DETAIL" 
          :refresh="queryData" 
        />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="orderId"
      :scroll="{ x: 2200, y: 2000 }"
      bordered
      :pagination="false"
      :loading="tableLoading"
      @resizeColumn="handleResizeColumn"
    >
      <template #headerCell="{ column }">
        {{ column.title }}
      </template>

      <template #bodyCell="{ text, record, column }">
        <!-- 订单信息 -->
        <template v-if="column.dataIndex === 'orderInfo'">
          <div class="order-info-cell">
            <div>商品名称：{{ record.productName }}</div>
            <div class="text-gray">订单编号：
              <a @click="goToSegmentOrderDetail(record.orderId)" class="order-link">
                {{ record.orderNo }}
              </a>
            </div>
          </div>
        </template>

        <!-- 用户信息 -->
        <template v-if="column.dataIndex === 'userInfo'">
          <div class="user-info-cell">
            <div>{{ record.customerName }}</div>
            <div class="text-gray">{{ maskPhoneNumber(record.customerPhone) }}</div>
          </div>
        </template>

        <!-- 邀请码 -->
        <template v-if="column.dataIndex === 'invitationCode'">
          <div class="invitation-info-cell">
            <div>邀请码：{{ record.invitationCode || '-' }}</div>
            <div>员工：{{ record.employeeName || '-' }}</div>
            <div>部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>

        <!-- 订单总金额 -->
        <template v-if="column.dataIndex === 'orderAmount'">
          <span>{{ formatAmount(text) }}</span>
        </template>

        <!-- 期数 -->
        <template v-if="column.dataIndex === 'installmentNo'">
          {{ text !== null && text !== undefined ? text : '-' }}
        </template>

        <!-- 应扣金额 -->
        <template v-if="column.dataIndex === 'installmentAmount'">
          <span>{{ formatAmount(text) }}</span>
        </template>

        <!-- 已扣金额 -->
        <template v-if="column.dataIndex === 'deductAmount'">
          <span>{{ formatAmount(text) }}</span>
        </template>

        <!-- 是否逾期 -->
        <template v-if="column.dataIndex === 'overdueFlag'">
          <a-tag :color="text === 1 ? 'red' : 'green'">
            {{ text === 1 ? '是' : '否' }}
          </a-tag>
        </template>

        <!-- 应收日期 -->
        <template v-if="column.dataIndex === 'receivableDate'">
          <div>{{ text || '-' }}</div>
        </template>

        <!-- 扣款日期 -->
        <template v-if="column.dataIndex === 'deductTime'">
          <div>{{ text || '-' }}</div>
        </template>

        <!-- 扣款状态 -->
        <template v-if="column.dataIndex === 'deductStatus'">
          <a-tag :color="getDeductStatusColor(text)">
            {{ getDeductStatusDesc(text) }}
          </a-tag>
        </template>

        <!-- 订单代扣解除 -->
        <template v-if="column.dataIndex === 'deductUnbindFlag'">
          <a-tag :color="text === 0 ? 'blue' : 'red'">
            {{ text === 0 ? '否' : '是' }}
          </a-tag>
        </template>

        <!-- 当期代扣解除 -->
        <template v-if="column.dataIndex === 'removeDeductFlag'">
          <a-tag :color="text === 0 ? 'blue' : 'red'">
            {{ text === 0 ? '否' : '是' }}
          </a-tag>
        </template>

        <!-- 扣款失败信息 -->
        <template v-if="column.dataIndex === 'failureInfo'">
          <div class="failure-info-cell" v-if="record.deductFailReason">
            <div>{{ record.deductFailReason }}</div>
          </div>
          <span v-else>-</span>
        </template>

        <!-- 收款方式 -->
        <template v-if="column.dataIndex === 'receivePaymentType'">
          {{ getPaymentMethodDesc(text) }}
        </template>

        <!-- 下单时间 -->
        <template v-if="column.dataIndex === 'createTime'">
          <div>{{ text || '-' }}</div>
        </template>

        <!-- 扣款计划备注 -->
        <template v-if="column.dataIndex === 'deductRemark'">
          <span>{{ text || '-' }}</span>
        </template>

        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ text || '-' }}</span>
        </template>

      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <!-- 分段详情弹窗 -->
    <SegmentDetailModal 
      ref="detailModalRef" 
      @reload="queryData" 
    />
  </a-card>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { segmentDetailApi } from '/@/api/business/finance/segment-detail-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
  import { 
    WITHHOLD_RELEASE_ENUM,
    PAYMENT_METHOD_ENUM,
    DEDUCTION_STATUS_ENUM
  } from '/@/constants/business/finance/segment-detail-const';
  import { 
    SearchOutlined, 
    ReloadOutlined, 
    ExportOutlined
  } from '@ant-design/icons-vue';
  
  // 引入子组件
  import SegmentDetailModal from './components/segment-detail-modal.vue';
  import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';

  const router = useRouter();

  // 获取本月第一天到今天的默认日期范围
  function getDefaultDueDateRange() {
    const today = dayjs();
    const firstDayOfMonth = today.startOf('month');
    return [firstDayOfMonth, today];
  }

  // ----------------------- 以下是reactive的基础数据定义 ---------------------------
  const queryFormState = {
    orderNo: null, // 订单编号
    customerName: null, // 用户姓名
    customerPhone: null, // 用户手机号
    dueDateRange: getDefaultDueDateRange(), // 应收日期范围
    receivableDateStart: null, // 应收日期起
    receivableDateEnd: null, // 应收日期止
    productName: null, // 商品名称
    orderStatus: null, // 订单状态
    deductStatus: null, // 扣款状态
    overdueFlag: null, // 是否逾期:0否1是
    overdueDayStart: null, // 逾期天数起
    overdueDayEnd: null, // 逾期天数止
    receivePaymentType: null, // 收款方式
    deductUnbindFlag: null, // 订单代扣解除:0否1是
    removeDeductFlag: null, // 当期代扣解除:0否1是
    invitationCode: null, // 邀请码
    orderDateRange: null, // 下单日期范围
    createStartDate: null, // 下单开始时间
    createEndDate: null, // 下单结束时间
    deductionDateRange: null, // 扣款日期范围
    deductTimeStart: null, // 收款日期起
    deductTimeEnd: null, // 收款日期止
    qrCreateBy: null, // 商品创建人
    storeName: [], // 门店名称查询字段
    pageNum: 1,
    pageSize: 10,
    searchCount: true,
    sortItemList: []
  };
  const queryForm = reactive({ ...queryFormState });
  
  const tableData = ref([]);
  const total = ref(0);
  const tableLoading = ref(false);
  // 已删除批量选择功能

  // 统计信息
  const statistics = ref({
    deductCount: 0,
    totalInstallmentAmount: 0,
    totalDeductAmount: 0
  });

  // 弹窗引用
  const detailModalRef = ref();

  // ----------------------- 表格显示列 ---------------------------
  const columns = ref([
    {
      title: '订单信息',
      dataIndex: 'orderInfo',
      width: 300,
      resizable: true,
    },
    {
      title: '用户信息',
      dataIndex: 'userInfo',
      width: 200,
      resizable: true,
    },
    {
      title: '邀请码',
      dataIndex: 'invitationCode',
      width: 200,
      resizable: true,
    },
    {
      title: '订单总金额',
      dataIndex: 'orderAmount',
      width: 120,
      resizable: true,
    },
    {
      title: '期数',
      dataIndex: 'installmentNo',
      width: 80,
      resizable: true,
    },
    {
      title: '应扣金额',
      dataIndex: 'installmentAmount',
      width: 120,
      resizable: true,
    },
    {
      title: '已扣金额',
      dataIndex: 'deductAmount',
      width: 120,
      resizable: true,
    },
    {
      title: '是否逾期',
      dataIndex: 'overdueFlag',
      width: 100,
      resizable: true,
    },
    {
      title: '应收日期',
      dataIndex: 'receivableDate',
      width: 180,
      resizable: true,
    },
    {
      title: '扣款日期',
      dataIndex: 'deductTime',
      width: 180,
      resizable: true,
    },
    {
      title: '扣款状态',
      dataIndex: 'deductStatus',
      width: 100,
      resizable: true,
    },
    {
      title: '订单代扣解除',
      dataIndex: 'deductUnbindFlag',
      width: 130,
      resizable: true,
    },
    {
      title: '当期代扣解除',
      dataIndex: 'removeDeductFlag',
      width: 130,
      resizable: true,
    },
    {
      title: '扣款失败信息',
      dataIndex: 'failureInfo',
      width: 200,
      resizable: true,
    },
    {
      title: '收款方式',
      dataIndex: 'receivePaymentType',
      width: 120,
      resizable: true,
    },
    {
      title: '下单时间',
      dataIndex: 'createTime',
      width: 180,
      resizable: true,
    },
    {
      title: '扣款计划备注',
      dataIndex: 'deductRemark',
      width: 150,
      resizable: true,
    },
    {
      title: '门店名称',
      dataIndex: 'storeName',
      width: 120,
      resizable: true,
    },
  ]);

  // ----------------------- 生命周期函数 ---------------------------
  onMounted(() => {
    queryData();
    loadStatistics();
  });

  // ----------------------- 公共函数 ---------------------------
  
  // 跳转到分期订单详情
  const goToSegmentOrderDetail = (orderId) => {
    router.push({
      path: '/order/segment-order-detail',
      query: { id: orderId }
    });
  };
  
  // 只允许输入数字
  function onlyNumber(event) {
    // 允许数字、删除键、退格键
    const char = String.fromCharCode(event.which);
    if (!/[0-9]/.test(char) && event.which !== 8 && event.which !== 46) {
      event.preventDefault();
    }
  }

  /**
   * 处理查询参数 - 统一的参数处理逻辑
   */
  function prepareQueryParams() {
    const params = { ...queryForm };
    if (queryForm.dueDateRange && queryForm.dueDateRange.length === 2) {
      params.receivableDateStart = dayjs(queryForm.dueDateRange[0]).format('YYYY-MM-DD');
      params.receivableDateEnd = dayjs(queryForm.dueDateRange[1]).format('YYYY-MM-DD');
    }
    if (queryForm.orderDateRange && queryForm.orderDateRange.length === 2) {
      params.createStartDate = dayjs(queryForm.orderDateRange[0]).format('YYYY-MM-DD');
      params.createEndDate = dayjs(queryForm.orderDateRange[1]).format('YYYY-MM-DD');
    }
    if (queryForm.deductionDateRange && queryForm.deductionDateRange.length === 2) {
      params.deductTimeStart = dayjs(queryForm.deductionDateRange[0]).format('YYYY-MM-DD');
      params.deductTimeEnd = dayjs(queryForm.deductionDateRange[1]).format('YYYY-MM-DD');
    }
    delete params.dueDateRange;
    delete params.orderDateRange;
    delete params.deductionDateRange;
    
    // 处理门店名称查询参数（多选时逗号拼接门店ID）
    if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
      // 如果选择了门店，将ID数组用逗号拼接成字符串
      params.storeName = params.storeName.join(',');
    } else {
      // 如果没有选择门店，传 null
      params.storeName = null;
    }
    
    return params;
  }

  /**
   * 查询数据
   */
  async function queryData() {
    tableLoading.value = true;
    try {
      // 使用统一的参数处理
      const params = prepareQueryParams();

      let queryResult = await segmentDetailApi.queryPage(params);
      const { data } = queryResult;
      
      tableData.value = data.list;
      total.value = data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  /**
   * 加载统计信息
   */
  async function loadStatistics() {
    try {
      // 使用统一的参数处理，确保与查询接口传参一致
      const params = prepareQueryParams();
      let result = await segmentDetailApi.getStatistics(params);
      statistics.value = result.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  /**
   * 搜索
   */
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
    loadStatistics();
  }

  /**
   * 重置查询表单
   */
  function resetQuery() {
    Object.assign(queryForm, queryFormState);
    onSearch();
  }

  // 已删除批量操作功能

  /**
   * 导出
   */
  async function onExport() {
    message.info('导出功能开发中...');
  }

  // 已删除全量导出功能

  /**
   * 显示详情弹窗
   */
  function showDetailModal(record) {
    detailModalRef.value.show(record);
  }

  /**
   * 重新扣款
   */
  async function retryDeduction(record) {
    try {
      SmartLoading.show();
      await segmentDetailApi.retryPayment({ orderId: record.orderId });
      message.success('重新扣款请求已提交');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  /**
   * 手动扣款
   */
  async function manualDeduction(record) {
    try {
      SmartLoading.show();
      await segmentDetailApi.manualDeduction({ orderId: record.orderId });
      message.success('手动扣款成功');
      queryData();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  /**
   * 调整列宽
   */
  function handleResizeColumn(w, col) {
    col.width = w;
  }

  // ----------------------- 辅助函数 ---------------------------

  // 格式化金额，添加千分符，移除¥符号
  function formatAmount(amount) {
    if (!amount && amount !== 0) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  // 格式化统计数字（整数），添加千分位
  function formatStatisticsNumber(number) {
    if (!number && number !== 0) return '0';
    return Number(number).toLocaleString('zh-CN');
  }

  // 手机号脱敏处理
  function maskPhoneNumber(phone) {
    if (!phone) return '-';
    
    // 去除所有非数字字符
    const cleanPhone = phone.toString().replace(/\D/g, '');
    
    // 如果不是11位手机号，直接返回原值
    if (cleanPhone.length !== 11) {
      return phone;
    }
    
    // 格式化为 137****1999
    return `${cleanPhone.substring(0, 3)}****${cleanPhone.substring(7)}`;
  }

  /**
   * 获取代扣解除状态描述
   */
  function getWithholdReleaseDesc(value) {
    return WITHHOLD_RELEASE_ENUM[value]?.desc || value;
  }

  /**
   * 获取收款方式描述
   */
  function getPaymentMethodDesc(value) {
    const methodMap = {
      'BANK_SEGMENT': '账单付',
      'ZFB_ANXIN': '安心付'
    };
    return methodMap[value] || value || '未知';
  }

  /**
   * 获取扣款状态描述
   */
  function getDeductStatusDesc(value) {
    const statusMap = {
      'WAIT_AUTHORIZ': '待授权',
      'PENDING_DEDUCT': '待扣款',
      'PAID': '已扣款',
      'DEDUCT_FAIL': '扣款失败',
      'PAUSED': '已暂停',
      'TERMINATED': '已解约',
      'CANCELED': '已取消'
    };
    return statusMap[value] || '未知';
  }

  /**
   * 获取扣款状态颜色
   */
  function getDeductStatusColor(value) {
    const colorMap = {
      'WAIT_AUTHORIZ': 'default',
      'PENDING_DEDUCT': 'blue',
      'PAID': 'green',
      'DEDUCT_FAIL': 'red',
      'PAUSED': 'warning',
      'TERMINATED': 'red',
      'CANCELED': 'default'
    };
    return colorMap[value] || 'default';
  }

  /**
   * 获取扣款状态描述
   */
  function getDeductionStatusDesc(value) {
    return DEDUCTION_STATUS_ENUM[value]?.desc || value;
  }

  /**
   * 获取扣款状态颜色
   */
  function getDeductionStatusColor(value) {
    return DEDUCTION_STATUS_ENUM[value]?.color || 'default';
  }


</script>

<style scoped>
.segment-statistics {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.statistics-text {
  font-size: inherit;
  color: inherit;
  line-height: 1.5;
}

.order-info-cell {
  line-height: 1.6;
}

.user-info-cell {
  line-height: 1.6;
}

.failure-info-cell {
  line-height: 1.6;
}

.text-gray {
  color: inherit;
  font-size: inherit;
}

.smart-table-operate {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.smart-table-operate .ant-btn-link {
  padding: 0;
  height: auto;
}

.order-link {
  color: #1890ff;
  cursor: pointer;
}

.order-link:hover {
  color: #40a9ff;
}

.statistics-number {
  color: #1890ff;
}

.invitation-info-cell {
  line-height: 1.6;
}

/* 筛选区域卡片圆角样式 */
.smart-query-form {
  border-radius: 4px;
}
</style>
 