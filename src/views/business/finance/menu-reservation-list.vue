<!--
  * 预定菜单列表
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2024-01-01 00:00:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <!-- 订单号 -->
      <a-form-item label="订单号" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.orderNo" placeholder="请输入" />
      </a-form-item>
      <!-- 用户手机号 -->
      <a-form-item label="用户手机号" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.customerPhone" placeholder="请输入" />
      </a-form-item>
      <!-- 下单日期 -->
      <a-form-item label="下单日期" class="smart-query-form-item">
        <a-range-picker
          :value="[queryForm.createtimeStart, queryForm.createtimeEnd]"
          @update:value="(dates) => { queryForm.createtimeStart = dates?.[0] || null; queryForm.createtimeEnd = dates?.[1] || null; }"
          :placeholder="['开始日期', '结束日期']"
          style="width: 280px"
          format="YYYY/MM/DD"
          value-format="YYYY-MM-DD"
        />
      </a-form-item>
      <!-- 订单状态 -->
      <a-form-item label="订单状态" name="orderStatus" class="smart-query-form-item">
        <a-select v-model:value="queryForm.orderStatus" style="width: 120px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="PENDING_PAYMENT">待支付</a-select-option>
          <a-select-option value="PAID">已付款</a-select-option>
          <a-select-option value="CANCELED">已取消</a-select-option>
          <a-select-option value="REFUNDING">退款中</a-select-option>
          <a-select-option value="REFUND_SUCCESS">已退款</a-select-option>
          <a-select-option value="REFUND_FAIL">退款失败</a-select-option>
        </a-select>
      </a-form-item>
      <!-- 邀请码 -->
      <a-form-item label="邀请码" class="smart-query-form-item">
        <a-input style="width: 200px" v-model:value="queryForm.invitationCode" placeholder="请输入" @keypress="onlyNumber" />
      </a-form-item>
      <!-- 支付工具 -->
      <a-form-item label="支付工具" class="smart-query-form-item">
        <a-select v-model:value="queryForm.payTool" style="width: 120px" placeholder="全部">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="BAOFOO_BANK">宝付银行卡</a-select-option>
          <a-select-option value="OFFLINE_PAYMENT">线下收款</a-select-option>
          <a-select-option value="ZHIFUBAO">支付宝</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 门店名称 -->
      <a-form-item label="门店名称" class="smart-query-form-item">
        <StoreMultiSelect 
          v-model="queryForm.storeName" 
          width="200px"
          placeholder="请选择门店"
        />
      </a-form-item>
      <!-- 查询/重置按钮 -->
      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 统计信息 begin ----------->
    <div class="order-statistics">
      <div class="statistics-text">
        订单数：<strong class="statistics-number">{{ formatAmount(statistics.totalCount) }}</strong>
        <a-divider type="vertical" />
        订单总金额：<strong class="statistics-number">{{ formatAmount(statistics.totalAmount) }}</strong>
      </div>
    </div>
    <!---------- 统计信息 end ----------->

    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button v-privilege="'business:complaint-list:showMerchantQrCode'" @click="showMerchantQrCode" class="qr-code-btn">
          <template #icon>
            <QrcodeOutlined />
          </template>
          商家独立小程序收款码
        </a-button>
        <a-button v-privilege="'business:complaint-list:showHappyPayQrCode'" @click="showHappyPayQrCode" class="qr-code-btn">
          <template #icon>
            <QrcodeOutlined />
          </template>
          欢乐付小程序收款码
        </a-button>
        <a-button v-privilege="'business:complaint-list:export'" @click="onExportOrders">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator v-model="columns" :tableId="TABLE_ID_CONST.BUSINESS.ORDER.MENU_RESERVATION" :refresh="queryData" />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="orderId"
      :scroll="{ x: 2000, y: 2000 }"
      bordered
      :pagination="false"
      :showSorterTooltip="false"
      @change="onChange"
      @resizeColumn="handleResizeColumn"
    >

      <template #bodyCell="{ text, record, column }">
        <template v-if="column.dataIndex === 'orderInfo'">
          <div class="order-info-cell">
            <div>商品名称：{{ record.productName || '-' }}</div>
            <div class="text-gray">订单编号：
              <span class="order-link">
                {{ record.orderNo || '-' }}
              </span>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'receiptType'">
          <a-tag :color="getPaymentTypeColor(text)">
            {{ getPaymentTypeDesc(text) }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'orderStatus'">
          <a-tag :color="getOrderStatusColor(text)">
            {{ getOrderStatusDesc(text) }}
          </a-tag>
        </template>
        <template v-if="column.dataIndex === 'userInfo'">
          <div class="user-info-cell">
            <div>{{ maskPhone(record.customerPhone) }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'invitationCode'">
          <div class="invitation-info-cell">
            <div>邀请码：{{ record.invitationCode || '-' }}</div>
            <div>员工：{{ record.employeeName || '-' }}</div>
            <div>部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'installmentAmount'">
          {{ formatMoney(text) }}
        </template>
        <template v-if="column.dataIndex === 'penaltyFee'">
          {{ record.receiptType === 'TERMINATION_PAYMENT_CODE' ? formatMoney(text) : '-' }}
        </template>
        <template v-if="column.dataIndex === 'sessionFee'">
          {{ record.receiptType === 'TERMINATION_PAYMENT_CODE' ? formatMoney(text) : '-' }}
        </template>
        <template v-if="column.dataIndex === 'payChannel'">
          <span>{{ getPayChannelDesc(record.paymentChannels) }}</span>
        </template>
        <template v-if="column.dataIndex === 'payTool'">
          <span>{{ getPayToolDesc(record.paymentMethod) }}</span>
        </template>
        <template v-if="column.dataIndex === 'transactionNo'">
          <span>{{ record.deductSerialNo || '-' }}</span>
        </template>
        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ text || '-' }}</span>
        </template>

        <template v-if="column.dataIndex === 'refundReason'">
          <span>{{ text || '-' }}</span>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <div class="smart-table-operate operation-buttons">
            <!-- <a-button @click="openAssociateOrder(record)" type="link">关联订单</a-button> -->
            <a-button v-privilege="'business:complaint-list:refund'" v-if="record.receiptType !== 'TERMINATION_PAYMENT_CODE' && (record.orderStatus === 'PAID' || record.orderStatus === 'REFUND_FAIL')" @click="openRefund(record)" type="link">退款</a-button>
          </div>
        </template>
      </template>
    </a-table>
    <!-- 弹窗区 -->
    <AssociateOrderModal
      v-model:visible="associateOrderModalVisible"
      :defaultOrderNo="currentOrder?.orderNo"
      @confirm="onAssociateOrderConfirm"
      @cancel="onAssociateOrderCancel"
    />
    <RefundModal
      v-model:visible="refundModalVisible"
      @confirm="onRefundConfirm"
      @cancel="onRefundCancel"
    />
    
    <!-- 二维码显示弹窗 -->
    <QrCodeModal 
      ref="qrCodeModalRef" 
    />

    <div class="smart-query-table-page">
      <a-pagination·
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>
  </a-card>
</template>

<script setup>
  import { onMounted, reactive, ref } from 'vue';
  import { message, Modal } from 'ant-design-vue';
  import { useRouter } from 'vue-router';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { menuReservationApi } from '/@/api/business/order/menu-reservation-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
  
  import { SearchOutlined, ReloadOutlined, ExportOutlined, QrcodeOutlined } from '@ant-design/icons-vue';
  import { dataFormatter } from '/@/utils/data-formatter';
  import _ from 'lodash';
  import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';
  import AssociateOrderModal from './components/associate-order-modal.vue';
  import RefundModal from './components/refund-modal.vue';
  import QrCodeModal from './components/qr-code-modal.vue';

  // ---------------------------- 路由实例 ----------------------------
  const router = useRouter();

  // ---------------------------- 表格列 ----------------------------

  const columns = ref([
    {
      title: '创建时间',
      dataIndex: 'createtime',
      resizable: true,
      width: 170,
    },
    {
      title: '商品信息',
      dataIndex: 'orderInfo',
      width: 350,
      resizable: true,
    },
    {
      title: '收款类型',
      dataIndex: 'receiptType',
      resizable: true,
      width: 120,
    },
    {
      title: '状态',
      dataIndex: 'orderStatus',
      resizable: true,
      width: 100,
    },
    {
      title: '用户信息',
      dataIndex: 'userInfo',
      resizable: true,
      width: 150,
    },
    {
      title: '邀请码',
      dataIndex: 'invitationCode',
      resizable: true,
      width: 200,
    },
    {
      title: '订单总金额',
      dataIndex: 'installmentAmount',
      resizable: true,
      width: 120,
    },
    {
      title: '违约金',
      dataIndex: 'penaltyFee',
      resizable: true,
      width: 120,
    },
    {
      title: '课时费',
      dataIndex: 'sessionFee',
      resizable: true,
      width: 120,
    },
    {
      title: '支付渠道',
      dataIndex: 'payChannel',
      resizable: true,
      width: 120,
    },
    {
      title: '支付工具',
      dataIndex: 'payTool',
      resizable: true,
      width: 120,
    },
    {
      title: '流水号',
      dataIndex: 'transactionNo',
      resizable: true,
      width: 220,
    },
    {
      title: '门店名称',
      dataIndex: 'storeName',
      resizable: true,
      width: 150,
    },

    {
      title: '退款原因',
      dataIndex: 'refundReason',
      resizable: true,
      width: 200,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 150,
      fixed: 'right',
      resizable: true,
    },
  ]);

  // ---------------------------- 查询数据表单和方法 ----------------------------

  // 获取当月1号和今天的日期
  const today = new Date();
  const currentMonth = today.getMonth();
  const currentYear = today.getFullYear();
  const firstDayOfMonth = new Date(currentYear, currentMonth, 1);
  
  const formatDate = (date) => {
    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  };

  const queryFormState = {
    // 基础分页参数
    pageNum: 1,                           // 页码
    pageSize: 10,                         // 每页数量
    searchCount: true,                    // 是否查询总条数
    sortItemList: [],                     // 排序字段集合
    
    // 订单基本信息
    orderNo: null,                        // 订单编号
    customerPhone: null,                  // 用户手机号
    orderStatus: null,                    // 订单状态
    invitationCode: null,                 // 邀请码
    payTool: null,                        // 支付工具
    
    // 时间范围查询
    createtimeStart: formatDate(firstDayOfMonth),  // 下单开始时间
    createtimeEnd: formatDate(today),              // 下单结束时间
    
    // 其他查询条件
    storeName: [],                        // 门店名称查询字段
  };

  // 查询表单form
  const queryForm = reactive(_.cloneDeep(queryFormState));
  // 表格加载loading
  const tableLoading = ref(false);
  // 表格数据
  const tableData = ref([]);
  // 总数
  const total = ref(0);
  // 统计数据
  const statistics = ref({
    totalCount: 0,
    totalAmount: 0,
  });

  function handleResizeColumn(w, col) {
    columns.value.forEach((item) => {
      if (item.dataIndex === col.dataIndex) {
        item.width = Math.floor(w);
        // 拖动宽度标识
        item.dragAndDropFlag = true;
      }
    });
  }

  // 重置查询条件
  function resetQuery() {
    let pageSize = queryForm.pageSize;
    Object.assign(queryForm, _.cloneDeep(queryFormState));
    queryForm.pageSize = pageSize;
    queryData();
  }

  // 搜索
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
  }

  // 查询数据
  async function queryData() {
    tableLoading.value = true;
    try {
      // 使用统一的参数处理函数
      const params = prepareQueryParams();

      // 并行调用两个接口：列表数据和统计数据
      const [listResult, statisticsResult] = await Promise.all([
        menuReservationApi.queryMenuReservationList(params),     // 调用 /order/reserve/queryPage
        menuReservationApi.queryMenuReservationStatistics(params) // 调用 /order/reserve/queryPageTotal
      ]);

      // 处理列表数据
      tableData.value = listResult.data.list || [];
      total.value = listResult.data.total || 0;
      
      // 处理统计数据
      statistics.value = {
        totalCount: statisticsResult.data.orderCount || 0,
        totalAmount: parseFloat(statisticsResult.data.totalOrderAmount || '0'),
      };
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  onMounted(queryData);

  // ---------------------------- 参数处理方法 ----------------------------
  
  /**
   * 统一处理查询参数 - 确保所有下拉框选择"全部"时传null
   */
  function prepareQueryParams() {
    const params = { ...queryForm };
    
    // 字符串类型参数 - 空值转换为null
    if (params.orderStatus === null || params.orderStatus === undefined || params.orderStatus === '') {
      params.orderStatus = null;
    }
    if (params.payTool === null || params.payTool === undefined || params.payTool === '') {
      params.payTool = null;
    }
    // 处理门店名称查询参数（多选时逗号拼接门店ID）
    if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
      params.storeName = params.storeName.join(',');
    } else {
      params.storeName = null;
    }
    return params;
  }

  // ---------------------------- 操作方法 ----------------------------

  // 跳转到订单详情
  const goToOrderDetail = (orderId) => {
    router.push({
      path: '/order/menu-reservation-detail',
      query: { id: orderId }
    });
  };

  // 查看订单详情
  function viewOrderDetail(record) {
    router.push({
      path: '/order/menu-reservation-detail',
      query: { id: record.orderId },
    });
  }

  // 导出订单
  async function onExportOrders() {
    message.info('导出功能开发中...');
  }

  function onChange(pagination, filters, sorter, { action }) {
    if (action === 'sort') {
      const { order, field } = sorter;
      let column = camelToUnderscore(field);
      let findIndex = queryForm.sortItemList.findIndex((e) => e.column === column);
      if (findIndex !== -1) {
        queryForm.sortItemList.splice(findIndex, 1);
      }
      if (order) {
        let isAsc = order !== 'ascend';
        queryForm.sortItemList.push({
          column,
          isAsc,
        });
      }
      queryData();
    }
  }

  function camelToUnderscore(str) {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase();
  }

  // 动态设置表格高度
  const yHeight = ref(0);
  onMounted(() => {
    resetGetHeight();
  });

  function resetGetHeight() {
    // 搜索部分高度
    let doc = document.querySelector('.ant-form');
    // 按钮部分高度
    let btn = document.querySelector('.smart-table-btn-block');
    // 表格头高度
    let tableCell = document.querySelector('.ant-table-cell');
    // 分页高度
    let page = document.querySelector('.smart-query-table-page');
    // 内容区总高度
    let box = document.querySelector('.admin-content');
    setTimeout(() => {
      let dueHeight = doc.offsetHeight + 10 + 24 + btn.offsetHeight + 15 + tableCell.offsetHeight + page.offsetHeight + 20 + 50; // 加50给统计信息
      yHeight.value = box.offsetHeight - dueHeight;
    }, 100);
  }

  window.addEventListener(
    'resize',
    _.throttle(() => {
      resetGetHeight();
    }, 1000)
  );

  // 只允许输入数字
  function onlyNumber(event) {
    // 允许数字、删除键、退格键
    const char = String.fromCharCode(event.which);
    if (!/[0-9]/.test(char) && event.which !== 8 && event.which !== 46) {
      event.preventDefault();
    }
  }

  function formatMoney(value) {
    return dataFormatter.formatMoney(value);
  }

  // 格式化统计数字，添加千分位
  function formatAmount(amount) {
    if (!amount && amount !== 0) return '0';
    
    // 如果是整数（如订单数），不显示小数位
    if (Number.isInteger(Number(amount))) {
      return Number(amount).toLocaleString('zh-CN');
    }
    
    // 如果是小数（如金额），显示两位小数
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  function maskPhone(phone) {
    if (!phone) return '';
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  // 枚举转换函数
  function getOrderStatusDesc(value) {
    const enumMap = {
      'PENDING_PAYMENT': '待支付',
      'PAID': '已付款',
      'CANCELED': '已取消',
      'REFUNDING': '退款中',
      'REFUND_SUCCESS': '已退款',
      'REFUND_FAIL': '退款失败'
    };
    return enumMap[value] || value || '-';
  }

  function getPayChannelDesc(value) {
    const enumMap = {
      'DEDUCT': '代扣',
      'OFFLINE_PAYMENT': '线下收款',
      'SELF_PAYMENT': '主动支付'
    };
    return enumMap[value] || value || '-';
  }

  function getPayToolDesc(value) {
    const enumMap = {
      'BAOFOO_BANK': '宝付银行卡',
      'OFFLINE_PAYMENT': '线下收款',
      'ZHIFUBAO': '支付宝'
    };
    return enumMap[value] || value || '-';
  }

  // 获取订单状态颜色
  function getOrderStatusColor(status) {
    const colorMap = {
      'PENDING_PAYMENT': 'default',  // 待支付 - 灰色
      'PAID': 'green',               // 已付款 - 绿色
      'CANCELED': 'default',         // 已取消 - 灰色
      'REFUNDING': 'processing',     // 退款中 - 蓝色
      'REFUND_SUCCESS': 'orange',    // 已退款 - 橙色
      'REFUND_FAIL': 'error',        // 退款失败 - 红色
    };
    return colorMap[status] || 'default';
  }



  // 收款类型枚举转换函数
  function getPaymentTypeDesc(value) {
    const enumMap = {
      'PAYMENT_CODE': '收款码',
      'TERMINATION_PAYMENT_CODE': '解约收款码'
    };
    return enumMap[value] || value || '-';
  }

  // 获取收款类型颜色
  function getPaymentTypeColor(type) {
    const colorMap = {
      'PAYMENT_CODE': 'green',               // 收款码 - 绿色
      'TERMINATION_PAYMENT_CODE': 'orange',  // 解约收款码 - 橙色
    };
    return colorMap[type] || 'default';
  }

  // 弹窗引用
  const associateOrderModalVisible = ref(false);
  const refundModalVisible = ref(false);
  const currentOrder = ref(null);
  const qrCodeModalRef = ref();

  function openAssociateOrder(record) {
    currentOrder.value = record;
    associateOrderModalVisible.value = true;
  }
  function openRefund(record) {
    currentOrder.value = record;
    refundModalVisible.value = true;
  }
  async function onAssociateOrderConfirm(orderInfo) {
    try {
      SmartLoading.show();
      // 调用关联订单接口
      const response = await menuReservationApi.associateOrder({
        orderId: currentOrder.value.orderId,
        associateOrderNo: orderInfo.orderNo,
        remark: orderInfo.remark
      });
      
      if (response.data) {
        message.success('关联订单成功');
        associateOrderModalVisible.value = false;
        // 刷新列表数据
        queryData();
      }
    } catch (e) {
      smartSentry.captureError(e);
      message.error('关联订单失败');
    } finally {
      SmartLoading.hide();
    }
  }
  function onAssociateOrderCancel() {
    associateOrderModalVisible.value = false;
  }
  async function onRefundConfirm(refundData) {
    try {
      SmartLoading.show();
      // 调用退款接口
      const response = await menuReservationApi.refund({
        orderId: currentOrder.value.orderId,
        refundReason: refundData
      });
      
      if (response.code === 0 && response.ok) {
        message.success('退款成功');
        refundModalVisible.value = false;
        // 刷新列表数据
        queryData();
      } else {
        message.error(response.msg || '退款失败');
      }
    } catch (e) {
      smartSentry.captureError(e);
      if (e.response && e.response.data) {
        message.error(e.response.data.msg || '退款失败');
      } else {
        message.error('退款失败');
      }
    } finally {
      SmartLoading.hide();
    }
  }
  function onRefundCancel() {
    refundModalVisible.value = false;
  }

  // 商家独立小程序收款码
  function showMerchantQrCode() {
    // 构造商家独立小程序收款码数据
    const merchantQrData = {
      type: 'MERCHANT_MINI_PROGRAM',
      title: '商家独立小程序收款码',
      // 这里可以添加其他需要的数据
    };
    qrCodeModalRef.value.show(merchantQrData);
  }

  // 欢乐付小程序收款码
  function showHappyPayQrCode() {
    // 构造欢乐付小程序收款码数据
    const happyPayQrData = {
      type: 'HAPPY_PAY_MINI_PROGRAM',
      title: '欢乐付小程序收款码',
      // 这里可以添加其他需要的数据
    };
    qrCodeModalRef.value.show(happyPayQrData);
  }
</script> 

<style scoped>
.order-info-cell {
  line-height: 1.6;
}

.user-info-cell {
  line-height: 1.6;
}

.text-gray {
  color: inherit;
  font-size: inherit;
}

.order-statistics {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.statistics-text {
  font-size: inherit;
  color: inherit;
  line-height: 1.5;
}

.operation-buttons {
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  white-space: nowrap;
  align-items: center;
  gap: 8px;
  line-height: 1.2;
}

.order-link {
  color: #1890ff;
  cursor: pointer;
}

.order-link:hover {
  color: #40a9ff;
}

.statistics-number {
  color: #1890ff;
}

.invitation-info-cell {
  line-height: 1.6;
}

/* 筛选区域卡片圆角样式 */
.smart-query-form {
  border-radius: 4px;
}

/* 二维码按钮样式 */
.qr-code-btn {
  margin-right: 8px;
}

.qr-code-btn .anticon {
  color: inherit;
}
</style> 