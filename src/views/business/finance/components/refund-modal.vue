<template>
  <a-modal
    v-model:visible="visible"
    :title="'退款'"
    width="900px"
    :footer="null"
    @cancel="handleCancel"
    :maskClosable="false"
    class="refund-modal"
  >
    <a-form layout="vertical" class="refund-form">
      <a-form-item
        label="退款原因"
        :required="true"
        :validateStatus="reasonError ? 'error' : ''"
        :help="reasonError ? '请输入退款原因' : ''"
      >
        <a-textarea
          v-model:value="refundReason"
          :maxlength="100"
          :rows="5"
          placeholder="请输入"
          show-count
        />
      </a-form-item>
      <div class="modal-footer-btns">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk" :loading="confirming">确定</a-button>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue';
import { message } from 'ant-design-vue';

const props = defineProps({
  visible: Boolean,
});
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

const visible = ref(props.visible);
watch(() => props.visible, v => (visible.value = v));
watch(visible, v => emit('update:visible', v));

const refundReason = ref('');
const reasonError = ref(false);
const confirming = ref(false);

function handleOk() {
  if (!refundReason.value.trim()) {
    reasonError.value = true;
    return;
  }
  reasonError.value = false;
  confirming.value = true;
  setTimeout(() => {
    confirming.value = false;
    emit('confirm', refundReason.value.trim());
    visible.value = false;
  }, 600);
}

function handleCancel() {
  emit('cancel');
  visible.value = false;
}
</script>

<style scoped>
.refund-form {
  margin-top: 0;
}
.modal-footer-btns {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
}
</style> 