<!--
  * 批量操作分段明细弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 16:00:00
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    :title="getModalTitle()"
    v-model:open="visible"
    :width="800"
    @ok="handleSubmit"
    @cancel="handleClose"
    :confirmLoading="loading"
    okText="确认操作"
    cancelText="取消"
  >
    <a-form
      :model="formData"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="操作类型" name="operationType">
        <a-tag :color="getOperationTypeColor()">{{ getOperationTypeDesc() }}</a-tag>
      </a-form-item>

      <!-- 批量扣款特有字段 -->
      <template v-if="operationType === 'BATCH_DEDUCTION'">
        <a-form-item label="扣款方式" name="deductionMethod" :rules="[{ required: true, message: '请选择扣款方式' }]">
          <a-radio-group v-model:value="formData.deductionMethod">
            <a-radio value="AUTO">自动扣款</a-radio>
            <a-radio value="MANUAL">手动扣款</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item label="扣款时间" name="deductionTime" :rules="[{ required: true, message: '请选择扣款时间' }]">
          <a-radio-group v-model:value="formData.deductionTime">
            <a-radio value="IMMEDIATE">立即扣款</a-radio>
            <a-radio value="SCHEDULED">定时扣款</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item 
          v-if="formData.deductionTime === 'SCHEDULED'" 
          label="预定时间" 
          name="scheduledTime"
          :rules="[{ required: true, message: '请选择预定扣款时间' }]"
        >
          <a-date-picker
            v-model:value="formData.scheduledTime"
            showTime
            placeholder="请选择预定扣款时间"
            style="width: 100%"
            :disabled-date="disabledDate"
          />
        </a-form-item>
      </template>

      <!-- 批量重试特有字段 -->
      <template v-if="operationType === 'BATCH_RETRY'">
        <a-form-item label="重试策略" name="retryStrategy" :rules="[{ required: true, message: '请选择重试策略' }]">
          <a-select v-model:value="formData.retryStrategy" placeholder="请选择重试策略">
            <a-select-option value="IMMEDIATE">立即重试</a-select-option>
            <a-select-option value="DELAY_5MIN">延迟5分钟重试</a-select-option>
            <a-select-option value="DELAY_1HOUR">延迟1小时重试</a-select-option>
            <a-select-option value="DELAY_1DAY">延迟1天重试</a-select-option>
          </a-select>
        </a-form-item>

        <a-form-item label="最大重试次数" name="maxRetryCount" :rules="[{ required: true, message: '请输入最大重试次数' }]">
          <a-input-number
            v-model:value="formData.maxRetryCount"
            :min="1"
            :max="10"
            placeholder="请输入最大重试次数"
            style="width: 100%"
          />
        </a-form-item>

        <a-form-item label="重试条件" name="retryCondition">
          <a-checkbox-group v-model:value="formData.retryCondition">
            <a-checkbox value="BALANCE_INSUFFICIENT">余额不足</a-checkbox>
            <a-checkbox value="NETWORK_ERROR">网络异常</a-checkbox>
            <a-checkbox value="SYSTEM_ERROR">系统错误</a-checkbox>
            <a-checkbox value="LIMIT_EXCEEDED">超过限额</a-checkbox>
          </a-checkbox-group>
        </a-form-item>
      </template>

      <a-form-item label="操作原因" name="reason" :rules="[{ required: true, message: '请输入操作原因' }]">
        <a-textarea
          v-model:value="formData.reason"
          :rows="3"
          placeholder="请输入操作原因"
          :maxlength="200"
          showCount
        />
      </a-form-item>

      <a-form-item label="备注说明" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          :rows="2"
          placeholder="请输入备注说明"
          :maxlength="500"
          showCount
        />
      </a-form-item>

      <!-- 风险提示 -->
      <a-form-item v-if="showRiskWarning()">
        <a-alert
          :message="getRiskWarningMessage()"
          type="warning"
          show-icon
          style="margin-bottom: 16px"
        />
      </a-form-item>
    </a-form>

    <!-- 选中的订单信息 -->
    <div class="selected-orders-info">
      <a-divider>选中的分段信息（共{{ selectedRowKeys.length }}条）</a-divider>
      <div class="orders-summary">
        <a-space size="large">
          <span>总应扣金额：<strong style="color: #fa8c16;">¥{{ totalShouldDeductAmount }}</strong></span>
          <span>总已扣金额：<strong style="color: #52c41a;">¥{{ totalDeductedAmount }}</strong></span>
          <span>待扣金额：<strong style="color: #ff4d4f;">¥{{ totalPendingAmount }}</strong></span>
        </a-space>
      </div>
    </div>

    <!-- 操作结果预览 -->
    <div v-if="showPreview" class="operation-preview">
      <a-divider>操作结果预览</a-divider>
      <a-alert
        :message="previewMessage"
        :type="previewType"
        show-icon
        style="margin-bottom: 16px"
      />
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { segmentDetailApi } from '/@/api/business/finance/segment-detail-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import dayjs from 'dayjs';

  // ----------------------- 事件 ---------------------------
  const emit = defineEmits(['reload']);

  // ----------------------- 数据定义 ---------------------------
  const visible = ref(false);
  const loading = ref(false);
  const selectedRowKeys = ref([]);
  const operationType = ref('');
  
  const formData = reactive({
    deductionMethod: '',
    deductionTime: '',
    scheduledTime: null,
    retryStrategy: '',
    maxRetryCount: 3,
    retryCondition: [],
    reason: '',
    remark: ''
  });

  // 选中订单的统计信息
  const totalShouldDeductAmount = ref(0);
  const totalDeductedAmount = ref(0);
  const totalPendingAmount = ref(0);

  // 预览相关
  const showPreview = ref(false);
  const previewMessage = ref('');
  const previewType = ref('info');

  // ----------------------- 监听器 ---------------------------
  
  // 监听表单变化，生成预览信息
  watch(
    () => [formData.deductionMethod, formData.deductionTime, formData.retryStrategy, formData.maxRetryCount],
    () => {
      generatePreview();
    },
    { deep: true }
  );

  // ----------------------- 公共函数 ---------------------------

  /**
   * 显示弹窗
   */
  function show(rowKeys, type) {
    selectedRowKeys.value = rowKeys;
    operationType.value = type;
    visible.value = true;
    resetForm();
    calculateStatistics();
  }

  /**
   * 重置表单
   */
  function resetForm() {
    Object.assign(formData, {
      deductionMethod: '',
      deductionTime: '',
      scheduledTime: null,
      retryStrategy: '',
      maxRetryCount: 3,
      retryCondition: [],
      reason: '',
      remark: ''
    });
    showPreview.value = false;
  }

  /**
   * 计算统计信息
   */
  function calculateStatistics() {
    // 这里应该根据selectedRowKeys获取对应的分段数据
    // 暂时使用模拟数据
    const avgAmount = 1000;
    totalShouldDeductAmount.value = (selectedRowKeys.value.length * avgAmount).toFixed(2);
    totalDeductedAmount.value = (selectedRowKeys.value.length * avgAmount * 0.6).toFixed(2);
    totalPendingAmount.value = (selectedRowKeys.value.length * avgAmount * 0.4).toFixed(2);
  }

  /**
   * 获取弹窗标题
   */
  function getModalTitle() {
    const titleMap = {
      'BATCH_DEDUCTION': '批量扣款',
      'BATCH_RETRY': '批量重试'
    };
    return titleMap[operationType.value] || '批量操作';
  }

  /**
   * 获取操作类型描述
   */
  function getOperationTypeDesc() {
    const descMap = {
      'BATCH_DEDUCTION': '批量扣款',
      'BATCH_RETRY': '批量重试'
    };
    return descMap[operationType.value] || '未知操作';
  }

  /**
   * 获取操作类型颜色
   */
  function getOperationTypeColor() {
    const colorMap = {
      'BATCH_DEDUCTION': 'green',
      'BATCH_RETRY': 'orange'
    };
    return colorMap[operationType.value] || 'default';
  }

  /**
   * 是否显示风险提示
   */
  function showRiskWarning() {
    if (operationType.value === 'BATCH_DEDUCTION' && selectedRowKeys.value.length > 50) {
      return true;
    }
    if (operationType.value === 'BATCH_RETRY' && formData.maxRetryCount > 5) {
      return true;
    }
    return false;
  }

  /**
   * 获取风险提示信息
   */
  function getRiskWarningMessage() {
    if (operationType.value === 'BATCH_DEDUCTION' && selectedRowKeys.value.length > 50) {
      return `您正在对${selectedRowKeys.value.length}条记录进行批量扣款，请确认操作的准确性`;
    }
    if (operationType.value === 'BATCH_RETRY' && formData.maxRetryCount > 5) {
      return '重试次数较多可能会对用户造成困扰，请谨慎设置';
    }
    return '';
  }

  /**
   * 生成预览信息
   */
  function generatePreview() {
    if (!operationType.value) {
      showPreview.value = false;
      return;
    }

    showPreview.value = true;
    
    if (operationType.value === 'BATCH_DEDUCTION') {
      if (formData.deductionMethod && formData.deductionTime) {
        const methodDesc = formData.deductionMethod === 'AUTO' ? '自动' : '手动';
        const timeDesc = formData.deductionTime === 'IMMEDIATE' ? '立即' : '定时';
        previewMessage.value = `将通过${methodDesc}方式${timeDesc}对${selectedRowKeys.value.length}条分段进行扣款，预计扣款金额：¥${totalPendingAmount.value}`;
        previewType.value = 'info';
      }
    } else if (operationType.value === 'BATCH_RETRY') {
      if (formData.retryStrategy && formData.maxRetryCount) {
        const strategyMap = {
          'IMMEDIATE': '立即',
          'DELAY_5MIN': '延迟5分钟',
          'DELAY_1HOUR': '延迟1小时',
          'DELAY_1DAY': '延迟1天'
        };
        const strategyDesc = strategyMap[formData.retryStrategy] || formData.retryStrategy;
        previewMessage.value = `将${strategyDesc}重试${selectedRowKeys.value.length}条失败的分段扣款，最大重试${formData.maxRetryCount}次`;
        previewType.value = 'warning';
      }
    }
  }

  /**
   * 禁用日期
   */
  function disabledDate(current) {
    // 不能选择过去的日期
    return current && current < dayjs().startOf('day');
  }

  /**
   * 提交操作
   */
  async function handleSubmit() {
    try {
      // 验证表单
      if (!formData.reason) {
        message.error('请输入操作原因');
        return;
      }

      // 特殊验证
      if (operationType.value === 'BATCH_DEDUCTION') {
        if (!formData.deductionMethod) {
          message.error('请选择扣款方式');
          return;
        }
        if (!formData.deductionTime) {
          message.error('请选择扣款时间');
          return;
        }
        if (formData.deductionTime === 'SCHEDULED' && !formData.scheduledTime) {
          message.error('请选择预定扣款时间');
          return;
        }
      }

      if (operationType.value === 'BATCH_RETRY') {
        if (!formData.retryStrategy) {
          message.error('请选择重试策略');
          return;
        }
        if (!formData.maxRetryCount) {
          message.error('请输入最大重试次数');
          return;
        }
      }

      loading.value = true;

      const params = {
        segmentIds: selectedRowKeys.value,
        operationType: operationType.value,
        ...formData,
        scheduledTime: formData.scheduledTime ? formData.scheduledTime.format('YYYY-MM-DD HH:mm:ss') : null
      };

      // 生产环境调用真实API
      // await segmentDetailApi.batchOperation(params);
      
      // 开发环境模拟请求
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success('批量操作成功');
      handleClose();
      emit('reload');
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 关闭弹窗
   */
  function handleClose() {
    visible.value = false;
    resetForm();
  }

  // ----------------------- 暴露给父组件的方法 ---------------------------
  defineExpose({
    show
  });
</script>

<style scoped>
.selected-orders-info {
  margin-top: 16px;
}

.orders-summary {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.operation-preview {
  margin-top: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.ant-checkbox-group .ant-checkbox-wrapper) {
  margin-right: 0;
}

:deep(.ant-radio-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}
</style> 