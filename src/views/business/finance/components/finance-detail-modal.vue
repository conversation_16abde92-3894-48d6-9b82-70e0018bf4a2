<!--
  * 财务明细详情弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-01-01
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    title="财务明细详情"
    :width="800"
    :footer="null"
    @cancel="handleCancel"
  >
    <a-spin :spinning="loading">
      <div v-if="detailData">
        <!-- 基本信息 -->
        <a-descriptions title="基本信息" :column="2" bordered>
          <a-descriptions-item label="订单编号">
            {{ detailData.orderNo || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="商品名称">
            {{ detailData.productName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="用户姓名">
            {{ detailData.userName || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="用户手机号">
            {{ detailData.userPhone || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="邀请码">
            {{ detailData.inviteCode || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="流水号" :span="2">
            <span style="font-family: monospace;">{{ detailData.transactionNo || '-' }}</span>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 金额信息 -->
        <a-descriptions title="金额信息" :column="2" bordered style="margin-top: 24px">
          <a-descriptions-item label="订单总金额">
            <span style="color: #1890ff; font-weight: 500;">
              ¥{{ detailData.totalAmount ? detailData.totalAmount.toFixed(2) : '0.00' }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="期数">
            {{ detailData.installmentNumber || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="应收金额">
            <span style="color: #52c41a; font-weight: 500;">
              ¥{{ detailData.dueAmount ? detailData.dueAmount.toFixed(2) : '0.00' }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="实收金额">
            <span style="color: #fa8c16; font-weight: 500;">
              ¥{{ detailData.actualAmount ? detailData.actualAmount.toFixed(2) : '0.00' }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="优惠金额">
            <span style="color: #f5222d;">
              ¥{{ detailData.discountAmount ? detailData.discountAmount.toFixed(2) : '0.00' }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="平台服务费">
            <span style="color: #722ed1;">
              {{ detailData.platformServiceFee ? detailData.platformServiceFee.toFixed(2) : '0.00' }}
            </span>
          </a-descriptions-item>
          <a-descriptions-item label="交易手续费（预估）">
            <span style="color: #eb2f96;">
              {{ detailData.transactionFee ? detailData.transactionFee.toFixed(2) : '0.00' }}
            </span>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 支付信息 -->
        <a-descriptions title="支付信息" :column="2" bordered style="margin-top: 24px">
          <a-descriptions-item label="收款方式">
            {{ getPaymentMethodText(detailData.paymentMethod) }}
          </a-descriptions-item>
          <a-descriptions-item label="支付渠道">
            {{ getPaymentChannelText(detailData.paymentChannel) }}
          </a-descriptions-item>
          <a-descriptions-item label="扣款状态">
            <a-tag :color="getDeductionStatusColor(detailData.deductionStatus)">
              {{ getDeductionStatusText(detailData.deductionStatus) }}
            </a-tag>
          </a-descriptions-item>
          <a-descriptions-item label="平台入账方式">
            {{ getPlatformAccountMethodText(detailData.platformAccountMethod) }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 时间信息 -->
        <a-descriptions title="时间信息" :column="2" bordered style="margin-top: 24px">
          <a-descriptions-item label="应收日期">
            {{ detailData.dueDate || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="收款时间">
            {{ detailData.paymentTime || '-' }}
          </a-descriptions-item>
          <a-descriptions-item label="下单时间" :span="2">
            {{ detailData.orderTime || '-' }}
          </a-descriptions-item>
        </a-descriptions>

        <!-- 备注信息 -->
        <a-descriptions title="备注信息" :column="1" bordered style="margin-top: 24px">
          <a-descriptions-item label="备注" :span="2">
            <div style="white-space: pre-wrap; max-height: 120px; overflow-y: auto;">
              {{ detailData.remark || '暂无备注' }}
            </div>
          </a-descriptions-item>
        </a-descriptions>

        <!-- 扩展信息 -->
        <div v-if="extendedInfo && Object.keys(extendedInfo).length > 0" style="margin-top: 24px">
          <a-descriptions title="扩展信息" :column="2" bordered>
            <a-descriptions-item v-if="extendedInfo.department" label="用户部门">
              {{ extendedInfo.department }}
            </a-descriptions-item>
            <a-descriptions-item v-if="extendedInfo.salesPerson" label="销售人员">
              {{ extendedInfo.salesPerson }}
            </a-descriptions-item>
            <a-descriptions-item v-if="extendedInfo.contractNo" label="合同编号">
              {{ extendedInfo.contractNo }}
            </a-descriptions-item>
            <a-descriptions-item v-if="extendedInfo.refundAmount" label="退款金额">
              <span style="color: #f5222d;">
                ¥{{ extendedInfo.refundAmount.toFixed(2) }}
              </span>
            </a-descriptions-item>
          </a-descriptions>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup>
import { ref } from 'vue';
import { financeDetailApi } from '/@/api/business/finance/finance-detail-api';
import { 
  PAYMENT_METHOD_ENUM,
  PAYMENT_CHANNEL_ENUM,
  PLATFORM_ACCOUNT_METHOD_ENUM
} from '/@/constants/business/finance/finance-detail-const';
import { smartSentry } from '/@/lib/smart-sentry';

// 弹窗状态
const modalVisible = ref(false);
const loading = ref(false);

// 详情数据
const detailData = ref(null);
const extendedInfo = ref(null);

// 显示详情弹窗
async function showModal(record) {
  modalVisible.value = true;
  loading.value = true;
  
  try {
    // 暂时使用假数据，注释掉真实API调用
    // const result = await financeDetailApi.getFinanceDetailInfo(record.detailId);
    
    // 模拟详情数据
    const mockDetailData = {
      data: {
        ...record, // 继承传入的record数据
        // 可以添加更多详细信息
        extendedInfo: {
          department: '技术部',
          salesPerson: '张销售',
          contractNo: 'CON20250507001',
          refundAmount: 0.00
        }
      }
    };
    
    detailData.value = mockDetailData.data;
    extendedInfo.value = mockDetailData.data.extendedInfo;
    
    // 真实API调用代码（暂时注释）
    // detailData.value = result.data;
    // extendedInfo.value = result.data.extendedInfo;
  } catch (error) {
    smartSentry.captureError(error);
  } finally {
    loading.value = false;
  }
}

// 关闭弹窗
function handleCancel() {
  modalVisible.value = false;
  detailData.value = null;
  extendedInfo.value = null;
}

// 获取收款方式文本
function getPaymentMethodText(method) {
  const methodEnum = Object.values(PAYMENT_METHOD_ENUM).find(item => item.value === method);
  return methodEnum ? methodEnum.label : '未知';
}

// 获取扣款状态颜色
function getDeductionStatusColor(status) {
  const colorMap = {
    'WAIT_AUTHORIZ': 'default',
    'PENDING_DEDUCT': 'blue',
    'PAID': 'success',
    'DEDUCT_FAIL': 'error',
    'PAUSED': 'warning',
    'TERMINATED': 'error',
    'CANCELED': 'default'
  };
  return colorMap[status] || 'default';
}

// 获取扣款状态文本
function getDeductionStatusText(status) {
  const statusMap = {
    'WAIT_AUTHORIZ': '待授权',
    'PENDING_DEDUCT': '待扣款',
    'PAID': '已扣款',
    'DEDUCT_FAIL': '扣款失败',
    'PAUSED': '已暂停',
    'TERMINATED': '已解约',
    'CANCELED': '已取消'
  };
  return statusMap[status] || '未知';
}

// 获取支付渠道文本
function getPaymentChannelText(channel) {
  const channelEnum = Object.values(PAYMENT_CHANNEL_ENUM).find(item => item.value === channel);
  return channelEnum ? channelEnum.label : '未知';
}

// 获取平台入账方式文本
function getPlatformAccountMethodText(method) {
  const methodEnum = Object.values(PLATFORM_ACCOUNT_METHOD_ENUM).find(item => item.value === method);
  return methodEnum ? methodEnum.label : '未知';
}

// 暴露方法
defineExpose({
  showModal
});
</script>

<style scoped>
:deep(.ant-descriptions-title) {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
}

:deep(.ant-descriptions-item-label) {
  font-weight: 500;
  background-color: #fafafa;
}
</style> 