<!--
  * 分段详情弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 16:00:00
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    :title="'分段详情 - ' + (formData.orderNo || '')"
    v-model:open="visible"
    :width="1000"
    :footer="null"
    :destroyOnClose="true"
  >
    <a-spin :spinning="loading">
      <div class="segment-detail-container">
        <!-- 基本信息 -->
        <a-card title="基本信息" size="small" class="detail-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>订单号：</label>
                <span>{{ formData.orderNo }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>商品名称：</label>
                <span>{{ formData.goodsName }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>订单总金额：</label>
                <span class="amount-text primary">¥{{ formData.orderTotalAmount }}</span>
              </div>
            </a-col>
          </a-row>
          
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>下单时间：</label>
                <span>{{ formData.orderTime }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>当前期数：</label>
                <a-tag color="blue">{{ formData.periodNumber }}</a-tag>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>是否逾期：</label>
                <a-tag :color="formData.isOverdue ? 'red' : 'green'">
                  {{ formData.isOverdue ? '是' : '否' }}
                </a-tag>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 用户信息 -->
        <a-card title="用户信息" size="small" class="detail-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>用户姓名：</label>
                <span>{{ formData.userName }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>用户手机号：</label>
                <span>{{ formData.userPhone }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>邀请码：</label>
                <span>{{ formData.inviteCode || '-' }}</span>
              </div>
            </a-col>
          </a-row>
          
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>员工信息：</label>
                <span>{{ formData.employeeInfo || '-' }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>部门：</label>
                <span>{{ formData.department || '-' }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 扣款信息 -->
        <a-card title="扣款信息" size="small" class="detail-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>扣款状态：</label>
                <a-tag :color="getDeductionStatusColor(formData.deductionStatus)">
                  {{ getDeductionStatusDesc(formData.deductionStatus) }}
                </a-tag>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>收款方式：</label>
                <a-tag color="geekblue">{{ getPaymentMethodDesc(formData.paymentMethod) }}</a-tag>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>扣款日期：</label>
                <span>{{ formData.deductionDate || '-' }}</span>
              </div>
            </a-col>
          </a-row>
          
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>应收日期：</label>
                <span>{{ formData.dueDate }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>订单代扣解除：</label>
                <a-tag :color="formData.orderWithholdRelease === 'ACTIVE' ? 'green' : 'red'">
                  {{ getWithholdReleaseDesc(formData.orderWithholdRelease) }}
                </a-tag>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>当前代扣解除：</label>
                <a-tag :color="formData.currentWithholdRelease === 'ACTIVE' ? 'green' : 'red'">
                  {{ getWithholdReleaseDesc(formData.currentWithholdRelease) }}
                </a-tag>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 金额信息 -->
        <a-card title="金额信息" size="small" class="detail-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>应扣金额：</label>
                <span class="amount-text warning">¥{{ formData.shouldDeductAmount }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>已扣金额：</label>
                <span class="amount-text success">¥{{ formData.deductedAmount }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>未扣金额：</label>
                <span class="amount-text danger">¥{{ (parseFloat(formData.shouldDeductAmount) - parseFloat(formData.deductedAmount)).toFixed(2) }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 扣款失败信息 -->
        <a-card title="扣款失败信息" size="small" class="detail-card" v-if="formData.failureReason">
          <a-row :gutter="24">
            <a-col :span="12">
              <div class="detail-item">
                <label>失败时间：</label>
                <span style="color: #ff4d4f;">{{ formData.failureTime }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="detail-item">
                <label>失败原因：</label>
                <span style="color: #ff4d4f;">{{ formData.failureReason }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 扣款计划备注 -->
        <a-card title="扣款计划备注" size="small" class="detail-card" v-if="formData.planRemark">
          <div class="detail-item">
            <span>{{ formData.planRemark }}</span>
          </div>
        </a-card>

        <!-- 分段列表 -->
        <a-card title="分段明细列表" size="small" class="detail-card">
          <a-table
            :dataSource="segmentList"
            :columns="segmentColumns"
            :pagination="false"
            size="small"
            rowKey="segmentId"
          >
            <template #bodyCell="{ text, record, column }">
              <template v-if="column.dataIndex === 'periodNumber'">
                <a-tag color="blue">{{ text }}</a-tag>
              </template>
              <template v-if="column.dataIndex === 'deductionStatus'">
                <a-tag :color="getDeductionStatusColor(text)">{{ getDeductionStatusDesc(text) }}</a-tag>
              </template>
              <template v-if="column.dataIndex === 'shouldDeductAmount'">
                <span class="amount-text warning">¥{{ text }}</span>
              </template>
              <template v-if="column.dataIndex === 'deductedAmount'">
                <span class="amount-text success">¥{{ text }}</span>
              </template>
              <template v-if="column.dataIndex === 'isOverdue'">
                <a-tag :color="text ? 'red' : 'green'">{{ text ? '是' : '否' }}</a-tag>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 操作记录 -->
        <a-card title="操作记录" size="small" class="detail-card">
          <a-table
            :dataSource="operationRecords"
            :columns="recordColumns"
            :pagination="false"
            size="small"
            rowKey="recordId"
          >
            <template #bodyCell="{ text, record, column }">
              <template v-if="column.dataIndex === 'operationType'">
                <a-tag :color="getOperationTypeColor(text)">{{ text }}</a-tag>
              </template>
              <template v-if="column.dataIndex === 'result'">
                <a-tag :color="text === '成功' ? 'green' : 'red'">{{ text }}</a-tag>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 操作按钮 -->
        <div class="modal-footer">
          <a-space>
            <a-button @click="handleClose">关闭</a-button>
            <a-button type="primary" @click="handleRetryDeduction" v-if="formData.deductionStatus === 'FAILED'">重新扣款</a-button>
            <a-button type="primary" @click="handleManualDeduction" v-if="formData.deductionStatus === 'PENDING'">手动扣款</a-button>
            <a-button @click="handleExportDetail">导出详情</a-button>
          </a-space>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import { segmentDetailApi } from '/@/api/business/finance/segment-detail-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { 
    WITHHOLD_RELEASE_ENUM,
    PAYMENT_METHOD_ENUM
  } from '/@/constants/business/finance/segment-detail-const';

  // ----------------------- 事件 ---------------------------
  const emit = defineEmits(['reload']);

  // ----------------------- 数据定义 ---------------------------
  const visible = ref(false);
  const loading = ref(false);
  
  const formData = reactive({
    segmentId: '',
    orderNo: '',
    goodsName: '',
    orderTotalAmount: 0,
    orderTime: '',
    periodNumber: 0,
    isOverdue: false,
    userName: '',
    userPhone: '',
    inviteCode: '',
    employeeInfo: '',
    department: '',
    deductionStatus: '',
    paymentMethod: '',
    deductionDate: '',
    dueDate: '',
    orderWithholdRelease: '',
    currentWithholdRelease: '',
    shouldDeductAmount: 0,
    deductedAmount: 0,
    failureTime: '',
    failureReason: '',
    planRemark: ''
  });

  // 分段明细列表
  const segmentList = ref([]);
  // 操作记录数据
  const operationRecords = ref([]);

  // 分段明细表格列
  const segmentColumns = [
    {
      title: '期数',
      dataIndex: 'periodNumber',
      width: 80,
    },
    {
      title: '应扣金额',
      dataIndex: 'shouldDeductAmount',
      width: 120,
    },
    {
      title: '已扣金额',
      dataIndex: 'deductedAmount',
      width: 120,
    },
    {
      title: '扣款状态',
      dataIndex: 'deductionStatus',
      width: 100,
    },
    {
      title: '是否逾期',
      dataIndex: 'isOverdue',
      width: 100,
    },
    {
      title: '应收日期',
      dataIndex: 'dueDate',
      width: 120,
    },
    {
      title: '扣款日期',
      dataIndex: 'deductionDate',
      ellipsis: true,
    },
  ];

  // 操作记录表格列
  const recordColumns = [
    {
      title: '操作时间',
      dataIndex: 'operationTime',
      width: 150,
    },
    {
      title: '操作类型',
      dataIndex: 'operationType',
      width: 120,
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      width: 100,
    },
    {
      title: '操作结果',
      dataIndex: 'result',
      width: 100,
    },
    {
      title: '备注说明',
      dataIndex: 'remark',
      ellipsis: true,
    },
  ];

  // ----------------------- 公共函数 ---------------------------

  /**
   * 显示弹窗
   */
  function show(record) {
    Object.assign(formData, record);
    visible.value = true;
    loadDetail(record.segmentId);
  }

  /**
   * 加载详细信息
   */
  async function loadDetail(segmentId) {
    loading.value = true;
    try {
      // 生产环境使用真实API
      // let result = await segmentDetailApi.getDetail(segmentId);
      // Object.assign(formData, result.data);
      // segmentList.value = result.data.segmentList || [];
      // operationRecords.value = result.data.operationRecords || [];
      
      // 开发环境使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 模拟分段明细列表
      segmentList.value = [
        {
          segmentId: 1,
          periodNumber: 1,
          shouldDeductAmount: '550.00',
          deductedAmount: '550.00',
          deductionStatus: 'SUCCESS',
          isOverdue: false,
          dueDate: '2025/05/09',
          deductionDate: '2025/05/09 20:00:00'
        },
        {
          segmentId: 2,
          periodNumber: 2,
          shouldDeductAmount: '550.00',
          deductedAmount: '0.00',
          deductionStatus: 'PENDING',
          isOverdue: false,
          dueDate: '2025/06/09',
          deductionDate: '-'
        },
        {
          segmentId: 3,
          periodNumber: 3,
          shouldDeductAmount: '550.00',
          deductedAmount: '0.00',
          deductionStatus: 'PENDING',
          isOverdue: false,
          dueDate: '2025/07/09',
          deductionDate: '-'
        }
      ];
      
      // 模拟操作记录
      operationRecords.value = [
        {
          recordId: 1,
          operationTime: '2025-05-09 20:00:00',
          operationType: '自动扣款',
          operator: '系统',
          result: '成功',
          remark: '第1期扣款成功'
        },
        {
          recordId: 2,
          operationTime: '2025-05-09 11:29:17',
          operationType: '重试扣款',
          operator: '系统',
          result: '失败',
          remark: '用户银行卡余额不足'
        },
        {
          recordId: 3,
          operationTime: '2025-05-08 10:00:00',
          operationType: '创建扣款计划',
          operator: '系统',
          result: '成功',
          remark: '订单创建时自动生成扣款计划'
        }
      ];
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 重新扣款
   */
  async function handleRetryDeduction() {
    try {
      loading.value = true;
      // await segmentDetailApi.retryPayment({ segmentId: formData.segmentId });
      message.success('重新扣款请求已提交');
      handleClose();
      emit('reload');
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 手动扣款
   */
  async function handleManualDeduction() {
    try {
      loading.value = true;
      // await segmentDetailApi.manualDeduction({ segmentId: formData.segmentId });
      message.success('手动扣款成功');
      handleClose();
      emit('reload');
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 导出详情
   */
  function handleExportDetail() {
    message.success('导出成功，请查看下载文件');
  }

  /**
   * 关闭弹窗
   */
  function handleClose() {
    visible.value = false;
  }

  // ----------------------- 辅助函数 ---------------------------

  /**
   * 获取代扣解除状态描述
   */
  function getWithholdReleaseDesc(value) {
    return WITHHOLD_RELEASE_ENUM[value]?.desc || value;
  }

  /**
   * 获取收款方式描述
   */
  function getPaymentMethodDesc(value) {
    return PAYMENT_METHOD_ENUM[value]?.desc || value;
  }

  /**
   * 获取扣款状态描述
   */
  function getDeductionStatusDesc(value) {
    const statusMap = {
      'WAIT_AUTHORIZ': '待授权',
      'PENDING_DEDUCT': '待扣款',
      'PAID': '已扣款',
      'DEDUCT_FAIL': '扣款失败',
      'PAUSED': '已暂停',
      'TERMINATED': '已解约',
      'CANCELED': '已取消'
    };
    return statusMap[value] || value;
  }

  /**
   * 获取扣款状态颜色
   */
  function getDeductionStatusColor(value) {
    const colorMap = {
      'WAIT_AUTHORIZ': 'default',
      'PENDING_DEDUCT': 'blue',
      'PAID': 'green',
      'DEDUCT_FAIL': 'red',
      'PAUSED': 'warning',
      'TERMINATED': 'red',
      'CANCELED': 'default'
    };
    return colorMap[value] || 'default';
  }

  /**
   * 获取操作类型颜色
   */
  function getOperationTypeColor(type) {
    const colorMap = {
      '自动扣款': 'blue',
      '手动扣款': 'green',
      '重试扣款': 'orange',
      '取消扣款': 'red',
      '创建扣款计划': 'purple',
      '修改扣款计划': 'cyan'
    };
    return colorMap[type] || 'default';
  }

  // ----------------------- 暴露给父组件的方法 ---------------------------
  defineExpose({
    show
  });
</script>

<style scoped>
.segment-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-item {
  margin-bottom: 12px;
}

.detail-item label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
  display: inline-block;
}

.amount-text {
  font-weight: bold;
  font-size: 14px;
}

.amount-text.primary {
  color: #1890ff;
}

.amount-text.warning {
  color: #fa8c16;
}

.amount-text.success {
  color: #52c41a;
}

.amount-text.danger {
  color: #ff4d4f;
}

.modal-footer {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>