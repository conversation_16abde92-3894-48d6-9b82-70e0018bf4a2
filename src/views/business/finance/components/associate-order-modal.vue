<template>
  <a-modal
    v-model:visible="visible"
    :title="'关联订单'"
    width="900px"
    :footer="null"
    @cancel="handleCancel"
    :maskClosable="false"
    class="associate-order-modal"
  >
    <div class="associate-tip">
      <span class="tip-text">
        预定金付款后，系统将每小时自动查询一次分段订单，以“手机号”为标准自动匹配用户最新一笔履约中的分段订单
      </span>
    </div>
    <a-form layout="vertical" class="associate-form">
      <a-form-item
        label="关联订单"
        :required="true"
        :validateStatus="orderNoError ? 'error' : ''"
        :help="orderNoError ? '请输入订单号搜索订单' : ''"
      >
        <a-input-search
          v-model:value="searchOrderNo"
          placeholder="输入订单号搜索订单"
          enter-button="搜索"
          style="width: 600px"
          @search="handleSearchOrder"
          :loading="searching"
        />
      </a-form-item>
      <div class="order-info-block">
        <div class="order-info-title">订单信息</div>
        <div v-if="orderInfo" class="order-info-content">
          <div class="order-info-row">
            <span>订单编号：{{ orderInfo.orderNo || '-' }}</span>
            <span>创建时间：{{ orderInfo.createTime || '-' }}</span>
            <span>商品名称：{{ orderInfo.productName || '-' }}</span>
          </div>
          <div class="order-info-row">
            <span>订单金额：{{ orderInfo.orderAmount || '-' }}</span>
            <span>用户手机号：{{ maskPhone(orderInfo.customerPhone) }}</span>
            <span>用户名称：{{ orderInfo.customerName || '-' }}</span>
          </div>
        </div>
        <div v-else class="order-info-empty">暂无订单信息</div>
      </div>
      <div class="modal-footer-btns">
        <a-button @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk" :loading="confirming">确定</a-button>
      </div>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, watch, defineEmits, defineProps } from 'vue';
import { message } from 'ant-design-vue';

const props = defineProps({
  visible: Boolean,
  defaultOrderNo: String,
});
const emit = defineEmits(['update:visible', 'confirm', 'cancel']);

const visible = ref(props.visible);
watch(() => props.visible, v => (visible.value = v));
watch(visible, v => emit('update:visible', v));

const searchOrderNo = ref(props.defaultOrderNo || '');
const orderNoError = ref(false);
const searching = ref(false);
const confirming = ref(false);
const orderInfo = ref(null);

function handleSearchOrder() {
  if (!searchOrderNo.value) {
    orderNoError.value = true;
    return;
  }
  orderNoError.value = false;
  searching.value = true;
  // TODO: 替换为实际API
  setTimeout(() => {
    // 假数据，实际应请求后端
    orderInfo.value = {
      orderNo: searchOrderNo.value,
      createTime: '2025/05/09 15:00:00',
      productName: 'CG 板绘进阶班',
      orderAmount: '6600.00',
      customerPhone: '18682338233',
      customerName: '张三',
    };
    searching.value = false;
  }, 800);
}

function handleOk() {
  if (!orderInfo.value) {
    message.error('请先搜索并选择要关联的订单');
    return;
  }
  confirming.value = true;
  setTimeout(() => {
    confirming.value = false;
    emit('confirm', orderInfo.value);
    visible.value = false;
  }, 600);
}

function handleCancel() {
  emit('cancel');
  visible.value = false;
}

function maskPhone(phone) {
  if (!phone) return '';
  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
}
</script>

<style scoped>
.associate-order-modal .associate-tip {
  margin-bottom: 24px;
}
.associate-order-modal .tip-text {
  color: #1890ff;
  font-size: 16px;
}
.associate-form {
  margin-top: 0;
}
.order-info-block {
  margin-top: 16px;
  margin-bottom: 24px;
}
.order-info-title {
  color: #aaa;
  font-size: 16px;
  margin-bottom: 12px;
}
.order-info-content {
  background: #fafafa;
  border-radius: 6px;
  padding: 16px 24px;
}
.order-info-row {
  display: flex;
  flex-wrap: wrap;
  gap: 40px;
  margin-bottom: 8px;
  font-size: 16px;
  color: #222;
}
.order-info-empty {
  color: #bbb;
  font-size: 15px;
  padding: 16px 0 0 0;
}
.modal-footer-btns {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  margin-top: 32px;
}
</style> 