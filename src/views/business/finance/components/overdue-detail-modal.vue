<!--
  * 逾期详情弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 15:30:00
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    :title="'逾期详情 - ' + (formData.orderNo || '')"
    v-model:open="visible"
    :width="1000"
    :footer="null"
    :destroyOnClose="true"
  >
    <a-spin :spinning="loading">
      <div class="overdue-detail-container">
        <!-- 基本信息 -->
        <a-card title="基本信息" size="small" class="detail-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>订单号：</label>
                <span>{{ formData.orderNo }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>商品名称：</label>
                <span>{{ formData.goodsName }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>创建人：</label>
                <span>{{ formData.createUser }}</span>
              </div>
            </a-col>
          </a-row>
          
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>下单时间：</label>
                <span>{{ formData.orderTime }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>订单状态：</label>
                <a-tag color="blue">{{ formData.status }}</a-tag>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>当前期数：</label>
                <a-tag color="green">{{ formData.currentPeriod }}</a-tag>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 用户信息 -->
        <a-card title="用户信息" size="small" class="detail-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>用户姓名：</label>
                <span>{{ formData.userName }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>用户手机号：</label>
                <span>{{ formData.userPhone }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>邀请码：</label>
                <span>{{ formData.inviteCode || '-' }}</span>
              </div>
            </a-col>
          </a-row>
          
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>部门：</label>
                <span>{{ formData.department || '-' }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>存在客诉：</label>
                <a-tag :color="formData.hasComplaint ? 'red' : 'green'">
                  {{ formData.hasComplaint ? '是' : '否' }}
                </a-tag>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 代扣信息 -->
        <a-card title="代扣信息" size="small" class="detail-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>当前代扣工具：</label>
                <span>{{ formData.currentWithholdTool }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>收款方式：</label>
                <a-tag color="geekblue">{{ getPaymentMethodDesc(formData.paymentMethod) }}</a-tag>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>代扣状态：</label>
                <a-tag :color="formData.currentWithholdRelease === 'ACTIVE' ? 'green' : 'red'">
                  {{ getWithholdReleaseDesc(formData.currentWithholdRelease) }}
                </a-tag>
              </div>
            </a-col>
          </a-row>
          
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>解绑时间：</label>
                <span>{{ formData.releaseTime || '-' }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>解绑来源：</label>
                <span>{{ formData.releaseSource || '-' }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 逾期金额信息 -->
        <a-card title="逾期金额信息" size="small" class="detail-card">
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>应扣金额：</label>
                <span class="amount-text primary">¥{{ formData.shouldDeductAmount }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>扣款金额：</label>
                <span class="amount-text success">¥{{ formData.deductedAmount }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>逾期金额：</label>
                <span class="amount-text danger">¥{{ formData.overdueAmount }}</span>
              </div>
            </a-col>
          </a-row>
          
          <a-row :gutter="24">
            <a-col :span="8">
              <div class="detail-item">
                <label>应收日期：</label>
                <span>{{ formData.dueDate }}</span>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>逾期天数：</label>
                <a-tag :color="getOverdueDaysColor(formData.overdueDays)">
                  {{ formData.overdueDays }}天
                </a-tag>
              </div>
            </a-col>
            <a-col :span="8">
              <div class="detail-item">
                <label>订单总逾期次数：</label>
                <a-tag color="orange">{{ formData.orderOverdueCount }}</a-tag>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 扣款失败信息 -->
        <a-card title="扣款失败信息" size="small" class="detail-card" v-if="formData.failureReason">
          <a-row :gutter="24">
            <a-col :span="12">
              <div class="detail-item">
                <label>失败时间：</label>
                <span style="color: #ff4d4f;">{{ formData.failureTime }}</span>
              </div>
            </a-col>
            <a-col :span="12">
              <div class="detail-item">
                <label>失败原因：</label>
                <span style="color: #ff4d4f;">{{ formData.failureReason }}</span>
              </div>
            </a-col>
          </a-row>
        </a-card>

        <!-- 处理记录 -->
        <a-card title="处理记录" size="small" class="detail-card">
          <a-table
            :dataSource="processRecords"
            :columns="recordColumns"
            :pagination="false"
            size="small"
            rowKey="recordId"
          >
            <template #bodyCell="{ text, record, column }">
              <template v-if="column.dataIndex === 'processType'">
                <a-tag :color="getProcessTypeColor(text)">{{ text }}</a-tag>
              </template>
              <template v-if="column.dataIndex === 'result'">
                <a-tag :color="text === '成功' ? 'green' : 'red'">{{ text }}</a-tag>
              </template>
            </template>
          </a-table>
        </a-card>

        <!-- 操作按钮 -->
        <div class="modal-footer">
          <a-space>
            <a-button @click="handleClose">关闭</a-button>
            <a-button type="primary" @click="handleProcess">处理逾期</a-button>
            <a-button @click="handleExportDetail">导出详情</a-button>
          </a-space>
        </div>
      </div>
    </a-spin>
  </a-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { message } from 'ant-design-vue';
  import { overdueDetailApi } from '/@/api/business/finance/overdue-detail-api';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { 
    WITHHOLD_RELEASE_ENUM,
    PAYMENT_METHOD_ENUM
  } from '/@/constants/business/finance/overdue-detail-const';

  // ----------------------- 事件 ---------------------------
  const emit = defineEmits(['reload']);

  // ----------------------- 数据定义 ---------------------------
  const visible = ref(false);
  const loading = ref(false);
  
  const formData = reactive({
    overdueId: '',
    orderNo: '',
    goodsName: '',
    createUser: '',
    orderTime: '',
    status: '',
    currentPeriod: 0,
    userName: '',
    userPhone: '',
    inviteCode: '',
    department: '',
    hasComplaint: false,
    currentWithholdTool: '',
    paymentMethod: '',
    currentWithholdRelease: '',
    releaseTime: '',
    releaseSource: '',
    shouldDeductAmount: 0,
    deductedAmount: 0,
    overdueAmount: 0,
    dueDate: '',
    overdueDays: 0,
    orderOverdueCount: 0,
    failureTime: '',
    failureReason: ''
  });

  // 处理记录数据
  const processRecords = ref([]);

  // 处理记录表格列
  const recordColumns = [
    {
      title: '处理时间',
      dataIndex: 'processTime',
      width: 150,
    },
    {
      title: '处理类型',
      dataIndex: 'processType',
      width: 120,
    },
    {
      title: '处理人',
      dataIndex: 'processor',
      width: 100,
    },
    {
      title: '处理结果',
      dataIndex: 'result',
      width: 100,
    },
    {
      title: '备注说明',
      dataIndex: 'remark',
      ellipsis: true,
    },
  ];

  // ----------------------- 公共函数 ---------------------------

  /**
   * 显示弹窗
   */
  function show(record) {
    Object.assign(formData, record);
    visible.value = true;
    loadDetail(record.overdueId);
  }

  /**
   * 加载详细信息
   */
  async function loadDetail(overdueId) {
    loading.value = true;
    try {
      // 生产环境使用真实API
      // let result = await overdueDetailApi.getDetail(overdueId);
      // Object.assign(formData, result.data);
      // processRecords.value = result.data.processRecords || [];
      
      // 开发环境使用模拟数据
      await new Promise(resolve => setTimeout(resolve, 300));
      
      // 模拟处理记录
      processRecords.value = [
        {
          recordId: 1,
          processTime: '2025-05-10 10:30:00',
          processType: '系统催收',
          processor: '系统',
          result: '成功',
          remark: '已发送催收短信'
        },
        {
          recordId: 2,
          processTime: '2025-05-11 14:20:00',
          processType: '人工催收',
          processor: '李客服',
          result: '失败',
          remark: '用户电话无法接通'
        },
        {
          recordId: 3,
          processTime: '2025-05-12 09:15:00',
          processType: '重新扣款',
          processor: '系统',
          result: '失败',
          remark: '银行卡余额不足'
        }
      ];
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 处理逾期
   */
  function handleProcess() {
    message.info('处理逾期功能开发中...');
    // 这里可以打开处理逾期的弹窗或跳转到处理页面
  }

  /**
   * 导出详情
   */
  function handleExportDetail() {
    message.success('导出成功，请查看下载文件');
  }

  /**
   * 关闭弹窗
   */
  function handleClose() {
    visible.value = false;
  }

  // ----------------------- 辅助函数 ---------------------------

  /**
   * 获取代扣解除状态描述
   */
  function getWithholdReleaseDesc(value) {
    return WITHHOLD_RELEASE_ENUM[value]?.desc || value;
  }

  /**
   * 获取收款方式描述
   */
  function getPaymentMethodDesc(value) {
    return PAYMENT_METHOD_ENUM[value]?.desc || value;
  }

  /**
   * 获取逾期天数颜色
   */
  function getOverdueDaysColor(days) {
    if (days <= 7) return 'green';
    if (days <= 15) return 'orange';
    if (days <= 30) return 'red';
    return 'purple';
  }

  /**
   * 获取处理类型颜色
   */
  function getProcessTypeColor(type) {
    const colorMap = {
      '系统催收': 'blue',
      '人工催收': 'orange',
      '重新扣款': 'purple',
      '延期处理': 'cyan',
      '减免处理': 'green'
    };
    return colorMap[type] || 'default';
  }

  // ----------------------- 暴露给父组件的方法 ---------------------------
  defineExpose({
    show
  });
</script>

<style scoped>
.overdue-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  margin-bottom: 16px;
}

.detail-item {
  margin-bottom: 12px;
}

.detail-item label {
  font-weight: 500;
  color: #666;
  margin-right: 8px;
  min-width: 80px;
  display: inline-block;
}

.amount-text {
  font-weight: bold;
  font-size: 14px;
}

.amount-text.primary {
  color: #1890ff;
}

.amount-text.success {
  color: #52c41a;
}

.amount-text.danger {
  color: #ff4d4f;
}

.modal-footer {
  text-align: right;
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}
</style>