<!--
  * 批量处理逾期弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 15:30:00
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    title="批量处理逾期订单"
    v-model:open="visible"
    :width="800"
    @ok="handleSubmit"
    @cancel="handleClose"
    :confirmLoading="loading"
    okText="确认处理"
    cancelText="取消"
  >
    <a-form
      :model="formData"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <a-form-item label="处理类型" name="processType" :rules="[{ required: true, message: '请选择处理类型' }]">
        <a-select v-model:value="formData.processType" placeholder="请选择处理类型" @change="onProcessTypeChange">
          <a-select-option value="RETRY_PAYMENT">重新扣款</a-select-option>
          <a-select-option value="MANUAL_COLLECT">人工催收</a-select-option>
          <a-select-option value="DELAY_PROCESS">延期处理</a-select-option>
          <a-select-option value="REDUCE_AMOUNT">减免处理</a-select-option>
          <a-select-option value="MARK_BAD_DEBT">标记坏账</a-select-option>
        </a-select>
      </a-form-item>

      <!-- 延期处理特有字段 -->
      <template v-if="formData.processType === 'DELAY_PROCESS'">
        <a-form-item label="延期天数" name="delayDays" :rules="[{ required: true, message: '请输入延期天数' }]">
          <a-input-number
            v-model:value="formData.delayDays"
            :min="1"
            :max="30"
            placeholder="请输入延期天数"
            style="width: 100%"
          />
        </a-form-item>
      </template>

      <!-- 减免处理特有字段 -->
      <template v-if="formData.processType === 'REDUCE_AMOUNT'">
        <a-form-item label="减免类型" name="reduceType" :rules="[{ required: true, message: '请选择减免类型' }]">
          <a-radio-group v-model:value="formData.reduceType">
            <a-radio value="PERCENTAGE">按比例减免</a-radio>
            <a-radio value="FIXED_AMOUNT">固定金额减免</a-radio>
          </a-radio-group>
        </a-form-item>

        <a-form-item 
          v-if="formData.reduceType === 'PERCENTAGE'" 
          label="减免比例" 
          name="reducePercentage"
          :rules="[{ required: true, message: '请输入减免比例' }]"
        >
          <a-input-number
            v-model:value="formData.reducePercentage"
            :min="1"
            :max="100"
            placeholder="请输入减免比例"
            style="width: 100%"
            addonAfter="%"
          />
        </a-form-item>

        <a-form-item 
          v-if="formData.reduceType === 'FIXED_AMOUNT'" 
          label="减免金额" 
          name="reduceAmount"
          :rules="[{ required: true, message: '请输入减免金额' }]"
        >
          <a-input-number
            v-model:value="formData.reduceAmount"
            :min="0"
            :precision="2"
            placeholder="请输入减免金额"
            style="width: 100%"
            addonBefore="¥"
          />
        </a-form-item>
      </template>

      <!-- 人工催收特有字段 -->
      <template v-if="formData.processType === 'MANUAL_COLLECT'">
        <a-form-item label="催收方式" name="collectMethod" :rules="[{ required: true, message: '请选择催收方式' }]">
          <a-checkbox-group v-model:value="formData.collectMethod">
            <a-checkbox value="SMS">短信催收</a-checkbox>
            <a-checkbox value="PHONE">电话催收</a-checkbox>
            <a-checkbox value="EMAIL">邮件催收</a-checkbox>
            <a-checkbox value="APP_PUSH">APP推送</a-checkbox>
          </a-checkbox-group>
        </a-form-item>

        <a-form-item label="催收模板" name="templateId">
          <a-select v-model:value="formData.templateId" placeholder="请选择催收模板" allowClear>
            <a-select-option value="TEMPLATE_1">温馨提醒模板</a-select-option>
            <a-select-option value="TEMPLATE_2">严重逾期模板</a-select-option>
            <a-select-option value="TEMPLATE_3">最终通知模板</a-select-option>
          </a-select>
        </a-form-item>
      </template>

      <a-form-item label="处理原因" name="reason" :rules="[{ required: true, message: '请输入处理原因' }]">
        <a-textarea
          v-model:value="formData.reason"
          :rows="3"
          placeholder="请输入处理原因"
          :maxlength="200"
          showCount
        />
      </a-form-item>

      <a-form-item label="备注说明" name="remark">
        <a-textarea
          v-model:value="formData.remark"
          :rows="2"
          placeholder="请输入备注说明"
          :maxlength="500"
          showCount
        />
      </a-form-item>
    </a-form>

    <!-- 选中的订单信息 -->
    <div class="selected-orders-info">
      <a-divider>选中的订单信息（共{{ selectedRowKeys.length }}条）</a-divider>
      <div class="orders-summary">
        <a-space size="large">
          <span>总逾期金额：<strong style="color: #ff4d4f;">¥{{ totalOverdueAmount }}</strong></span>
          <span>平均逾期天数：<strong style="color: #fa8c16;">{{ avgOverdueDays }}天</strong></span>
        </a-space>
      </div>
    </div>

    <!-- 处理结果预览 -->
    <div v-if="showPreview" class="process-preview">
      <a-divider>处理结果预览</a-divider>
      <a-alert
        :message="previewMessage"
        :type="previewType"
        show-icon
        style="margin-bottom: 16px"
      />
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, computed, watch } from 'vue';
  import { message } from 'ant-design-vue';
  import { overdueDetailApi } from '/@/api/business/finance/overdue-detail-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // ----------------------- 事件 ---------------------------
  const emit = defineEmits(['reload']);

  // ----------------------- 数据定义 ---------------------------
  const visible = ref(false);
  const loading = ref(false);
  const selectedRowKeys = ref([]);
  
  const formData = reactive({
    processType: '',
    delayDays: undefined,
    reduceType: '',
    reducePercentage: undefined,
    reduceAmount: undefined,
    collectMethod: [],
    templateId: '',
    reason: '',
    remark: ''
  });

  // 选中订单的统计信息
  const totalOverdueAmount = ref(0);
  const avgOverdueDays = ref(0);

  // 预览相关
  const showPreview = ref(false);
  const previewMessage = ref('');
  const previewType = ref('info');

  // ----------------------- 计算属性 ---------------------------
  
  // 监听处理类型变化，生成预览信息
  watch(
    () => [formData.processType, formData.delayDays, formData.reduceType, formData.reducePercentage, formData.reduceAmount],
    () => {
      generatePreview();
    },
    { deep: true }
  );

  // ----------------------- 公共函数 ---------------------------

  /**
   * 显示弹窗
   */
  function show(rowKeys) {
    selectedRowKeys.value = rowKeys;
    visible.value = true;
    resetForm();
    calculateStatistics();
  }

  /**
   * 重置表单
   */
  function resetForm() {
    Object.assign(formData, {
      processType: '',
      delayDays: undefined,
      reduceType: '',
      reducePercentage: undefined,
      reduceAmount: undefined,
      collectMethod: [],
      templateId: '',
      reason: '',
      remark: ''
    });
    showPreview.value = false;
  }

  /**
   * 计算统计信息
   */
  function calculateStatistics() {
    // 这里应该根据selectedRowKeys获取对应的订单数据
    // 暂时使用模拟数据
    totalOverdueAmount.value = selectedRowKeys.value.length * 1000; // 模拟总金额
    avgOverdueDays.value = Math.round(15 + Math.random() * 10); // 模拟平均天数
  }

  /**
   * 处理类型改变
   */
  function onProcessTypeChange() {
    // 清空相关字段
    formData.delayDays = undefined;
    formData.reduceType = '';
    formData.reducePercentage = undefined;
    formData.reduceAmount = undefined;
    formData.collectMethod = [];
    formData.templateId = '';
  }

  /**
   * 生成预览信息
   */
  function generatePreview() {
    if (!formData.processType) {
      showPreview.value = false;
      return;
    }

    showPreview.value = true;
    
    switch (formData.processType) {
      case 'RETRY_PAYMENT':
        previewMessage.value = `将对${selectedRowKeys.value.length}条逾期订单重新发起扣款操作`;
        previewType.value = 'info';
        break;
        
      case 'MANUAL_COLLECT':
        const methods = formData.collectMethod.join('、');
        previewMessage.value = `将通过${methods || '选定方式'}对${selectedRowKeys.value.length}条逾期订单进行人工催收`;
        previewType.value = 'warning';
        break;
        
      case 'DELAY_PROCESS':
        if (formData.delayDays) {
          previewMessage.value = `将对${selectedRowKeys.value.length}条逾期订单延期${formData.delayDays}天处理`;
          previewType.value = 'info';
        }
        break;
        
      case 'REDUCE_AMOUNT':
        if (formData.reduceType === 'PERCENTAGE' && formData.reducePercentage) {
          const reduceAmount = (totalOverdueAmount.value * formData.reducePercentage / 100).toFixed(2);
          previewMessage.value = `将对${selectedRowKeys.value.length}条逾期订单减免${formData.reducePercentage}%，预计减免金额：¥${reduceAmount}`;
          previewType.value = 'success';
        } else if (formData.reduceType === 'FIXED_AMOUNT' && formData.reduceAmount) {
          const totalReduce = (formData.reduceAmount * selectedRowKeys.value.length).toFixed(2);
          previewMessage.value = `将对${selectedRowKeys.value.length}条逾期订单每条减免¥${formData.reduceAmount}，总减免金额：¥${totalReduce}`;
          previewType.value = 'success';
        }
        break;
        
      case 'MARK_BAD_DEBT':
        previewMessage.value = `将${selectedRowKeys.value.length}条逾期订单标记为坏账，总金额：¥${totalOverdueAmount.value}`;
        previewType.value = 'error';
        break;
        
      default:
        showPreview.value = false;
    }
  }

  /**
   * 提交处理
   */
  async function handleSubmit() {
    try {
      // 验证表单
      if (!formData.processType) {
        message.error('请选择处理类型');
        return;
      }
      
      if (!formData.reason) {
        message.error('请输入处理原因');
        return;
      }

      // 特殊验证
      if (formData.processType === 'DELAY_PROCESS' && !formData.delayDays) {
        message.error('请输入延期天数');
        return;
      }

      if (formData.processType === 'REDUCE_AMOUNT') {
        if (!formData.reduceType) {
          message.error('请选择减免类型');
          return;
        }
        if (formData.reduceType === 'PERCENTAGE' && !formData.reducePercentage) {
          message.error('请输入减免比例');
          return;
        }
        if (formData.reduceType === 'FIXED_AMOUNT' && !formData.reduceAmount) {
          message.error('请输入减免金额');
          return;
        }
      }

      if (formData.processType === 'MANUAL_COLLECT' && formData.collectMethod.length === 0) {
        message.error('请选择催收方式');
        return;
      }

      loading.value = true;

      const params = {
        overdueIds: selectedRowKeys.value,
        ...formData
      };

      // 生产环境调用真实API
      // await overdueDetailApi.batchProcess(params);
      
      // 开发环境模拟请求
      await new Promise(resolve => setTimeout(resolve, 1000));

      message.success('批量处理成功');
      handleClose();
      emit('reload');
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      loading.value = false;
    }
  }

  /**
   * 关闭弹窗
   */
  function handleClose() {
    visible.value = false;
    resetForm();
  }

  // ----------------------- 暴露给父组件的方法 ---------------------------
  defineExpose({
    show
  });
</script>

<style scoped>
.selected-orders-info {
  margin-top: 16px;
}

.orders-summary {
  background: #f8f9fa;
  padding: 12px;
  border-radius: 6px;
  margin-bottom: 16px;
}

.process-preview {
  margin-top: 16px;
}

:deep(.ant-form-item) {
  margin-bottom: 20px;
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

:deep(.ant-checkbox-group .ant-checkbox-wrapper) {
  margin-right: 0;
}
</style>