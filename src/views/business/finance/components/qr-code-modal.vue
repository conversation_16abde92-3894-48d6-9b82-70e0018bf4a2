<!--
  * 二维码显示弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 16:30:00
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    :title="qrCodeInfo.title || '二维码'"
    v-model:open="visible"
    :width="600"
    :footer="null"
    :destroyOnClose="true"
    @cancel="handleClose"
    class="qr-code-modal"
    wrapClassName="qr-code-modal-wrap"
  >
    <div class="qr-code-container">
      <a-spin :spinning="loading">
        <!-- 二维码显示区域 -->
        <div class="qr-code-area">
          <div v-if="qrCodeImageUrl" class="qr-code-wrapper">
            <img :src="qrCodeImageUrl" alt="收款二维码" class="qr-code-image" />
            <p class="qr-tip">扫描二维码进行支付</p>
          </div>
          <div v-else class="qr-code-placeholder">
            <a-empty description="暂无二维码" />
          </div>
        </div>

        <!-- 操作按钮 -->
        <div class="operation-area">
          <a-space>
            <a-button @click="generateQrCode" type="primary" :loading="loading">
              <template #icon>
                <ReloadOutlined />
              </template>
              重新生成
            </a-button>
            <a-button @click="downloadQrCode" :disabled="!qrCodeImageUrl">
              <template #icon>
                <DownloadOutlined />
              </template>
              下载二维码
            </a-button>
          </a-space>
        </div>
      </a-spin>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, reactive, onUnmounted } from 'vue';
  import { message } from 'ant-design-vue';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { menuReservationApi } from '/@/api/business/order/menu-reservation-api';
  import { 
    ReloadOutlined, 
    DownloadOutlined 
  } from '@ant-design/icons-vue';

  // ----------------------- 数据定义 ---------------------------
  const visible = ref(false);
  const loading = ref(false);
  
  const qrCodeInfo = reactive({
    type: '',
    title: ''
  });

  const qrCodeImageUrl = ref('');

  // ----------------------- 公共函数 ---------------------------

  /**
   * 显示弹窗
   */
  function show(record) {
    qrCodeInfo.type = record.type;
    qrCodeInfo.title = record.title;
    
    visible.value = true;
    loadQrCode(record.type);
  }

  /**
   * 加载二维码
   */
  async function loadQrCode(type) {
    loading.value = true;
    try {
      // 根据type映射到API参数
      let appType;
      if (type === 'MERCHANT_MINI_PROGRAM') {
        appType = 'MERCHANT';  // 商家独立小程序
      } else if (type === 'HAPPY_PAY_MINI_PROGRAM') {
        appType = 'HUANLEFU';  // 欢乐付小程序
      } else {
        throw new Error('不支持的二维码类型');
      }

      // 调用API获取二维码
      const response = await menuReservationApi.getPaymentQrCode(appType);
      console.log('二维码API响应:', response);
      console.log('响应数据类型:', typeof response.data);
      console.log('是否为Blob:', response.data instanceof Blob);
      
      if (response.data && response.data instanceof Blob) {
        // 清理之前的URL
        if (qrCodeImageUrl.value && qrCodeImageUrl.value.startsWith('blob:')) {
          URL.revokeObjectURL(qrCodeImageUrl.value);
        }
        
        // 创建新的blob URL
        qrCodeImageUrl.value = URL.createObjectURL(response.data);
        console.log('二维码加载成功');
        return true; // 成功
      } else {
        // 如果不是Blob数据，说明API返回了错误信息
        qrCodeImageUrl.value = '';
        console.log('API返回错误信息:', response.data);
        // 从response.data.msg中获取错误信息，axios将响应数据放在data字段中
        const errorMsg = response?.data?.msg || '获取二维码失败';
        console.log('提取的错误信息:', errorMsg);
        message.warning(errorMsg);
        return false; // 失败
      }
    } catch (e) {
      console.log('请求异常:', e);
      console.log('异常响应:', e?.response);
      smartSentry.captureError(e);
      qrCodeImageUrl.value = '';
      
      // 特殊情况：如果异常对象是 application/json 类型的 Blob，需要读取其内容
      if (e instanceof Blob && e.type === 'application/json') {
        try {
          const text = await e.text();
          const errorData = JSON.parse(text);
          console.log('从Blob中解析的错误数据:', errorData);
          const errorMsg = errorData?.msg || '二维码加载失败';
          message.error(errorMsg);
        } catch (parseError) {
          console.log('解析Blob错误:', parseError);
          message.error('二维码加载失败');
        }
      } else {
        // 普通的HTTP请求异常
        const errorMsg = e?.response?.data?.msg || e?.message || '二维码加载失败';
        message.error(errorMsg);
      }
      return false; // 失败
    } finally {
      loading.value = false;
    }
  }

  /**
   * 重新生成二维码
   */
  async function generateQrCode() {
    const success = await loadQrCode(qrCodeInfo.type);
    if (success) {
      message.success('二维码已重新生成');
    }
  }

  /**
   * 下载二维码
   */
  function downloadQrCode() {
    if (!qrCodeImageUrl.value) {
      message.warning('暂无二维码可下载');
      return;
    }

    try {
      // 直接使用现有的blob URL进行下载
      const link = document.createElement('a');
      link.href = qrCodeImageUrl.value;
      link.download = `${qrCodeInfo.title}_二维码.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      message.success('二维码下载成功');
    } catch (e) {
      message.error('下载失败');
      smartSentry.captureError(e);
    }
  }

  /**
   * 关闭弹窗
   */
  function handleClose() {
    // 清理blob URL，避免内存泄漏
    if (qrCodeImageUrl.value && qrCodeImageUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(qrCodeImageUrl.value);
    }
    
    visible.value = false;
    qrCodeImageUrl.value = '';
  }

  // ----------------------- 生命周期 ---------------------------
  onUnmounted(() => {
    // 组件销毁时清理blob URL，避免内存泄漏
    if (qrCodeImageUrl.value && qrCodeImageUrl.value.startsWith('blob:')) {
      URL.revokeObjectURL(qrCodeImageUrl.value);
    }
  });

  // ----------------------- 暴露给父组件的方法 ---------------------------
  defineExpose({
    show
  });
</script>

<style>
/* 全局样式，覆盖最外层 Modal 容器 */
.qr-code-modal-wrap {
  background-color: transparent !important;
}

.qr-code-modal-wrap .ant-modal {
  background-color: transparent !important;
}

.qr-code-modal-wrap .ant-modal-content {
  background-color: #F2FAFE !important;
  border: none !important;
  border-radius: 8px !important;
  overflow: hidden !important;
}

.qr-code-modal-wrap .ant-modal-header {
  background-color: #F2FAFE !important;
  border-bottom: none !important;
}

.qr-code-modal-wrap .ant-modal-body {
  background-color: #F2FAFE !important;
  padding: 0 !important;
}

.qr-code-modal-wrap .ant-modal-footer {
  background-color: #F2FAFE !important;
  border-top: none !important;
}
</style>

<style scoped>
.qr-code-container {
  text-align: center;
  padding: 24px;
  background-color: #F2FAFE;
}

.qr-code-area {
  margin-bottom: 30px;
}

.qr-code-wrapper {
  display: inline-block;
}

.qr-code-image {
  width: 300px;
  height: auto;
  max-height: 500px;
  border: 1px solid #f0f0f0;
  border-radius: 8px;
  object-fit: contain;
}

.qr-tip {
  margin: 12px 0 0 0;
  font-size: 14px;
  color: #8c8c8c;
}

.qr-code-placeholder {
  width: 300px;
  height: 500px;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px dashed #d9d9d9;
  border-radius: 8px;
  background-color: #fafafa;
  margin: 0 auto;
}

.operation-area {
  margin-bottom: 20px;
}
</style> 