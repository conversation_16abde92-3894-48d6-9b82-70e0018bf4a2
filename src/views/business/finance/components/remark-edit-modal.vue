<!--
  * 财务明细备注编辑弹窗
  *
  * @Author:    SmartAdmin
  * @Date:      2024-01-01
  * @Copyright  SmartAdmin
-->
<template>
  <a-modal
    v-model:open="modalVisible"
    title="编辑备注"
    :width="600"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="confirmLoading"
  >
    <a-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      :label-col="{ span: 6 }"
      :wrapper-col="{ span: 18 }"
    >
      <!-- 基本信息展示 -->
      <a-divider>基本信息</a-divider>
      
      <a-form-item label="订单编号">
        <span>{{ detailData?.orderNo || '-' }}</span>
      </a-form-item>
      
      <a-form-item label="用户信息">
        <span>{{ detailData?.userName || '-' }} / {{ detailData?.userPhone || '-' }}</span>
      </a-form-item>
      
      <a-form-item label="商品名称">
        <span>{{ detailData?.productName || '-' }}</span>
      </a-form-item>

      <a-form-item label="实收金额">
        <span style="color: #fa8c16; font-weight: 500;">
          ¥{{ detailData?.actualAmount ? detailData.actualAmount.toFixed(2) : '0.00' }}
        </span>
      </a-form-item>

      <!-- 备注编辑 -->
      <a-divider>备注编辑</a-divider>
      
      <a-form-item label="当前备注">
        <div style="background-color: #f5f5f5; padding: 12px; border-radius: 6px; min-height: 60px;">
          {{ originalRemark || '暂无备注' }}
        </div>
      </a-form-item>

      <a-form-item label="新备注" name="remark">
        <a-textarea 
          v-model:value="formData.remark" 
          placeholder="请输入备注内容"
          :auto-size="{ minRows: 4, maxRows: 8 }"
          :maxlength="500"
          show-count
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup>
import { ref, reactive } from 'vue';
import { message } from 'ant-design-vue';
import { financeDetailApi } from '/@/api/business/finance/finance-detail-api';
import { SmartLoading } from '/@/components/framework/smart-loading';
import { smartSentry } from '/@/lib/smart-sentry';

const emit = defineEmits(['reloadList']);

// 弹窗状态
const modalVisible = ref(false);
const confirmLoading = ref(false);
const formRef = ref();

// 数据状态
const detailData = ref(null);
const originalRemark = ref('');

// 表单数据
const formDataState = {
  remark: ''
};

const formData = reactive({ ...formDataState });

// 表单验证规则
const rules = {
  remark: [
    { max: 500, message: '备注内容不能超过500个字符', trigger: 'blur' }
  ]
};

// 显示弹窗
function showModal(record) {
  detailData.value = record;
  originalRemark.value = record.remark || '';
  formData.remark = record.remark || '';
  modalVisible.value = true;
}

// 重置表单
function resetForm() {
  Object.assign(formData, { ...formDataState });
  if (formRef.value) {
    formRef.value.clearValidate();
  }
}

// 确认修改
async function handleOk() {
  try {
    await formRef.value.validate();
    confirmLoading.value = true;
    
    const requestData = {
      detailId: detailData.value.detailId,
      remark: formData.remark
    };
    
    // 暂时使用假数据处理，注释掉真实API调用
    // await financeDetailApi.updateFinanceDetailRemark(requestData);
    
    // 模拟更新成功
    await new Promise(resolve => setTimeout(resolve, 1000)); // 模拟网络延迟
    
    message.success('备注更新成功');
    modalVisible.value = false;
    emit('reloadList');
    
  } catch (error) {
    if (error.errorFields) {
      // 表单验证错误
      return;
    }
    smartSentry.captureError(error);
  } finally {
    confirmLoading.value = false;
  }
}

// 取消
function handleCancel() {
  modalVisible.value = false;
  resetForm();
}

// 暴露方法
defineExpose({
  showModal
});
</script>

<style scoped>
.ant-divider {
  margin: 16px 0 24px 0;
  font-weight: 600;
}
</style> 