<!--
  * 逾期明细列表
  *
  * @Author:    SmartAdmin
  * @Date:      2024-12-19 15:30:00
  * @Copyright  SmartAdmin
-->
<template>
  <!---------- 查询表单form begin ----------->
  <a-form class="smart-query-form">
    <a-row class="smart-query-form-row">
      <a-form-item label="订单号" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.orderNo" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="用户姓名" class="smart-query-form-item">
        <a-input 
          style="width: 150px" 
          v-model:value="queryForm.customerName" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="用户手机号" class="smart-query-form-item">
        <a-input 
          style="width: 180px" 
          v-model:value="queryForm.customerPhone" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="逾期天数" class="smart-query-form-item">
        <div style="display: flex; align-items: center;">
          <a-input-number
            style="width: 80px"
            v-model:value="queryForm.overdueDayStart"
            :min="0"
            placeholder="请输入"
          />
          <span style="margin: 0 8px">至</span>
          <a-input-number
            style="width: 80px"
            v-model:value="queryForm.overdueDayEnd"
            :min="0"
            placeholder="请输入"
          />
          <span style="margin-left: 8px">天</span>
        </div>
      </a-form-item>

      <a-form-item label="邀请码" class="smart-query-form-item">
        <a-input 
          style="width: 150px" 
          v-model:value="queryForm.invitationCode" 
          placeholder="请输入" 
          @keypress="onlyNumber"
        />
      </a-form-item>
    </a-row>

    <a-row class="smart-query-form-row">
      <a-form-item label="商品名称" class="smart-query-form-item">
        <a-input 
          style="width: 200px" 
          v-model:value="queryForm.productName" 
          placeholder="请输入" 
          allowClear
        />
      </a-form-item>

      <a-form-item label="存在客诉" class="smart-query-form-item">
        <a-select 
          style="width: 100px" 
          v-model:value="queryForm.complaintFlag" 
          placeholder="全部"
          allowClear
        >
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="订单代扣解除" class="smart-query-form-item">
        <a-select 
          style="width: 130px" 
          v-model:value="queryForm.deductUnbindFlag" 
          placeholder="全部"
          allowClear
        >
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="当期代扣解除" class="smart-query-form-item">
        <a-select 
          style="width: 130px" 
          v-model:value="queryForm.removeDeductFlag" 
          placeholder="全部"
          allowClear
        >
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="当日逾期" class="smart-query-form-item">
        <a-select 
          style="width: 100px" 
          v-model:value="queryForm.nowOverdue" 
          placeholder="全部"
          allowClear
        >
          <a-select-option :value="1">是</a-select-option>
          <a-select-option :value="0">否</a-select-option>
        </a-select>
      </a-form-item>

      <a-form-item label="门店名称" class="smart-query-form-item">
        <StoreMultiSelect 
          v-model="queryForm.storeName" 
          width="200px"
          placeholder="请选择门店"
        />
      </a-form-item>

      <a-form-item class="smart-query-form-item">
        <a-button-group>
          <a-button type="primary" @click="onSearch">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="resetQuery">
            <template #icon>
              <ReloadOutlined />
            </template>
            重置
          </a-button>
        </a-button-group>
      </a-form-item>
    </a-row>
  </a-form>
  <!---------- 查询表单form end ----------->

  <a-card size="small" :bordered="false" :hoverable="true">
    <!---------- 统计信息 begin ----------->
    <div class="overdue-statistics">
      <div class="statistics-text">
        逾期笔数：<strong class="statistics-number">{{ formatStatisticsNumber(statistics.deductCount) }}</strong>
        <a-divider type="vertical" />
        逾期总金额：<strong class="statistics-number">{{ formatAmount(statistics.totalOverdueAmount) }}</strong>
      </div>
    </div>
    <!---------- 统计信息 end ----------->

    <!---------- 表格操作行 begin ----------->
    <a-row class="smart-table-btn-block">
      <div class="smart-table-operate-block">
        <a-button @click="onExport">
          <template #icon>
            <ExportOutlined />
          </template>
          导出
        </a-button>
      </div>
      <div class="smart-table-setting-block">
        <TableOperator 
          v-model="columns" 
          :tableId="TABLE_ID_CONST.BUSINESS.FINANCE.OVERDUE_DETAIL" 
          :refresh="queryData" 
        />
      </div>
    </a-row>
    <!---------- 表格操作行 end ----------->

    <a-table
      size="small"
      :dataSource="tableData"
      :columns="columns"
      rowKey="id"
      :scroll="{ x: 2000, y: 2000 }"
      bordered
      :pagination="false"
      :loading="tableLoading"
      @resizeColumn="handleResizeColumn"
    >
      <template #headerCell="{ column }">
        {{ column.title }}
      </template>

      <template #bodyCell="{ text, record, column }">
        <!-- 订单信息 -->
        <template v-if="column.dataIndex === 'orderInfo'">
          <div class="order-info-cell">
            <div>{{ record.productName }}</div>
            <div class="text-gray">订单号：
              <a @click="goToSegmentOrderDetail(record.orderId)" class="order-link">
                {{ record.orderNo }}
              </a>
            </div>
            <div class="text-gray">创建人：{{ record.actualName }}</div>
            <div class="text-gray">下单时间：{{ record.createTime }}</div>
          </div>
        </template>

        <!-- 用户信息 -->
        <template v-if="column.dataIndex === 'userInfo'">
          <div class="user-info-cell">
            <div>{{ record.customerName }}</div>
            <div class="text-gray">{{ maskPhoneNumber(record.customerPhone) }}</div>
            <div class="text-gray">邀请码：{{ record.invitationCode || '-' }}</div>
            <div class="text-gray">部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>

        <!-- 绑定代扣信息 -->
        <template v-if="column.dataIndex === 'withholdInfo'">
          <div class="withhold-info-cell">
            <div>当前代扣工具：{{ getDeductMethodDesc(record.deductMethod) }}</div>
            <div>代扣是否解除：{{ record.removeDeductFlag === 0 ? '否' : '是' }}</div>
            <div>解绑时间：{{ record.deductUnbindTime || '-' }}</div>
            <div>解绑来源：{{ record.deductUnbindSource || '-' }}</div>
          </div>
        </template>

        <!-- 状态 -->
        <template v-if="column.dataIndex === 'orderStatus'">
          <a-tag :color="getStatusColor(text)">{{ getOrderStatusDesc(text) }}</a-tag>
        </template>

        <!-- 邀请码 -->
        <template v-if="column.dataIndex === 'invitationCode'">
          <div class="invitation-info-cell">
            <div>邀请码：{{ record.invitationCode || '-' }}</div>
            <div>员工：{{ record.employeeName || '-' }}</div>
            <div>部门：{{ record.departmentName || '-' }}</div>
          </div>
        </template>

        <!-- 存在客诉 -->
        <template v-if="column.dataIndex === 'complaintFlag'">
          {{ text === 1 ? '是' : '否' }}
        </template>

        <!-- 当前期数 -->
        <template v-if="column.dataIndex === 'installmentNo'">
          {{ text !== null && text !== undefined ? text : '-' }}
        </template>

        <!-- 订单总逾期数 -->
        <template v-if="column.dataIndex === 'orderOverdueCount'">
          {{ text !== null && text !== undefined ? text : '-' }}
        </template>

        <!-- 应扣金额 -->
        <template v-if="column.dataIndex === 'installmentAmount'">
          <span>{{ formatAmount(text) }}</span>
        </template>

        <!-- 扣款金额 -->
        <template v-if="column.dataIndex === 'deductAmount'">
          <span>{{ formatAmount(text) }}</span>
        </template>

        <!-- 逾期金额 -->
        <template v-if="column.dataIndex === 'overdueAmount'">
          <span>{{ formatAmount(text) }}</span>
        </template>

        <!-- 应收日期 -->
        <template v-if="column.dataIndex === 'receivableDate'">
          <div>{{ text || '-' }}</div>
        </template>

        <!-- 逾期天数 -->
        <template v-if="column.dataIndex === 'overdueDays'">
          {{ text !== null && text !== undefined ? `${text}天` : '-' }}
        </template>

        <!-- 扣款失败信息 -->
        <template v-if="column.dataIndex === 'failureInfo'">
          <div class="failure-info-cell">
            <div>[{{ record.deductTime }}]</div>
            <div>{{ record.deductFailReason }}</div>
          </div>
        </template>

        <!-- 收款方式 -->
        <template v-if="column.dataIndex === 'receivePaymentType'">
          {{ getPaymentMethodDesc(text) }}
        </template>

        <!-- 当期代扣解除 -->
        <template v-if="column.dataIndex === 'removeDeductFlag'">
          <a-tag :color="text === 0 ? 'blue' : 'red'">
            {{ text === 0 ? '否' : '是' }}
          </a-tag>
        </template>

        <template v-if="column.dataIndex === 'storeName'">
          <span>{{ text || '-' }}</span>
        </template>

      </template>
    </a-table>

    <div class="smart-query-table-page">
      <a-pagination
        showSizeChanger
        showQuickJumper
        show-less-items
        :pageSizeOptions="PAGE_SIZE_OPTIONS"
        :defaultPageSize="queryForm.pageSize"
        v-model:current="queryForm.pageNum"
        v-model:pageSize="queryForm.pageSize"
        :total="total"
        @change="queryData"
        @showSizeChange="queryData"
        :show-total="(total) => `共${total}条`"
      />
    </div>

    <!-- 逾期详情弹窗 -->
    <OverdueDetailModal 
      ref="detailModalRef" 
      @reload="queryData" 
    />
  </a-card>
</template>

<script setup>
  import { onMounted, reactive, ref, nextTick } from 'vue';
  import { useRouter } from 'vue-router';
  import { message } from 'ant-design-vue';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { overdueDetailApi } from '/@/api/business/finance/overdue-detail-api';
  import { PAGE_SIZE_OPTIONS } from '/@/constants/common-const';
  import { smartSentry } from '/@/lib/smart-sentry';
  import TableOperator from '/@/components/support/table-operator/index.vue';
  import { TABLE_ID_CONST } from '/@/constants/support/table-id-const';
  import { 
    WITHHOLD_RELEASE_ENUM,
    PAYMENT_METHOD_ENUM,
    ORDER_STATUS_ENUM
  } from '/@/constants/business/finance/overdue-detail-const';
  import { 
    SearchOutlined, 
    ReloadOutlined, 
    ExportOutlined
  } from '@ant-design/icons-vue';
  import _ from 'lodash';
  
  // 引入子组件
  import OverdueDetailModal from './components/overdue-detail-modal.vue';
  import StoreMultiSelect from '/@/components/business/store-multi-select/index.vue';

  const router = useRouter();

  // ----------------------- 以下是reactive的基础数据定义 ---------------------------
  const queryFormState = {
    orderNo: null, // 订单编号
    customerName: null, // 用户姓名
    customerPhone: null, // 用户手机号
    overdueDayStart: null, // 逾期天数起
    overdueDayEnd: null, // 逾期天数止
    invitationCode: null, // 邀请码
    productName: null, // 商品名称
    complaintFlag: null, // 存在客诉:0否1是
    deductUnbindFlag: null, // 订单代扣解除:0否1是
    removeDeductFlag: null, // 当期代扣解除:0否1是
    nowOverdue: null, // 当日逾期:0否1是
    storeName: [], // 门店名称查询字段
    pageNum: 1,
    pageSize: 10,
    searchCount: true,
    sortItemList: []
  };
  const queryForm = reactive({ ...queryFormState });
  
  const tableData = ref([]);
  const total = ref(0);
  const tableLoading = ref(false);
  // 已删除批量选择功能

  // 统计信息
  const statistics = ref({
    deductCount: 0,
    totalOverdueAmount: 0
  });

  // 弹窗引用
  const detailModalRef = ref();

  // ----------------------- 表格显示列 ---------------------------
  const columns = ref([
    {
      title: '订单信息',
      dataIndex: 'orderInfo',
      width: 300,
      resizable: true,
    },
    {
      title: '状态',
      dataIndex: 'orderStatus',
      width: 80,
      resizable: true,
    },
    {
      title: '用户信息',
      dataIndex: 'userInfo',
      width: 200,
      resizable: true,
    },
    {
      title: '邀请码',
      dataIndex: 'invitationCode',
      width: 200,
      resizable: true,
    },
    {
      title: '存在客诉',
      dataIndex: 'complaintFlag',
      width: 100,
      resizable: true,
    },
    {
      title: '绑定代扣信息',
      dataIndex: 'withholdInfo',
      width: 250,
      resizable: true,
    },
    {
      title: '当前期数',
      dataIndex: 'installmentNo',
      width: 100,
      resizable: true,
    },
    {
      title: '订单总逾期数',
      dataIndex: 'orderOverdueCount',
      width: 120,
      resizable: true,
    },
    {
      title: '应扣金额',
      dataIndex: 'installmentAmount',
      width: 120,
      resizable: true,
    },
    {
      title: '扣款金额',
      dataIndex: 'deductAmount',
      width: 120,
      resizable: true,
    },
    {
      title: '逾期金额',
      dataIndex: 'overdueAmount',
      width: 120,
      resizable: true,
    },
    {
      title: '应收日期',
      dataIndex: 'receivableDate',
      width: 120,
      resizable: true,
    },
    {
      title: '逾期天数',
      dataIndex: 'overdueDays',
      width: 100,
      resizable: true,
    },
    {
      title: '扣款失败信息',
      dataIndex: 'failureInfo',
      width: 200,
      resizable: true,
    },
    {
      title: '收款方式',
      dataIndex: 'receivePaymentType',
      width: 120,
      resizable: true,
    },
    {
      title: '当期代扣解除',
      dataIndex: 'removeDeductFlag',
      width: 130,
      resizable: true,
    },
    {
      title: '门店名称',
      dataIndex: 'storeName',
      width: 120,
      resizable: true,
    },
  ]);

  // ----------------------- 生命周期函数 ---------------------------
  onMounted(() => {
    queryData();
    loadStatistics();
  });

  // ----------------------- 公共函数 ---------------------------
  
  // 跳转到分期订单详情
  const goToSegmentOrderDetail = (orderId) => {
    router.push({
      path: '/order/segment-order-detail',
      query: { id: orderId }
    });
  };
  
  // 只允许输入数字
  function onlyNumber(event) {
    // 允许数字、删除键、退格键
    const char = String.fromCharCode(event.which);
    if (!/[0-9]/.test(char) && event.which !== 8 && event.which !== 46) {
      event.preventDefault();
    }
  }

  /**
   * 搜索
   */
  function onSearch() {
    queryForm.pageNum = 1;
    queryData();
    loadStatistics();
  }

  /**
   * 统一参数处理函数
   */
  function prepareQueryParams() {
    const params = _.cloneDeep(queryForm);
    
    // 处理Integer类型参数：空值转null，有值转数字
    if (params.complaintFlag !== '' && params.complaintFlag != null) {
      params.complaintFlag = parseInt(params.complaintFlag);
    } else {
      params.complaintFlag = null;
    }
    
    if (params.deductUnbindFlag !== '' && params.deductUnbindFlag != null) {
      params.deductUnbindFlag = parseInt(params.deductUnbindFlag);
    } else {
      params.deductUnbindFlag = null;
    }
    
    if (params.removeDeductFlag !== '' && params.removeDeductFlag != null) {
      params.removeDeductFlag = parseInt(params.removeDeductFlag);
    } else {
      params.removeDeductFlag = null;
    }
    
    if (params.nowOverdue !== '' && params.nowOverdue != null) {
      params.nowOverdue = parseInt(params.nowOverdue);
    } else {
      params.nowOverdue = null;
    }
    
    // 处理门店名称查询参数（多选时逗号拼接门店ID）
    if (params.storeName && Array.isArray(params.storeName) && params.storeName.length > 0) {
      // 如果选择了门店，将ID数组用逗号拼接成字符串
      params.storeName = params.storeName.join(',');
    } else {
      // 如果没有选择门店，传 null
      params.storeName = null;
    }
    
    return params;
  }

  /**
   * 查询数据
   */
  async function queryData() {
    tableLoading.value = true;
    try {
      // 使用统一的参数处理
      const params = prepareQueryParams();
      
      let queryResult = await overdueDetailApi.queryPage(params);
      const { data } = queryResult;
      
      tableData.value = data.list;
      total.value = data.total;
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      tableLoading.value = false;
    }
  }

  /**
   * 加载统计信息
   */
  async function loadStatistics() {
    try {
      // 使用统一的参数处理
      const params = prepareQueryParams();
      
      let result = await overdueDetailApi.getStatistics(params);
      statistics.value = result.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  /**
   * 重置查询表单
   */
  function resetQuery() {
    Object.assign(queryForm, queryFormState);
    onSearch();
  }

  // 已删除批量处理功能

  /**
   * 导出
   */
  async function onExport() {
    message.info('导出功能开发中...');
  }

  // 已删除全量导出功能

  /**
   * 显示详情弹窗
   */
  function showDetailModal(record) {
    detailModalRef.value.show(record);
  }

  /**
   * 处理逾期
   */
  function processOverdue(record) {
    // 实现单个逾期处理逻辑
    message.info('处理逾期功能开发中...');
  }

  /**
   * 调整列宽
   */
  function handleResizeColumn(w, col) {
    col.width = w;
  }

  // ----------------------- 辅助函数 ---------------------------

  // 格式化金额，添加千分符，移除¥符号
  function formatAmount(amount) {
    if (!amount && amount !== 0) return '0.00';
    return Number(amount).toLocaleString('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }

  // 格式化统计数字（整数），添加千分位
  function formatStatisticsNumber(number) {
    if (!number && number !== 0) return '0';
    return Number(number).toLocaleString('zh-CN');
  }

  // 手机号脱敏处理
  function maskPhoneNumber(phone) {
    if (!phone) return '-';
    
    // 去除所有非数字字符
    const cleanPhone = phone.toString().replace(/\D/g, '');
    
    // 如果不是11位手机号，直接返回原值
    if (cleanPhone.length !== 11) {
      return phone;
    }
    
    // 格式化为 137****1999
    return `${cleanPhone.substring(0, 3)}****${cleanPhone.substring(7)}`;
  }

  /**
   * 获取代扣解除状态描述
   */
  function getWithholdReleaseDesc(value) {
    return WITHHOLD_RELEASE_ENUM[value]?.desc || value;
  }

  /**
   * 获取收款方式描述
   */
  function getPaymentMethodDesc(value) {
    return PAYMENT_METHOD_ENUM[value]?.desc || value;
  }

  /**
   * 获取订单状态描述
   */
  function getOrderStatusDesc(value) {
    return ORDER_STATUS_ENUM[value]?.desc || value;
  }

  /**
   * 获取状态颜色
   */
  function getStatusColor(status) {
    const colorMap = {
      'WAIT_AUTHORIZ': 'default',   // 待授权 - 灰色
      'EXECUTION_CONTRACT': 'blue', // 履约中 - 蓝色
      'ENDED': 'green',             // 已结束 - 绿色
      'TERMINATED': 'red',          // 已解约 - 红色
      'PAUSED': 'warning',          // 已暂停 - 黄色
      'CANCELED': 'default'         // 已取消 - 灰色
    };
    return colorMap[status] || 'default';
  }

  /**
   * 获取代扣方式描述，与 segment-order-list.vue 保持一致
   */
  function getDeductMethodDesc(value) {
    const enumMap = {
      'ANXIN_DEDUCT': '安心付代扣',
      'BANK_DEDUCT': '银行卡代扣',
      'NONE': '无'
    };
    return enumMap[value] || value || '-';
  }

</script>

<style scoped>
.overdue-statistics {
  margin-bottom: 16px;
  padding: 12px 16px;
  background-color: #fafafa;
  border-radius: 6px;
}

.statistics-text {
  font-size: inherit;
  color: inherit;
  line-height: 1.5;
}

.order-info-cell {
  line-height: 1.6;
}

.user-info-cell {
  line-height: 1.6;
}

.withhold-info-cell {
  line-height: 1.6;
}

.failure-info-cell {
  line-height: 1.6;
}

.text-gray {
  color: inherit;
  font-size: inherit;
}

.smart-table-operate {
  display: flex;
  gap: 8px;
}

.smart-table-operate .ant-btn-link {
  padding: 0;
  height: auto;
}

.order-link {
  color: #1890ff;
  cursor: pointer;
}

.order-link:hover {
  color: #40a9ff;
}

.statistics-number {
  color: #1890ff;
}

.invitation-info-cell {
  line-height: 1.6;
}

/* 筛选区域卡片圆角样式 */
.smart-query-form {
  border-radius: 4px;
}
</style>
 