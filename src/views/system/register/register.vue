<!--
  * 商户注册
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2022-09-12 22:34:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
  *
-->
<template>
  <div class="register-container">
    <!-- 顶部品牌名称 -->
    <div class="brand-header">
      <h1 class="brand-name">Huanlefu</h1>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 页面标题 -->
      <div class="page-title">
        <h2>注册欢乐付商家</h2>
      </div>
      
      <a-form ref="formRef" class="register-form" :model="registerForm" :rules="rules" layout="vertical" autocomplete="off">
        
        <!-- 主体信息 -->
        <div class="form-section">
          <h3 class="section-title">主体信息</h3>
          
          <div class="form-row">
            <!-- 营业执照上传 -->
            <div class="form-col">
              <a-form-item name="businessLicensePic" label="营业执照" required>
                <div class="upload-section">
                  <div class="upload-container">
                    <div v-if="businessLicenseImage" class="uploaded-image">
                      <a-image :src="businessLicenseImage.url" alt="营业执照" />
                      <div class="image-remove" @click="removeBusinessLicense">
                        <close-outlined />
                      </div>
                    </div>
                    <div v-else class="upload-placeholder" @click="triggerBusinessLicenseInput">
                      <div class="upload-icon">
                        <plus-outlined />
                      </div>
                    </div>
                    <input
                      ref="businessLicenseInput"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleBusinessLicenseChange"
                    />
                  </div>
                </div>
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="businessLicenseName" label="营业执照名称" required>
                <a-input v-model:value="registerForm.businessLicenseName" placeholder="请输入营业执照名称" class="custom-input" />
              </a-form-item>
            </div>
            
            <div class="form-col">
              <a-form-item name="socialCreditCode" label="统一社会信用代码" required>
                <a-input v-model:value="registerForm.socialCreditCode" placeholder="请输入统一社会信用代码" class="custom-input" />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="registerCapital" label="注册资本" required>
                <a-input-number v-model:value="registerForm.registerCapital" placeholder="请输入注册资本（例：1000000）" :min="0" :max="999999999" :precision="0" :controls="false" class="custom-input" />
              </a-form-item>
            </div>
            
            <div class="form-col">
              <a-form-item name="storeName" label="门店名称（商户简称）" required>
                <a-input v-model:value="registerForm.storeName" placeholder="请输入门店名称" :maxlength="20" class="custom-input" />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="establishmentDate" label="成立日期" required>
                <a-date-picker
                  v-model:value="registerForm.establishmentDate"
                  placeholder="请选择成立日期"
                  class="custom-input"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledEstablishmentDate"
                />
              </a-form-item>
            </div>
          </div>
          
          <!-- 门店LOGO上传 -->
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="storeLogo" label="门店LOGO" required>
                <div class="upload-section">
                  <div class="upload-container small">
                    <div v-if="storeLogoImage" class="uploaded-image">
                      <a-image :src="storeLogoImage.url" alt="门店Logo" />
                      <div class="image-remove" @click="removeStoreLogo">
                        <close-outlined />
                      </div>
                    </div>
                    <div v-else class="upload-placeholder" @click="triggerStoreLogoInput">
                      <div class="upload-icon">
                        <plus-outlined />
                      </div>
                    </div>
                    <input
                      ref="storeLogoInput"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleStoreLogoChange"
                    />
                  </div>
                </div>
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="businessCategory" label="经营类目" required>
                <a-select v-model:value="registerForm.businessCategory" placeholder="请选择经营类目" class="custom-select">
                  <a-select-option value="9902">数字娱乐</a-select-option>
                  <a-select-option value="9910">互联网金融</a-select-option>
                  <a-select-option value="9911">保险</a-select-option>
                  <a-select-option value="9923">航旅</a-select-option>
                  <a-select-option value="9926">消费金融</a-select-option>
                  <a-select-option value="9927">基金</a-select-option>
                  <a-select-option value="9928">大宗商品现货交易</a-select-option>
                  <a-select-option value="9929">收藏品交易</a-select-option>
                  <a-select-option value="9930">电商B2B</a-select-option>
                  <a-select-option value="9931">零售B2C</a-select-option>
                  <a-select-option value="9937">代理商</a-select-option>
                  <a-select-option value="9938">物流</a-select-option>
                  <a-select-option value="9940">金融租赁</a-select-option>
                  <a-select-option value="9941">票据</a-select-option>
                  <a-select-option value="9943">教育培训</a-select-option>
                  <a-select-option value="9946">公共服务及便民服务</a-select-option>
                  <a-select-option value="9980">跨境服务贸易</a-select-option>
                  <a-select-option value="9981">跨境货物贸易</a-select-option>
                  <a-select-option value="9982">货币代兑</a-select-option>
                  <a-select-option value="9983">金融机构</a-select-option>
                  <a-select-option value="9990">征信</a-select-option>
                  <a-select-option value="10012">有偿资讯</a-select-option>
                  <a-select-option value="9999">其他</a-select-option>
                </a-select>
              </a-form-item>
            </div>
            
            <div class="form-col">
              <a-form-item name="qualificationDocument" label="资质文件" required>
                <a-select v-model:value="registerForm.qualificationDocument" placeholder="请选择资质文件" class="custom-select">
                  <a-select-option :value="1">《事业单位法人证书》并且证件业务范围包含：培训、辅导、教育、咨询、活动策划、体育赛事组织策划、健康管理</a-select-option>
                  <a-select-option :value="2">《办学许可证》并且业务范围包含：培训、辅导、教育、咨询、活动策划、体育赛事组织策划、健康管理</a-select-option>
                  <a-select-option :value="3">《民办非企业单位登记证书》并且证件主体名称包括：培训、辅导、教育、学校；或经营范围包含：培训、辅导、教育、训练、少儿、青少年、文化、艺术、舞蹈、体育、游泳、武术、咨询、活动策划、赛事组织策划、健康管理业务；且不包含"少年宫、青少年活动中心"</a-select-option>
                  <a-select-option :value="4">《社会服务机构登记证书》并且证件主体名称包括：培训、辅导、教育、学校；或经营范围包含：培训、辅导、教育、训练、少儿、青少年、文化、艺术、舞蹈、体育、游泳、武术、咨询、活动策划、赛事组织策划、健康管理业务；且不包含"少年宫、青少年活动中心"</a-select-option>
                  <a-select-option :value="5">营业执照经营范围包含：培训、辅导、教育、咨询、活动策划、体育赛事组织策划、健康管理等业务</a-select-option>
                </a-select>
              </a-form-item>
            </div>
          </div>
          
          <!-- 资质文件照片上传 -->
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="qualificationPic" label="资质文件照片（原件）" required>
                <div class="upload-section">
                  <div class="upload-container">
                    <div v-if="qualificationPicImage" class="uploaded-image">
                      <a-image :src="qualificationPicImage.url" alt="资质文件照片" />
                      <div class="image-remove" @click="removeQualificationPic">
                        <close-outlined />
                      </div>
                    </div>
                    <div v-else class="upload-placeholder" @click="triggerQualificationPicInput">
                      <div class="upload-icon">
                        <plus-outlined />
                      </div>
                    </div>
                    <input
                      ref="qualificationPicInput"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleQualificationPicChange"
                    />
                  </div>
                </div>
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 法人信息 -->
        <div class="form-section">
          <h3 class="section-title">法人信息</h3>
          
          <div class="form-row upload-row">
            <!-- 身份证正面 -->
            <div class="form-col upload-col">
              <a-form-item name="corporateIdcardFrontPic" label="法人身份证人像面" required>
                <div class="upload-section">
                  <div class="upload-container">
                    <div v-if="idCardFrontImage" class="uploaded-image">
                      <a-image :src="idCardFrontImage.url" alt="身份证正面" />
                      <div class="image-remove" @click="removeIdCardFront">
                        <close-outlined />
                      </div>
                    </div>
                    <div v-else class="upload-placeholder" @click="triggerIdCardFrontInput">
                      <div class="upload-icon">
                        <plus-outlined />
                      </div>
                    </div>
                    <input
                      ref="idCardFrontInput"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleIdCardFrontChange"
                    />
                  </div>
                </div>
              </a-form-item>
            </div>
            
            <!-- 身份证反面 -->
            <div class="form-col upload-col">
              <a-form-item name="corporateIdcardBackPic" label="法人身份证国徽面" required>
                <div class="upload-section">
                  <div class="upload-container">
                    <div v-if="idCardBackImage" class="uploaded-image">
                      <a-image :src="idCardBackImage.url" alt="身份证反面" />
                      <div class="image-remove" @click="removeIdCardBack">
                        <close-outlined />
                      </div>
                    </div>
                    <div v-else class="upload-placeholder" @click="triggerIdCardBackInput">
                      <div class="upload-icon">
                        <plus-outlined />
                      </div>
                    </div>
                    <input
                      ref="idCardBackInput"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleIdCardBackChange"
                    />
                  </div>
                </div>
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="corporateName" label="法人姓名" required>
                <a-input v-model:value="registerForm.corporateName" placeholder="请输入法人姓名" autocomplete="name" class="custom-input" />
              </a-form-item>
            </div>
            
            <div class="form-col">
              <a-form-item name="corporateCertId" label="法人身份证号" required>
                <a-input v-model:value="registerForm.corporateCertId" placeholder="请输入法人身份证号" autocomplete="off" class="custom-input" />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="corporateIdcardValidityPeriod" label="法人证件有效期" required>
                <a-date-picker
                  v-model:value="registerForm.corporateIdcardValidityPeriod"
                  placeholder="请选择法人证件有效期"
                  class="custom-input"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                  :disabled-date="disabledDate"
                />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="corporateMobile" label="法人手机号" required>
                <a-input v-model:value="registerForm.corporateMobile" placeholder="请输入法人手机号" autocomplete="tel" class="custom-input" />
              </a-form-item>
            </div>
            
            <div class="form-col">
              <a-form-item name="smsCode" label="短信验证码" required>
                <div class="sms-input-group">
                  <a-input 
                    v-model:value="registerForm.smsCode" 
                    placeholder="请输入短信验证码"
                    type="text"
                    :maxlength="6"
                    @input="handleSmsCodeInput"
                    class="custom-input sms-input"
                  />
                  <a-button @click="sendSmsCode" class="sms-btn" type="link" :disabled="smsCodeButtonDisabled">
                    {{ smsCodeButtonDisabled ? smsCodeTips : '获取' }}
                  </a-button>
                </div>
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 经营信息 -->
        <div class="form-section">
          <h3 class="section-title">经营信息</h3>
          
          <div class="form-row upload-row">
            <!-- 门头照 -->
            <div class="form-col upload-col">
              <a-form-item name="doorPhoto" label="门头照" required>
                <div class="upload-section">
                  <div class="upload-container">
                    <div v-if="storeFrontPhotoImage" class="uploaded-image">
                      <a-image :src="storeFrontPhotoImage.url" alt="门头照" />
                      <div class="image-remove" @click="removeStoreFrontPhoto">
                        <close-outlined />
                      </div>
                    </div>
                    <div v-else class="upload-placeholder" @click="triggerStoreFrontPhotoInput">
                      <div class="upload-icon">
                        <plus-outlined />
                      </div>
                    </div>
                    <input
                      ref="storeFrontPhotoInput"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleStoreFrontPhotoChange"
                    />
                  </div>
                </div>
              </a-form-item>
            </div>
            
            <!-- 内景照 -->
            <div class="form-col upload-col">
              <a-form-item name="interiorPhone" label="内景照" required>
                <div class="upload-section">
                  <div class="upload-container">
                    <div v-if="interiorPhotoImage" class="uploaded-image">
                      <a-image :src="interiorPhotoImage.url" alt="内景照" />
                      <div class="image-remove" @click="removeInteriorPhoto">
                        <close-outlined />
                      </div>
                    </div>
                    <div v-else class="upload-placeholder" @click="triggerInteriorPhotoInput">
                      <div class="upload-icon">
                        <plus-outlined />
                      </div>
                    </div>
                    <input
                      ref="interiorPhotoInput"
                      type="file"
                      accept="image/*"
                      style="display: none"
                      @change="handleInteriorPhotoChange"
                    />
                  </div>
                </div>
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="cascaderAddress" label="省市区" required>
                <a-cascader
                  v-model:value="registerForm.cascaderAddress"
                  :options="addressOptions"
                  placeholder="请选择省市区"
                  class="custom-select"
                  @change="handleAddressChange"
                />
              </a-form-item>
            </div>
            
            <div class="form-col">
              <a-form-item name="storeAddress" label="详细地址" required>
                <a-input v-model:value="registerForm.storeAddress" placeholder="请输入门店详细地址，精确到街道门牌号" class="custom-input" />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="contactPhone" label="联系电话" required>
                <a-input v-model:value="registerForm.contactPhone" placeholder="请输入门店客服电话" autocomplete="tel" class="custom-input" />
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 管理员信息 -->
        <div class="form-section">
          <h3 class="section-title">管理员信息</h3>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="adminName" label="管理员姓名" required>
                <a-input v-model:value="registerForm.adminName" placeholder="请输入管理员姓名" autocomplete="name" class="custom-input" />
              </a-form-item>
            </div>
            
            <div class="form-col">
              <a-form-item name="adminPhone" label="管理员手机号" required>
                <a-input v-model:value="registerForm.adminPhone" placeholder="请输入管理员手机号" autocomplete="username" class="custom-input" />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="adminEmail" label="管理员邮箱" required>
                <a-input v-model:value="registerForm.adminEmail" placeholder="请输入管理员邮箱" autocomplete="email" class="custom-input" />
              </a-form-item>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-col">
              <a-form-item name="loginPassword" label="登录密码" required>
                <a-input-password v-model:value="registerForm.loginPassword" placeholder="请输入登录密码（8-20位字母数字组合）" autocomplete="new-password" class="custom-input" />
              </a-form-item>
            </div>
            
            <div class="form-col">
              <a-form-item name="repeatLoginPassword" label="重复登录密码" required>
                <a-input-password v-model:value="registerForm.repeatLoginPassword" placeholder="请再次输入登录密码" autocomplete="new-password" class="custom-input" />
              </a-form-item>
            </div>
          </div>
        </div>

        <!-- 提交按钮 -->
        <div class="submit-section">
          <a-button type="primary" @click="onRegister" class="submit-btn" size="large">
            立即注册
          </a-button>
          
          <div class="divider">
            <span class="divider-text">OR</span>
          </div>
          
          <a-button @click="goToLogin" class="login-btn" size="large">
            已有账号，立即登录
          </a-button>
        </div>
      </a-form>
      
      <!-- 底部版权信息 -->
      <div class="copyright">
        Copyright © 四川蓉通达资讯科技有限公司 丨 蜀ICP备2025143009号-4
      </div>
    </div>
  </div>
</template>

<script setup>
  import { message } from 'ant-design-vue';
  import { reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { PlusOutlined, CloseOutlined } from '@ant-design/icons-vue';
  import { registerApi } from '/@/api/system/register-api';
  import { fileApi } from '/@/api/support/file-api';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import cascaderAddressOptions from '/@/utils/cascader-address-options';
  import dayjs from 'dayjs';

  const router = useRouter();

  // 注册表单 - 与API接口字段对应
  const registerForm = reactive({
    // 门店信息
    storeName: '', // 门店名称(商户简称)
    storeProvinceNo: '', // 省编码
    storeProvince: '', // 省名称
    storeCityNo: '', // 市编码
    storeCity: '', // 市名称
    storeCountyNo: '', // 县区编码
    storeCounty: '', // 县区名称
    storeAddress: '', // 详细地址
    storeLogo: '', // 门店logo (可选)
    doorPhoto: '', // 门头照
    interiorPhone: '', // 内景照
    
    // 主体信息
    registerCapital: null, // 注册资本 (整数)
    establishmentDate: null, // 成立日期
    businessLicensePic: '', // 营业执照
    businessLicenseName: '', // 营业执照名称
    socialCreditCode: '', // 统一社会信用代码
    businessCategory: undefined, // 经营类目
    qualificationDocument: undefined, // 资质文件
    qualificationPic: '', // 资质文件照片（原件）
    
    // 法人信息
    corporateIdcardFrontPic: '', // 法人身份证人像面
    corporateIdcardBackPic: '', // 法人身份证国徽面
    corporateName: '', // 法人姓名
    corporateCertId: '', // 法人身份证号
    corporateMobile: '', // 法人手机号
    corporateIdcardValidityPeriod: null, // 法人证件有效期
    
    // 管理员信息
    adminName: '', // 管理员姓名
    adminPhone: '', // 管理员手机号
    adminEmail: '', // 管理员邮箱
    loginPassword: '', // 登录密码
    contactPhone: '', // 联系电话
    
    // 前端辅助字段
    repeatLoginPassword: '', // 确认密码（API字段）
    smsCode: '', // 短信验证码（前端验证用）
    cascaderAddress: [], // 地址选择器绑定字段
    
    // 短信验证码相关（API返回，提交时需要）
    smsUuid: '', // 短信验证码唯一标识
    apiSmsCode: '' // API返回的短信验证码（临时解决方案）
  });

  // 图片数据
  const businessLicenseImage = ref(null);
  const qualificationPicImage = ref(null);
  const idCardFrontImage = ref(null);
  const idCardBackImage = ref(null);
  const storeFrontPhotoImage = ref(null);
  const interiorPhotoImage = ref(null);
  const storeLogoImage = ref(null);

  // 文件输入框引用
  const businessLicenseInput = ref(null);
  const qualificationPicInput = ref(null);
  const idCardFrontInput = ref(null);
  const idCardBackInput = ref(null);
  const storeFrontPhotoInput = ref(null);
  const interiorPhotoInput = ref(null);
  const storeLogoInput = ref(null);

  // 验证规则
  const rules = {
    // 门店信息
    storeName: [
      { required: true, message: '请输入门店名称' },
      { min: 2, max: 20, message: '门店名称长度应为2-20个字符' }
    ],
    storeLogo: [{ required: true, message: '请上传门店Logo' }],
    cascaderAddress: [{ required: true, message: '请选择门店地址' }],
    storeAddress: [{ required: true, message: '请输入详细地址' }],
    doorPhoto: [{ required: true, message: '请上传门头照' }],
    interiorPhone: [{ required: true, message: '请上传内景照' }],
    
    // 主体信息
    registerCapital: [
      { required: true, message: '请输入注册资本' },
      { 
        validator: (rule, value) => {
          if (value && value.toString().length > 9) {
            return Promise.reject('注册资本最多9位数');
          }
          return Promise.resolve();
        }
      }
    ],
    establishmentDate: [{ required: true, message: '请选择成立日期' }],
    businessLicensePic: [{ required: true, message: '请上传营业执照' }],
    businessLicenseName: [{ required: true, message: '请输入营业执照名称' }],
    socialCreditCode: [{ required: true, message: '请输入统一社会信用代码' }],
    businessCategory: [{ required: true, message: '请选择经营类目' }],
    qualificationDocument: [{ required: true, message: '请选择资质文件' }],
    qualificationPic: [{ required: true, message: '请上传资质文件照片' }],
    
    // 法人信息
    corporateIdcardFrontPic: [{ required: true, message: '请上传法人身份证人像面' }],
    corporateIdcardBackPic: [{ required: true, message: '请上传法人身份证国徽面' }],
    corporateName: [{ required: true, message: '请输入法人姓名' }],
    corporateCertId: [{ required: true, message: '请输入法人身份证号' }],
    corporateMobile: [{ required: true, message: '请输入法人手机号' }],
    corporateIdcardValidityPeriod: [{ required: true, message: '请选择法人证件有效期' }],
    smsCode: [{ required: true, message: '请输入短信验证码' }],
    
    // 管理员信息
    adminName: [{ required: true, message: '请输入管理员姓名' }],
    adminPhone: [{ required: true, message: '请输入管理员手机' }],
    adminEmail: [
      { required: true, message: '请输入管理员邮箱' },
      { type: 'email', message: '请输入正确的邮箱格式' }
    ],
    loginPassword: [{ required: true, message: '请输入登录密码' }],
    contactPhone: [{ required: true, message: '请输入联系电话' }],
    repeatLoginPassword: [
      { required: true, message: '请确认密码' },
      {
        validator: (rule, value) => {
          if (value && value !== registerForm.loginPassword) {
            return Promise.reject('两次输入的密码不一致');
          }
          return Promise.resolve();
        }
      }
    ]
  };

  const formRef = ref();
  
  const addressOptions = cascaderAddressOptions;

  // 地址选择器变化处理
  function handleAddressChange(value, selectedOptions) {
    if (selectedOptions && selectedOptions.length >= 3) {
      registerForm.storeProvinceNo = selectedOptions[0].value;
      registerForm.storeProvince = selectedOptions[0].label;
      registerForm.storeCityNo = selectedOptions[1].value;
      registerForm.storeCity = selectedOptions[1].label;
      registerForm.storeCountyNo = selectedOptions[2].value;
      registerForm.storeCounty = selectedOptions[2].label;
    }
  }

  // 触发营业执照文件选择
  function triggerBusinessLicenseInput() {
    businessLicenseInput.value?.click();
  }

  // 处理营业执照文件选择
  async function handleBusinessLicenseChange(event) {
    const file = handleFileSelect(event);
    if (file) {
      createImageData(file, async (imageData) => {
        businessLicenseImage.value = imageData;
        // 立即上传文件
        try {
          SmartLoading.show();
          const url = await uploadFile(file);
          registerForm.businessLicensePic = url;
          message.success('营业执照上传成功');
        } catch (error) {
          console.error('营业执照上传失败:', error);
          message.error('营业执照上传失败');
          // 上传失败时清除图片
          businessLicenseImage.value = null;
          registerForm.businessLicensePic = '';
        } finally {
          SmartLoading.hide();
        }
      });
    }
  }

  // 删除营业执照
  function removeBusinessLicense() {
    businessLicenseImage.value = null;
    registerForm.businessLicensePic = '';
  }

  // 资质文件照片相关
  function triggerQualificationPicInput() {
    qualificationPicInput.value?.click();
  }

  async function handleQualificationPicChange(event) {
    const file = handleFileSelect(event);
    if (file) {
      createImageData(file, async (imageData) => {
        qualificationPicImage.value = imageData;
        // 立即上传文件
        try {
          SmartLoading.show();
          const url = await uploadFile(file);
          registerForm.qualificationPic = url;
          message.success('资质文件照片上传成功');
        } catch (error) {
          console.error('资质文件照片上传失败:', error);
          message.error('资质文件照片上传失败');
          qualificationPicImage.value = null;
          registerForm.qualificationPic = '';
        } finally {
          SmartLoading.hide();
        }
      });
    }
  }

  function removeQualificationPic() {
    qualificationPicImage.value = null;
    registerForm.qualificationPic = '';
  }

  // 身份证正面相关
  function triggerIdCardFrontInput() {
    idCardFrontInput.value?.click();
  }

  async function handleIdCardFrontChange(event) {
    const file = handleFileSelect(event);
    if (file) {
      createImageData(file, async (imageData) => {
        idCardFrontImage.value = imageData;
        // 立即上传文件
        try {
          SmartLoading.show();
          const url = await uploadFile(file);
          registerForm.corporateIdcardFrontPic = url;
          message.success('身份证正面上传成功');
        } catch (error) {
          console.error('身份证正面上传失败:', error);
          message.error('身份证正面上传失败');
          idCardFrontImage.value = null;
          registerForm.corporateIdcardFrontPic = '';
        } finally {
          SmartLoading.hide();
        }
      });
    }
  }

  function removeIdCardFront() {
    idCardFrontImage.value = null;
    registerForm.corporateIdcardFrontPic = '';
  }

  // 身份证反面相关
  function triggerIdCardBackInput() {
    idCardBackInput.value?.click();
  }

  async function handleIdCardBackChange(event) {
    const file = handleFileSelect(event);
    if (file) {
      createImageData(file, async (imageData) => {
        idCardBackImage.value = imageData;
        // 立即上传文件
        try {
          SmartLoading.show();
          const url = await uploadFile(file);
          registerForm.corporateIdcardBackPic = url;
          message.success('身份证反面上传成功');
        } catch (error) {
          console.error('身份证反面上传失败:', error);
          message.error('身份证反面上传失败');
          idCardBackImage.value = null;
          registerForm.corporateIdcardBackPic = '';
        } finally {
          SmartLoading.hide();
        }
      });
    }
  }

  function removeIdCardBack() {
    idCardBackImage.value = null;
    registerForm.corporateIdcardBackPic = '';
  }

  // 门头照相关
  function triggerStoreFrontPhotoInput() {
    storeFrontPhotoInput.value?.click();
  }

  async function handleStoreFrontPhotoChange(event) {
    const file = handleFileSelect(event);
    if (file) {
      createImageData(file, async (imageData) => {
        storeFrontPhotoImage.value = imageData;
        // 立即上传文件
        try {
          SmartLoading.show();
          const url = await uploadFile(file);
          registerForm.doorPhoto = url;
          message.success('门头照上传成功');
        } catch (error) {
          console.error('门头照上传失败:', error);
          message.error('门头照上传失败');
          storeFrontPhotoImage.value = null;
          registerForm.doorPhoto = '';
        } finally {
          SmartLoading.hide();
        }
      });
    }
  }

  function removeStoreFrontPhoto() {
    storeFrontPhotoImage.value = null;
    registerForm.doorPhoto = '';
  }

  // 内景照相关
  function triggerInteriorPhotoInput() {
    interiorPhotoInput.value?.click();
  }

  async function handleInteriorPhotoChange(event) {
    const file = handleFileSelect(event);
    if (file) {
      createImageData(file, async (imageData) => {
        interiorPhotoImage.value = imageData;
        // 立即上传文件
        try {
          SmartLoading.show();
          const url = await uploadFile(file);
          registerForm.interiorPhone = url;
          message.success('内景照上传成功');
        } catch (error) {
          console.error('内景照上传失败:', error);
          message.error('内景照上传失败');
          interiorPhotoImage.value = null;
          registerForm.interiorPhone = '';
        } finally {
          SmartLoading.hide();
        }
      });
    }
  }

  function removeInteriorPhoto() {
    interiorPhotoImage.value = null;
    registerForm.interiorPhone = '';
  }

  // 门店Logo相关
  function triggerStoreLogoInput() {
    storeLogoInput.value?.click();
  }

  async function handleStoreLogoChange(event) {
    const file = handleFileSelect(event);
    if (file) {
      createImageData(file, async (imageData) => {
        storeLogoImage.value = imageData;
        // 立即上传文件
        try {
          SmartLoading.show();
          const url = await uploadFile(file);
          registerForm.storeLogo = url;
          message.success('门店Logo上传成功');
        } catch (error) {
          console.error('门店Logo上传失败:', error);
          message.error('门店Logo上传失败');
          storeLogoImage.value = null;
          registerForm.storeLogo = '';
        } finally {
          SmartLoading.hide();
        }
      });
    }
  }

  function removeStoreLogo() {
    storeLogoImage.value = null;
    registerForm.storeLogo = '';
  }

  // 通用文件选择处理
  function handleFileSelect(event) {
    const files = event.target.files;
    if (!files || files.length === 0) return null;
    
    const file = files[0];
    
    // 验证文件类型
    if (!file.type.startsWith('image/')) {
      message.error('请选择图片文件');
      return null;
    }
    
    // 验证文件大小（限制为5MB）
    const maxSize = 5 * 1024 * 1024;
    if (file.size > maxSize) {
      message.error('文件大小不能超过5MB');
      return null;
    }
    
    // 清空input值，允许重复选择同一文件
    event.target.value = '';
    
    return file;
  }

  // 创建图片数据对象
  function createImageData(file, callback) {
    const reader = new FileReader();
    reader.onload = (e) => {
      const imageData = {
        url: e.target.result,
        name: file.name,
        size: file.size,
        file: file
      };
      callback(imageData);
    };
    reader.readAsDataURL(file);
  }

  // 日期禁用函数
  function disabledDate(current) {
    // 禁用今天之前的日期
    return current && current < dayjs().startOf('day');
  }

  // 成立日期禁用函数
  function disabledEstablishmentDate(current) {
    // 禁用今天之后的日期，成立日期不能是未来
    return current && current > dayjs().endOf('day');
  }

  // 短信验证码
  const smsCodeTips = ref('');
  const smsCodeButtonDisabled = ref(false);

  // 处理验证码输入，只允许数字
  function handleSmsCodeInput(event) {
    const value = event.target.value;
    // 只保留数字字符
    const numericValue = value.replace(/[^0-9]/g, '');
    registerForm.smsCode = numericValue;
  }

  function sendSmsCode() {
    if (!registerForm.corporateMobile) {
      message.error('请先输入法人手机号');
      return;
    }
    
    SmartLoading.show();
    registerApi.sendSmsCode(registerForm.corporateMobile).then((response) => {
      if (response.data) {
        // 保存API返回的短信验证码信息
        registerForm.smsUuid = response.data.smsUuid || '';
        registerForm.apiSmsCode = response.data.smsCode || '';
        
        // 临时解决方案：自动填入短信验证码
        if (registerForm.apiSmsCode) {
          registerForm.smsCode = registerForm.apiSmsCode;
          message.success(`验证码发送成功，临时验证码：${registerForm.apiSmsCode}`);
        } else {
          message.success('验证码发送成功');
        }
        
        runCountDown();
      }
    }).catch((error) => {
      console.error('验证码发送失败:', error);
      message.error('验证码发送失败');
    }).finally(() => {
      SmartLoading.hide();
    });
  }

  function runCountDown() {
    smsCodeButtonDisabled.value = true;
    let countDown = 60;
    smsCodeTips.value = `${countDown}秒后重新获取`;
    const timer = setInterval(() => {
      if (countDown > 1) {
        countDown--;
        smsCodeTips.value = `${countDown}秒后重新获取`;
      } else {
        clearInterval(timer);
        smsCodeButtonDisabled.value = false;
        smsCodeTips.value = '';
      }
    }, 1000);
  }

  // 文件上传辅助函数
  async function uploadFile(file, folder = 1) {
    if (!file) {
      return '';
    }
    
    const formData = new FormData();
    formData.append('file', file);
    
    try {
      const response = await fileApi.uploadFile(formData, folder);
      return response.data.fileUrl || '';
    } catch (error) {
      console.error('文件上传失败:', error);
      throw new Error('文件上传失败');
    }
  }

  // 注册提交
  async function onRegister() {
    try {
      await formRef.value.validate();
      SmartLoading.show();
      
      // 构建提交数据（图片URL已经在选择时上传完成）
      const submitData = {
        // 门店基本信息
        storeName: registerForm.storeName,                     // 门店名称(商户简称) - 支付宝账单中显示的商户名称
        storeProvinceNo: registerForm.storeProvinceNo,         // 省编码 - 如"11"(北京市)
        storeProvince: registerForm.storeProvince,             // 省名称 - 如"北京市"
        storeCityNo: registerForm.storeCityNo,                 // 市编码 - 如"1101"(市辖区)
        storeCity: registerForm.storeCity,                     // 市名称 - 如"市辖区"
        storeCountyNo: registerForm.storeCountyNo,             // 县区编码 - 如"110102"(西城区)
        storeCounty: registerForm.storeCounty,                 // 县区名称 - 如"西城区"
        storeAddress: registerForm.storeAddress,               // 详细地址 - 具体的门店地址信息
        storeLogo: registerForm.storeLogo,                     // 门店logo - 可选，门店标识图片
        doorPhoto: registerForm.doorPhoto,                     // 门头照 - 必需，门店外观照片
        interiorPhone: registerForm.interiorPhone,            // 内景照 - 必需，门店内部照片
        
        // 企业主体信息
        registerCapital: registerForm.registerCapital,        // 注册资本 - 营业执照上的注册资本金额(万元)
        establishmentDate: registerForm.establishmentDate ? (typeof registerForm.establishmentDate === 'string' ? registerForm.establishmentDate : registerForm.establishmentDate.format('YYYY-MM-DD')) : '', // 成立日期 - 营业执照上的成立日期
        businessLicensePic: registerForm.businessLicensePic,  // 营业执照 - 营业执照照片
        businessLicenseName: registerForm.businessLicenseName, // 营业执照名称 - 营业执照上的企业名称
        socialCreditCode: registerForm.socialCreditCode,       // 统一社会信用代码 - 18位企业唯一标识
        businessCategory: registerForm.businessCategory,       // 经营类目 - 企业经营范围分类编码
        qualificationDocument: registerForm.qualificationDocument, // 资质文件类型 - 如1:事业单位法人证书, 2:办学许可证等
        qualificationPic: registerForm.qualificationPic,      // 资质文件照片 - 对应资质文件类型的证件照片
        
        // 法人信息
        corporateIdcardFrontPic: registerForm.corporateIdcardFrontPic, // 法人身份证人像面 - 法人身份证正面照片
        corporateIdcardBackPic: registerForm.corporateIdcardBackPic,   // 法人身份证国徽面 - 法人身份证背面照片
        corporateName: registerForm.corporateName,            // 法人姓名 - 企业法定代表人姓名
        corporateCertId: registerForm.corporateCertId,        // 法人身份证号 - 法定代表人身份证号码
        corporateMobile: registerForm.corporateMobile,        // 法人手机号 - 法定代表人联系手机号
        corporateIdcardValidityPeriod: registerForm.corporateIdcardValidityPeriod ? (typeof registerForm.corporateIdcardValidityPeriod === 'string' ? registerForm.corporateIdcardValidityPeriod : registerForm.corporateIdcardValidityPeriod.format('YYYY-MM-DD')) : '', // 法人证件有效期 - 法定代表人身份证有效期
        
        // 成立日期
        establishmentDate: registerForm.establishmentDate ? (typeof registerForm.establishmentDate === 'string' ? registerForm.establishmentDate : registerForm.establishmentDate.format('YYYY-MM-DD')) : '',
        
        // 管理员账户信息
        adminName: registerForm.adminName,                    // 管理员姓名 - 系统管理员真实姓名
        adminPhone: registerForm.adminPhone,                  // 管理员手机号 - 系统管理员联系电话
        adminEmail: registerForm.adminEmail,                  // 管理员邮箱 - 系统管理员邮箱地址
        loginPassword: registerForm.loginPassword,            // 登录密码 - 管理员账户登录密码
        contactPhone: registerForm.contactPhone,              // 联系电话 - 门店对外联系电话
        
        // 短信验证码相关
        smsUuid: registerForm.smsUuid,                        // 短信验证码uuid - 验证码唯一标识
        smsCode: registerForm.smsCode,                        // 短信验证码 - 6位数字验证码
        
        // 确认密码
        repeatLoginPassword: registerForm.repeatLoginPassword // 重复密码 - 与登录密码保持一致
      };
      
      console.log('提交数据:', submitData);
      
      await registerApi.register(submitData);
      message.success('注册申请提交成功，请等待审核！');
      
      // 跳转到登录页面
      router.push('/login');
      
    } catch (error) {
      console.error('注册失败:', error);
      message.error('注册失败，请检查信息后重试');
    } finally {
      SmartLoading.hide();
    }
  }

  // 返回登录
  function goToLogin() {
    router.push('/login');
  }
</script>

<style lang="less" scoped>
  @import './register.less';
</style> 