.register-container {
  min-height: 100vh;
  background: linear-gradient(180deg, #C4E6F5 0%, rgba(196, 230, 245, 0) 100%);
  padding: 0;
  overflow-x: hidden;
}

.brand-header {
  padding: 20px 40px;
  
  .brand-name {
    font-family: 'Poetsen One', sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
  }
}

.main-content {
  max-width: 1000px;
  margin: 0 auto;
  padding: 40px 60px 40px;
}

.page-title {
  text-align: left;
  margin-bottom: 60px;
  
  h2 {
    font-size: 34px;
      font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
  }
    }
    
    .register-form {
  background: transparent;
}

      .form-section {
  margin-bottom: 36px;
        
        .section-title {
          font-size: 16px;
          font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0 0 24px 0;
  }
}

.form-row {
  display: flex;
  gap: 100px;
  margin-bottom: 32px;
  
  .form-col {
    width: 400px;
    flex: none;
  }
  
  &.upload-row {
    gap: 24px;
    
    .upload-col {
      width: 150px;
    }
  }
}

.custom-input,
.custom-select {
  width: 400px;
  height: 48px;
  border-radius: 100px;
  background: #FFFFFF;
  border: 1px solid transparent;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  box-shadow: none;
  padding: 0 20px !important;
  
  &::placeholder {
    color: rgba(0, 0, 0, 0.3);
  }
        
        &:focus,
        &:hover {
    border-color: #1748FD;
    box-shadow: none;
    outline: none;
  }
}

.custom-select {
  height: 48px;
  border-radius: 100px;
  background: #FFFFFF;
  border: 1px solid transparent;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  box-shadow: none;
  padding: 0 20px !important;
  
        &:hover {
    border-color: #1748FD;
    box-shadow: none;
  }
  
  &.ant-select-focused {
    border-color: #1748FD;
    box-shadow: none;
    outline: none;
  }
  
  :deep(.ant-select-selector) {
    height: 48px !important;
    border-radius: 100px !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    
    .ant-select-selection-placeholder {
      color: rgba(0, 0, 0, 0.3);
      line-height: 46px;
    }
    
    .ant-select-selection-item {
      line-height: 46px;
    }
  }
}

.sms-input-group {
  display: flex;
  align-items: center;
  position: relative;
  
  .sms-input {
    padding-right: 80px !important;
    padding-left: 20px !important;
  }
  
  .sms-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    height: auto;
    padding: 0;
    color: #1748FD;
    font-size: 14px;
    font-weight: 400;
    border: none;
    background: none;
    
        &:hover {
      color: #1748FD;
    }
    
    &:disabled {
      color: rgba(0, 0, 0, 0.3);
    }
  }
}

.upload-section {
  .upload-container {
    width: 150px;
    height: 100px;
    border-radius: 6px;
    background: #FFFFFF;
    border: 1px dashed #D8D8D8;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    position: relative;
    margin-bottom: 12px;
  
    &.small {
      width: 100px;
      height: 100px;
    }
    
    &:hover {
      border-color: #1748FD;
      background: #FFFFFF;
    }
    
    .upload-placeholder {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      height: 100%;
      
      .upload-icon {
        font-size: 16px;
        color: #D8D8D8;
      }
    }
    
    .uploaded-image {
      width: 100%;
      height: 100%;
      position: relative;
      border-radius: 6px;
      overflow: hidden;
      
      :deep(.ant-image) {
        width: 100%;
        height: 100%;
        
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
      
      .image-remove {
        position: absolute;
        top: 4px;
        right: 4px;
        width: 16px;
        height: 16px;
        background: rgba(0, 0, 0, 0.5);
        color: white;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        font-size: 10px;
        
        &:hover {
          background: rgba(0, 0, 0, 0.7);
        }
      }
    }
        }
        
  .upload-tip {
          font-size: 12px;
    color: rgba(0, 0, 0, 0.3);
    line-height: 1.4;
        }
      }
      
      .submit-section {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  margin-top: 60px;
        
        .submit-btn {
    width: 400px;
    height: 48px;
    border-radius: 100px;
    background: #1748FD;
    border: none;
    font-size: 14px;
    font-weight: 600;
    color: #FFFFFF;
    margin-bottom: 32px;
    
    &:hover {
      background: #1748FD;
      opacity: 0.9;
    }
  }
  
  .divider {
    width: 100%;
    max-width: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 0 32px 0;
    position: relative;
    
    &::before,
    &::after {
      content: '';
      display: block;
      width: 170px;
      height: 1px;
      background: rgba(0, 0, 0, 0.08);
    }
    
    .divider-text {
      font-family: 'Poetsen One', sans-serif;
      font-size: 14px;
      color: rgba(0, 0, 0, 0.85);
      padding: 0 20px 0 21px;
      background: transparent;
    }
  }

  .login-btn {
    width: 400px;
    height: 48px;
    border-radius: 100px;
    background: transparent;
    border: 2px dashed rgba(0, 0, 0, 0.85);
    font-size: 14px;
          font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
          
          &:hover {
      border-color: #1748FD;
      color: #1748FD;
          }
        }
      }
      
.copyright {
        text-align: center;
  margin-top: 60px;
  font-family: 'SF Pro', sans-serif;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.3);
}

// 响应式设计
@media (max-width: 768px) {
  .main-content {
    padding: 0 20px 20px;
  }
  
  .form-row {
    flex-direction: column;
    gap: 16px;
  }
  
  .submit-section {
    .submit-btn,
    .login-btn {
      width: 100%;
    }
  }
}

// 表单验证错误样式
:deep(.ant-form-item-has-error) {
  .custom-input,
  .custom-select .ant-select-selector,
  .ant-cascader .ant-cascader-selector,
  .ant-input-number,
  .ant-input-password {
    border-color: #FA5151 !important;
    box-shadow: none !important;
    background: #FFFFFF !important;
  }
}

// 显示Ant Design表单项标签，用于显示必填星号
:deep(.ant-form-item-label) {
  display: block;
  
  label {
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    margin-bottom: 8px;
    
    &.ant-form-item-required {
      &::before {
        color: #FA5151;
      }
    }
  }
}

// 调整表单项间距
:deep(.ant-form-item) {
  margin-bottom: 0;
}

// 调整表单项标签间距
:deep(.ant-form-item-label) {
  padding-bottom: 4px;
}

// 级联选择器样式
:deep(.ant-cascader) {
  width: 400px;
  height: 48px;
  border-radius: 100px;
  background: #FFFFFF;
  border: 1px solid transparent;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  box-shadow: none;
  padding: 0 20px !important;
  
  &:hover {
    border-color: #1748FD;
    box-shadow: none;
  }
  
  &.ant-cascader-focused {
    border-color: #1748FD;
    box-shadow: none;
    outline: none;
  }
  
  .ant-cascader-selector {
    height: 48px !important;
    border-radius: 100px !important;
    background: transparent !important;
    border: none !important;
    box-shadow: none !important;
    padding: 0 !important;
    
    .ant-cascader-selection-placeholder {
      color: rgba(0, 0, 0, 0.3);
      line-height: 46px;
    }
    
    .ant-cascader-selection-item {
      line-height: 46px;
    }
  }
}

// 数字输入框样式
:deep(.ant-input-number) {
  width: 400px !important;
  height: 48px !important;
  border-radius: 100px !important;
  background: #FFFFFF !important;
  border: 1px solid transparent !important;
  box-shadow: none !important;
  padding: 0 20px !important;
  
  .ant-input-number-input {
    width: 100% !important;
    height: 46px !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    padding: 0 !important;
    
    &::placeholder {
      color: rgba(0, 0, 0, 0.3);
    }
  }
  
  &:hover {
    border-color: #1748FD !important;
    box-shadow: none !important;
  }
  
  &:focus-within {
    border-color: #1748FD !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// 密码输入框样式
:deep(.ant-input-password) {
  width: 400px !important;
  height: 48px !important;
  border-radius: 100px !important;
  background: #FFFFFF !important;
  border: 1px solid transparent !important;
  box-shadow: none !important;
  position: relative;
  padding: 0 60px 0 20px !important;
  
  .ant-input {
    width: 100% !important;
    height: auto !important;
    border: none !important;
    box-shadow: none !important;
    background: transparent !important;
    padding: 0 !important;
    font-size: 14px !important;
    
    &::placeholder {
      color: rgba(0, 0, 0, 0.3);
            font-size: 14px;
          }
        }
        
  .ant-input-password-icon {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    color: rgba(0, 0, 0, 0.45);
    cursor: pointer;
    z-index: 1;
    
    &:hover {
      color: rgba(0, 0, 0, 0.85);
    }
  }
  
  &:hover {
    border-color: #1748FD !important;
    box-shadow: none !important;
  }
  
  &:focus-within {
    border-color: #1748FD !important;
    box-shadow: none !important;
    outline: none !important;
  }
}

// 基础输入框样式已通过 .custom-input 类统一处理 