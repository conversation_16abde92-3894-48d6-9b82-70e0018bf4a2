<!--
  * 门店选择弹窗
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2025-01-27
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
-->
<template>
  <a-modal
    :open="visible"
    :title="null"
    :width="600"
    :centered="true"
    :closable="false"
    :maskClosable="false"
    :footer="null"
    :destroyOnClose="true"
    class="store-select-modal"
  >
    <div class="store-select-container">
      <!-- 自定义标题头部 -->
      <div class="modal-header">
        <div class="header-content">
          <div class="header-text">
            <h2 class="title">选择门店</h2>
            <p class="subtitle">请选择要登录的门店</p>
          </div>
        </div>
        <div class="close-btn" @click="closeModal">
          <CloseOutlined />
        </div>
      </div>

      <div class="store-list" v-if="!loadingStores">
        <div 
          v-for="store in storeList" 
          :key="store.id"
          class="store-item"
          :class="{ 'selected': selectedStoreId === store.id }"
          @click="selectStore(store)"
        >
          <div class="store-content">
            <div class="store-icon" v-if="!store.logo">
              <ShopOutlined />
            </div>
            <div class="store-logo" v-if="store.logo">
              <img :src="store.logo" :alt="store.name" />
            </div>
            <div class="store-info">
              <div class="store-name">{{ store.name }}</div>
              <div class="store-desc" v-if="store.description">{{ store.description }}</div>
            </div>
            <div class="store-check" v-if="selectedStoreId === store.id">
              <CheckCircleFilled />
            </div>
          </div>
        </div>
      </div>
      
      <!-- 加载状态 -->
      <div class="loading-container" v-if="loadingStores">
        <a-spin size="large" tip="正在加载门店列表...">
          <div class="loading-placeholder"></div>
        </a-spin>
      </div>
      
      <div class="footer">
        <a-button 
          type="primary" 
          size="large"
          block
          :disabled="!selectedStoreId"
          @click="confirmSelect"
          :loading="loading"
        >
          确认进入
        </a-button>
      </div>
    </div>
  </a-modal>
</template>

<script setup>
  import { ref, reactive } from 'vue';
  import { CheckCircleFilled, ShopOutlined, CloseOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { storeApi } from '/@/api/system/store-api';
  import { smartSentry } from '/@/lib/smart-sentry';

  // 弹窗显示状态
  const visible = ref(false);
  const loading = ref(false);
  const loadingStores = ref(false);
  const selectedStoreId = ref(null);
  const employeeId = ref(null);

  // 门店数据
  const storeList = reactive([]);

  // 事件定义
  const emit = defineEmits(['confirm', 'cancel']);

  // 显示弹窗
  async function showModal(loginResponseData) {
    selectedStoreId.value = null;
    
    // 从登录响应中获取employeeId
    if (loginResponseData && loginResponseData.employeeId) {
      employeeId.value = loginResponseData.employeeId;
      // 先加载门店列表，根据门店数量决定是否显示弹窗
      await loadStoreList();
    } else {
      message.error('获取员工信息失败');
    }
  }

  // 加载门店列表
  async function loadStoreList() {
    if (!employeeId.value) {
      message.error('员工ID不能为空');
      return;
    }

    try {
      loadingStores.value = true;
      const result = await storeApi.getEmployeeStoreList(employeeId.value);
      
      // 清空现有数据
      storeList.splice(0, storeList.length);
      
      // 处理响应数据
      if (result.data && Array.isArray(result.data)) {
        const stores = result.data.map(store => ({
          id: store.storeId,
          name: store.storeName,
          logo: store.storeLogo,
          description: store.storeDesc || store.description,
          employeeId: store.employeeId
        }));
        storeList.push(...stores);
      }
      
      if (storeList.length === 0) {
        message.warning('暂无可选门店');
      } else if (storeList.length === 1) {
        // 只有一个门店时直接选择该门店，不显示弹窗
        await autoSelectSingleStore();
        return;
      } else {
        // 多个门店时显示选择弹窗
        visible.value = true;
      }
    } catch (error) {
      smartSentry.captureError(error);
      message.error('加载门店列表失败');
    } finally {
      loadingStores.value = false;
    }
  }

  // 选择门店
  function selectStore(store) {
    selectedStoreId.value = store.id;
  }

  // 自动选择单个门店
  async function autoSelectSingleStore() {
    const store = storeList[0];
    try {
      // 调用API设置当前门店
      await storeApi.setCurrentStore(store.id, employeeId.value);
      visible.value = false;
      emit('confirm', store);
    } catch (error) {
      smartSentry.captureError(error);
      message.error('选择门店失败，请重试');
    }
  }

  // 确认选择
  async function confirmSelect() {
    if (!selectedStoreId.value) {
      message.warning('请选择一个门店');
      return;
    }
    
    const selectedStore = storeList.find(store => store.id === selectedStoreId.value);
    if (!selectedStore) {
      message.error('门店信息不存在');
      return;
    }
    
    loading.value = true;
    
    try {
      // 调用API设置当前门店
      await storeApi.setCurrentStore(selectedStoreId.value, employeeId.value);
      visible.value = false;
      emit('confirm', selectedStore);
    } catch (error) {
      smartSentry.captureError(error);
      message.error('选择门店失败，请重试');
    } finally {
      loading.value = false;
    }
  }

  // 关闭弹窗
  function closeModal() {
    visible.value = false;
    emit('cancel');
  }

  // 暴露方法给父组件
  defineExpose({
    showModal
  });
</script>

<style scoped lang="less">
  // 模态框整体样式
  :global(.store-select-modal .ant-modal-content) {
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 16px 48px rgba(0, 0, 0, 0.08);
    border: none;
    background: #ffffff;
  }

  :global(.store-select-modal .ant-modal-body) {
    padding: 0;
  }

  .store-select-container {
    padding: 0;
    position: relative;
    overflow: hidden;
  }

  // 自定义头部样式
  .modal-header {
    padding: 24px 24px 20px 24px;
    text-align: center;
    background: #fff;
    position: relative;

    .header-content {
      display: flex;
      flex-direction: column;
      align-items: center;
    }

    .header-text {
      text-align: center;
    }

    .title {
      font-size: 20px;
      font-weight: 600;
      margin: 0 0 4px 0;
      color: #1F2937;
    }

    .subtitle {
      font-size: 14px;
      margin: 0;
      color: #6B7280;
      font-weight: 400;
    }

    .header-decoration {
      display: none;
    }

    .close-btn {
      position: absolute;
      top: 20px;
      right: 20px;
      width: 32px;
      height: 32px;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 8px;
      cursor: pointer;
      color: #6B7280;
      background: transparent;
      transition: all 0.2s ease;

      &:hover {
        background: #F3F4F6;
        color: #374151;
      }

      .anticon {
        font-size: 16px;
      }
    }
  }

  .store-list {
    padding: 24px;
    margin-bottom: 0;
    max-height: 360px;
    overflow-y: auto;

    // 简约滚动条
    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: transparent;
    }

    &::-webkit-scrollbar-thumb {
      background: #D1D5DB;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #9CA3AF;
    }
  }

  .loading-container {
    text-align: center;
    padding: 40px 24px;
  }

  .loading-placeholder {
    height: 120px;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .store-item {
    margin-bottom: 12px;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    background: white;
    border: 1px solid #E5E7EB;

    &:hover {
      border-color: #1748FD;
      box-shadow: 0 4px 12px rgba(23, 72, 253, 0.15);
    }

    &.selected {
      border-color: #1748FD;
      background: #F8FAFF;
      box-shadow: 0 4px 12px rgba(23, 72, 253, 0.15);
    }

    &:last-child {
      margin-bottom: 0;
    }
  }

  .store-content {
    display: flex;
    align-items: center;
    padding: 16px 20px;
  }

  .store-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border-radius: 10px;
    background: #1748FD;
    color: white;
    font-size: 18px;
    margin-right: 16px;
  }

  .store-logo {
    width: 40px;
    height: 40px;
    border-radius: 10px;
    margin-right: 16px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .store-info {
    flex: 1;
  }

  .store-name {
    font-size: 16px;
    font-weight: 600;
    color: #1F2937;
    margin-bottom: 4px;
    line-height: 1.4;
  }

  .store-desc {
    font-size: 14px;
    color: #6B7280;
    line-height: 1.4;
  }

  .store-check {
    color: #1748FD;
    font-size: 20px;
    margin-left: 12px;
  }

  .footer {
    background: #fff;
    padding: 20px 24px;
    text-align: center;
  }

  .footer .ant-btn-primary {
    height: 44px;
    font-size: 16px;
    font-weight: 500;
    border-radius: 8px;
    background: #1748FD;
    border: none;
    transition: all 0.2s ease;
    
    &:hover {
      background: #0E3DD6;
    }

    &:disabled {
      background: #E5E7EB;
      color: #9CA3AF;
    }
  }
</style> 