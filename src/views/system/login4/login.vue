<!--
  * 登录
  *
  * @Author:    1024创新实验室-主任：卓大
  * @Date:      2022-09-12 22:34:00
  * @Wechat:    zhuda1024
  * @Email:     <EMAIL>
  * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
  *
-->
<template>
  <div class="login-container">
    <!-- 顶部品牌名称 -->
    <div class="brand-header">
      <h1 class="brand-name">Huanlefu</h1>
    </div>
    
    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 页面标题 -->
      <div class="page-title">
        <h2>登录</h2>
      </div>
      
      <!-- 登录表单 -->
      <a-form ref="formRef" class="login-form" :model="loginForm" :rules="rules" layout="vertical">
        <!-- 用户名 -->
        <a-form-item name="loginName" label="用户名">
          <a-input v-model:value.trim="loginForm.loginName" placeholder="请输入用户名" class="custom-input" />
        </a-form-item>
        
        <!-- 邮箱验证码 -->
        <a-form-item name="emailCode" label="邮箱验证码" v-if="emailCodeShowFlag">
          <div class="email-input-group">
            <a-input 
              v-model:value="loginForm.emailCode" 
              placeholder="请输入邮箱验证码"
              class="custom-input email-input"
            />
            <a-button @click="sendSmsCode" class="email-btn" type="link" :disabled="emailCodeButtonDisabled">
              {{ emailCodeTips }}
            </a-button>
          </div>
        </a-form-item>
        
        <!-- 密码 -->
        <a-form-item name="password" label="密码">
          <a-input-password 
            v-model:value="loginForm.password" 
            placeholder="请输入密码" 
            autocomplete="on"
            class="custom-input" 
          />
        </a-form-item>
        
        <!-- 验证码 -->
        <a-form-item name="captchaCode" label="验证码">
          <div class="captcha-input-group">
            <a-input 
              v-model:value.trim="loginForm.captchaCode" 
              placeholder="请输入验证码" 
              class="custom-input captcha-input"
            />
            <img class="captcha-img" :src="captchaBase64Image" @click="getCaptcha" />
          </div>
        </a-form-item>
        

        
        <!-- 登录按钮 -->
        <a-form-item>
          <a-button type="primary" @click="onLogin" class="login-btn" size="large">
            登录
          </a-button>
        </a-form-item>
        
        <!-- 分隔线 -->
        <div class="divider">
          <span class="divider-text">OR</span>
        </div>
        
        <!-- 注册按钮 -->
        <a-form-item>
          <a-button @click="goToRegister" class="register-btn" size="large">
            立即入驻欢乐付商家平台
          </a-button>
        </a-form-item>
      </a-form>
      
      <!-- 底部版权信息 -->
      <div class="copyright">
        Copyright © 四川蓉通达资讯科技有限公司 丨 蜀ICP备2025143009号-4
      </div>
    </div>
    
    <!-- 门店选择弹窗 -->
    <StoreSelectModal ref="storeSelectModalRef" @confirm="onStoreSelected" />
  </div>
</template>

<script setup>
  import { message } from 'ant-design-vue';
  import { onMounted, onUnmounted, reactive, ref } from 'vue';
  import { useRouter } from 'vue-router';
  import { loginApi } from '/@/api/system/login-api';
  import { SmartLoading } from '/@/components/framework/smart-loading';
  import { LOGIN_DEVICE_ENUM } from '/@/constants/system/login-device-const';
  import { useUserStore } from '/@/store/modules/system/user';
  import { buildRoutes } from '/@/router/index';
  import { smartSentry } from '/@/lib/smart-sentry';
  import { encryptData } from '/@/lib/encrypt';
  import { localSave } from '/@/utils/local-util.js';
  import LocalStorageKeyConst from '/@/constants/local-storage-key-const.js';
  import { useDictStore } from '/@/store/modules/system/dict.js';
  import { dictApi } from '/@/api/support/dict-api.js';
  import StoreSelectModal from './components/store-select-modal.vue';

  //--------------------- 登录表单 ---------------------------------

  const loginForm = reactive({
    loginName: '',
    password: '',
    emailCode: '',
    captchaCode: '',
    captchaUuid: '',
    loginDevice: LOGIN_DEVICE_ENUM.PC.value,
  });
  
  const rules = {
    loginName: [{ required: true, message: '用户名不能为空' }],
    password: [{ required: true, message: '密码不能为空' }],
    emailCode: [{ required: true, message: '邮箱验证码不能为空' }],
    captchaCode: [{ required: true, message: '验证码不能为空' }],
  };

  const router = useRouter();
  const formRef = ref();
  const storeSelectModalRef = ref();
  const captchaBase64Image = ref('');

  onMounted(() => {
    document.onkeyup = (e) => {
      if (e.keyCode === 13) {
        onLogin();
      }
    };
  });

  onUnmounted(() => {
    document.onkeyup = null;
  });

  //登录
  async function onLogin() {
    formRef.value.validate().then(async () => {
      try {
        SmartLoading.show();
        // 密码加密
        let encryptPasswordForm = Object.assign({}, loginForm, {
          password: encryptData(loginForm.password),
        });
        const res = await loginApi.login(encryptPasswordForm);
        localSave(LocalStorageKeyConst.USER_TOKEN, res.data.token ? res.data.token : '');
        message.success('登录成功');
        //更新用户信息到pinia
        useUserStore().setUserLoginInfo(res.data);
        // 初始化数据字典
        const dictRes = await dictApi.getAllDictData();
        useDictStore().initData(dictRes.data);
        //构建系统的路由 - 传递当前用户的菜单路由列表
        let menuRouterList = res.data.menuList.filter((e) => e.path || e.frameUrl);
        buildRoutes(menuRouterList);
        
        stopRefreshCaptchaInterval();
        
        // 显示门店选择弹窗，传递登录响应数据
        storeSelectModalRef.value.showModal(res.data);
      } catch (e) {
        if (e.data && e.data.code !== 0) {
          loginForm.captchaCode = '';
          getCaptcha();
        }
        smartSentry.captureError(e);
      } finally {
        SmartLoading.hide();
      }
    });
  }

  //--------------------- 门店选择 ---------------------------------

  // 门店选择确认处理
  function onStoreSelected(selectedStore) {
    console.log('选择的门店:', selectedStore);
    // 保存选择的门店信息到本地存储
    localSave(LocalStorageKeyConst.CURRENT_STORE, selectedStore);
    // 更新用户store中的当前门店信息
    useUserStore().setCurrentStore(selectedStore);
    // 跳转到首页
    router.push('/home');
  }

  //--------------------- 注册跳转 ---------------------------------

  // 跳转到注册页面
  function goToRegister() {
    router.push('/register');
  }

  onMounted(() => {
    getCaptcha();
    getTwoFactorLoginFlag();
  });

  //--------------------- 邮箱验证码 ---------------------------------

  const emailCodeShowFlag = ref(false);
  let emailCodeTips = ref('获取邮箱验证码');
  let emailCodeButtonDisabled = ref(false);
  // 定时器
  let countDownTimer = null;
  // 开始倒计时
  function runCountDown() {
    emailCodeButtonDisabled.value = true;
    let countDown = 60;
    emailCodeTips.value = `${countDown}秒后重新获取`;
    countDownTimer = setInterval(() => {
      if (countDown > 1) {
        countDown--;
        emailCodeTips.value = `${countDown}秒后重新获取`;
      } else {
        clearInterval(countDownTimer);
        emailCodeButtonDisabled.value = false;
        emailCodeTips.value = '获取验证码';
      }
    }, 1000);
  }

  // 获取双因子登录标识
  async function getTwoFactorLoginFlag() {
    try {
      let result = await loginApi.getTwoFactorLoginFlag();
      emailCodeShowFlag.value = result.data;
    } catch (e) {
      smartSentry.captureError(e);
    }
  }

  // 发送邮箱验证码
  async function sendSmsCode() {
    try {
      SmartLoading.show();
      let result = await loginApi.sendLoginEmailCode(loginForm.loginName);
      message.success('验证码发送成功!请登录邮箱查看验证码~');
      runCountDown();
    } catch (e) {
      smartSentry.captureError(e);
    } finally {
      SmartLoading.hide();
    }
  }

  //--------------------- 验证码 ---------------------------------

  async function getCaptcha() {
    try {
      let captchaResult = await loginApi.getCaptcha();
      captchaBase64Image.value = captchaResult.data.captchaBase64Image;
      loginForm.captchaUuid = captchaResult.data.captchaUuid;
      beginRefreshCaptchaInterval(captchaResult.data.expireSeconds);
    } catch (e) {
      console.log(e);
    }
  }

  let refreshCaptchaInterval = null;
  function beginRefreshCaptchaInterval(expireSeconds) {
    if (refreshCaptchaInterval === null) {
      refreshCaptchaInterval = setInterval(getCaptcha, (expireSeconds - 5) * 1000);
    }
  }

  function stopRefreshCaptchaInterval() {
    if (refreshCaptchaInterval != null) {
      clearInterval(refreshCaptchaInterval);
      refreshCaptchaInterval = null;
    }
  }
</script>

<style lang="less" scoped>
  @import './login.less';
</style>
