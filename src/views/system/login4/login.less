.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #C4E6F5 0%, rgba(196, 230, 245, 0) 100%);
  padding: 0;
  overflow-x: hidden;
}

.brand-header {
  padding: 20px 40px;
  
  .brand-name {
    font-family: 'Poetsen One', sans-serif;
    font-size: 24px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
  }
}

.main-content {
  flex: 1;
  max-width: 400px;
  margin: 0 auto;
  padding: 40px 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  min-height: calc(100vh - 120px);
  justify-content: flex-start;
}

.page-title {
  width: 100%;
  max-width: 400px;
  text-align: left;
  margin-bottom: 60px;
  
  h2 {
    font-size: 34px;
    font-weight: 600;
    color: rgba(0, 0, 0, 0.85);
    margin: 0;
  }
}

.login-form {
  width: 100%;
  max-width: 400px;
  
  .ant-form-item {
    margin-bottom: 32px;
  }
  
  .ant-form-item-label {
    padding-bottom: 8px;
    
    label {
      font-size: 14px;
      font-weight: 400;
      color: rgba(0, 0, 0, 0.85);
    }
  }
}

.custom-input {
  width: 100%;
  height: 48px;
  border-radius: 100px;
  background: #FFFFFF;
  border: 1px solid transparent;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.85);
  box-shadow: none;
  padding: 0 20px;
  
  &::placeholder {
    color: rgba(0, 0, 0, 0.3);
  }
  
  &:focus,
  &:hover {
    border-color: #1748FD;
    box-shadow: none;
    outline: none;
  }
  
  // 覆盖浏览器自动填充的样式
  &:-webkit-autofill,
  &:-webkit-autofill:hover,
  &:-webkit-autofill:focus,
  &:-webkit-autofill:active {
    -webkit-box-shadow: 0 0 0 1000px #FFFFFF inset !important;
    -webkit-text-fill-color: rgba(0, 0, 0, 0.85) !important;
    background-color: #FFFFFF !important;
    background-image: none !important;
    border-radius: 100px !important;
    transition: background-color 5000s ease-in-out 0s;
  }
}

// 邮箱验证码输入组
.email-input-group {
  display: flex;
  align-items: center;
  position: relative;
  
  .email-input {
    padding-right: 120px;
  }
  
  .email-btn {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    height: auto;
    padding: 0;
    color: #1748FD;
    font-size: 14px;
    font-weight: 400;
    border: none;
    background: none;
    
    &:hover {
      color: #1748FD;
    }
    
    &:disabled {
      color: rgba(0, 0, 0, 0.3);
    }
  }
}

// 验证码输入组
.captcha-input-group {
  display: flex;
  align-items: center;
  gap: 16px;
  
  .captcha-input {
    flex: 1;
  }
  
  .captcha-img {
    width: 120px;
    height: 48px;
    border-radius: 8px;
    border: 1px solid rgba(0, 0, 0, 0.08);
    cursor: pointer;
    object-fit: contain;
    
    &:hover {
      border-color: #1748FD;
    }
  }
}

// 密码输入框样式
:deep(.ant-input-password) {
  width: 100%;
  height: 48px;
  border-radius: 100px;
  background: #FFFFFF;
  border: 1px solid transparent;
  box-shadow: none;
  padding: 0 20px;
  
  .ant-input {
    height: auto;
    border: none;
    box-shadow: none;
    background: transparent;
    padding: 0;
    font-size: 14px;
    
    &::placeholder {
      color: rgba(0, 0, 0, 0.3);
    }
    
    // 覆盖密码输入框的浏览器自动填充样式
    &:-webkit-autofill,
    &:-webkit-autofill:hover,
    &:-webkit-autofill:focus,
    &:-webkit-autofill:active {
      -webkit-box-shadow: 0 0 0 1000px #FFFFFF inset !important;
      -webkit-text-fill-color: rgba(0, 0, 0, 0.85) !important;
      background-color: #FFFFFF !important;
      background-image: none !important;
      transition: background-color 5000s ease-in-out 0s;
    }
  }
  
  .ant-input-password-icon {
    color: rgba(0, 0, 0, 0.45);
    
    &:hover {
      color: rgba(0, 0, 0, 0.85);
    }
  }
  
  &:hover {
    border-color: #1748FD;
    box-shadow: none;
  }
  
  &:focus-within {
    border-color: #1748FD;
    box-shadow: none;
    outline: none;
  }
}



// 登录按钮
.login-btn {
  width: 100%;
  height: 48px;
  border-radius: 100px;
  background: #1748FD;
  border: none;
  font-size: 14px;
  font-weight: 600;
  color: #FFFFFF;
  
  &:hover {
    background: #1748FD;
    opacity: 0.9;
  }
}

// 分隔线
.divider {
  width: 100%;
  max-width: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 40px 0;
  position: relative;
  
  &::before,
  &::after {
    content: '';
    display: block;
    width: 170px;
    height: 1px;
    background: rgba(0, 0, 0, 0.08);
  }
  
  .divider-text {
    font-family: 'Poetsen One', sans-serif;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
    padding: 0 20px 0 21px;
    background: transparent;
  }
}

// 注册按钮
.register-btn {
  width: 100%;
  height: 48px;
  border-radius: 100px;
  background: transparent;
  border: 2px dashed rgba(0, 0, 0, 0.85);
  font-size: 14px;
  font-weight: 600;
  color: rgba(0, 0, 0, 0.85);
  
  &:hover {
    border-color: #1748FD;
    color: #1748FD;
  }
}

// 版权信息
.copyright {
  text-align: left;
  align-self: flex-start;
  width: 400px;
  margin-top: auto;
  font-family: 'SF Pro', sans-serif;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.3);
}

// 响应式设计
@media (max-width: 768px) {
  .main-content {
    padding: 20px 20px;
  }
  
  .page-title {
    margin-bottom: 40px;
    
    h2 {
      font-size: 28px;
    }
  }
}

// 表单验证错误样式
:deep(.ant-form-item-has-error) {
  .custom-input,
  .ant-input-password {
    border-color: #FA5151 !important;
    box-shadow: none !important;
    background: #FFFFFF !important;
  }
}
