import { postRequest, getRequest } from '/@/lib/axios';

export const registerApi = {
  /**
   * 发送手机验证码
   * API: /store/smsCode
   */
  sendSmsCode: (corporateMobile) => {
    return postRequest('/store/smsCode', { corporateMobile });
  },

  /**
   * 上传图片
   */
  uploadImage: (formData) => {
    return postRequest('/register/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 提交注册信息
   * API: /store/register
   */
  register: (data) => {
    return postRequest('/store/register', data);
  },

  /**
   * 提交注册信息（别名）
   */
  submitRegister: (data) => {
    return postRequest('/register/submit', data);
  },

  /**
   * 检查社会信用代码是否存在
   */
  checkSocialCreditCode: (code) => {
    return getRequest(`/register/check-social-credit-code/${code}`);
  },

  /**
   * 检查手机号是否存在
   */
  checkPhone: (phone) => {
    return getRequest(`/register/check-phone/${phone}`);
  }
}; 