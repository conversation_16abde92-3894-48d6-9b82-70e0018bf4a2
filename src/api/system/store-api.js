/*
 * 门店相关API
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2025-01-27
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { getRequest, postRequest } from '/@/lib/axios';

export const storeApi = {
  /**
   * 查询账户所有门店
   * @param {number} employeeId 员工ID
   */
  getEmployeeStoreList: (employeeId) => {
    return getRequest(`/employee/queryStore/${employeeId}`);
  },

  /**
   * 选择登录门店
   * @param {number} storeId 门店ID
   */
  selectStore: (storeId) => {
    return postRequest('/store/selectStore', { storeId });
  },

  /**
   * 设置/切换当前门店
   * @param {number} storeId 门店ID
   * @param {number} employeeId 员工ID (可选)
   */
  setCurrentStore: (storeId, employeeId) => {
    const params = { storeId };
    if (employeeId) {
      params.employeeId = employeeId;
    }
    return postRequest('/employee/setCurrentStore', params);
  },

  /**
   * 获取当前选择的门店信息
   */
  getCurrentStore: () => {
    return getRequest('/store/getCurrentStore');
  },
}; 