import { postRequest, getRequest, getDownload } from '/@/lib/axios'

export const financeDetailApi = {
  // 查询收支明细列表
  queryFinanceDetailList: (params) => {
    return postRequest('/orderInstallment/incomeDetailsPage', params)
  },

  // 获取收支统计信息
  getFinanceStatistics: (params) => {
    return postRequest('/orderInstallmentDeduct/incomeDetailsPageTotal', params)
  },

  // 获取收支明细详情
  getFinanceDetailInfo: (id) => {
    return getRequest(`/finance/detail/${id}`)
  },

  // 导出收支明细
  exportFinanceDetail: (params) => {
    return getDownload('/finance/detail/export', params)
  },

  // 批量导出收支明细
  batchExportFinanceDetail: (idList) => {
    return postRequest('/finance/detail/batchExport', idList)
  },

  // 更新收支明细备注
  updateFinanceDetailRemark: (data) => {
    return postRequest('/finance/detail/updateRemark', data)
  }
} 