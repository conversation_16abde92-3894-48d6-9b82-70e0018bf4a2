/**
 * 逾期明细 api
 *
 * @Author:    SmartAdmin
 * @Date:      2024-12-19 15:30:00
 * @Copyright  SmartAdmin
 */
import { postRequest, getRequest, getDownload } from '/@/lib/axios';

export const overdueDetailApi = {

  /**
   * 查询逾期明细列表 <AUTHOR>
   */
  queryPage: (params) => {
    return postRequest('/orderInstallment/overdueDetailsPage', params);
  },

  /**
   * 导出逾期明细 <AUTHOR>
   */
  exportExcel: (params) => {
    return getDownload('/overdueDetail/exportExcel', params);
  },

  /**
   * 获取逾期明细详情 <AUTHOR>
   */
  getDetail: (overdueId) => {
    return getRequest(`/overdueDetail/get/${overdueId}`);
  },

  /**
   * 批量处理逾期订单 <AUTHOR>
   */
  batchProcess: (params) => {
    return postRequest('/overdueDetail/batchProcess', params);
  },

  /**
   * 更新代扣状态 <AUTHOR>
   */
  updateWithholdStatus: (params) => {
    return postRequest('/overdueDetail/updateWithholdStatus', params);
  },

  /**
   * 获取逾期统计信息 <AUTHOR>
   */
  getStatistics: (params) => {
    return postRequest('/orderInstallmentDeduct/overdueDetailsPageTotal', params);
  }

}; 