/**
 * 分段明细 api
 *
 * @Author:    SmartAdmin
 * @Date:      2024-12-19 16:00:00
 * @Copyright  SmartAdmin
 */
import { postRequest, getRequest, getDownload } from '/@/lib/axios';

export const segmentDetailApi = {

  /**
   * 查询分段明细列表 <AUTHOR>
   */
  queryPage: (params) => {
    return postRequest('/orderInstallment/installmentDetailsPage', params);
  },

  /**
   * 导出分段明细 <AUTHOR>
   */
  exportExcel: (params) => {
    return getDownload('/segmentDetail/exportExcel', params);
  },

  /**
   * 获取分段明细详情 <AUTHOR>
   */
  getDetail: (segmentId) => {
    return getRequest(`/segmentDetail/get/${segmentId}`);
  },

  /**
   * 批量操作分段明细 <AUTHOR>
   */
  batchOperation: (params) => {
    return postRequest('/segmentDetail/batchOperation', params);
  },

  /**
   * 手动扣款 <AUTHOR>
   */
  manualDeduction: (params) => {
    return postRequest('/segmentDetail/manualDeduction', params);
  },

  /**
   * 更新扣款计划 <AUTHOR>
   */
  updatePaymentPlan: (params) => {
    return postRequest('/segmentDetail/updatePaymentPlan', params);
  },

  /**
   * 获取分段统计信息 <AUTHOR>
   */
  getStatistics: (params) => {
    return postRequest('/orderInstallmentDeduct/installmentDetailsPageTotal', params);
  },

  /**
   * 重新扣款 <AUTHOR>
   */
  retryPayment: (params) => {
    return postRequest('/segmentDetail/retryPayment', params);
  }

}; 