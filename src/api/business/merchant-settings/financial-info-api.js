import { postRequest, getRequest } from '/@/lib/axios';

export const financialInfoApi = {
  
  /**
   * 获取对公账户信息
   */
  getCorporateAccountInfo: () => {
    return getRequest('/merchant/financial/corporate-account/info');
  },

  /**
   * 绑定/修改对公账户
   */
  bindCorporateAccount: (requestObject) => {
    return postRequest('/store/corporateAccount', requestObject);
  },

  /**
   * 修改对公账户 (保留旧接口，可能其他地方会用到)
   */
  updateCorporateAccount: (requestObject) => {
    return postRequest('/merchant/financial/corporate-account/update', requestObject);
  },

  /**
   * 获取支付钱包信息
   */
  getPaymentWalletInfo: () => {
    return getRequest('/merchant/financial/payment-wallet/info');
  },

  /**
   * 开通支付钱包
   */
  openPaymentWallet: (storeId) => {
    return postRequest(`/store/${storeId}/openBaofoo`);
  },

  /**
   * 钱包提现
   */
  withdrawFromWallet: (requestObject) => {
    return postRequest('/merchant/financial/payment-wallet/withdraw', requestObject);
  },

  /**
   * 获取钱包余额
   */
  getWalletBalance: () => {
    return getRequest('/merchant/financial/payment-wallet/balance');
  },

  /**
   * 获取宝付钱包余额
   */
  getBaofooBalance: (storeId) => {
    return getRequest(`/store/${storeId}/baofooBalance`);
  },

  /**
   * 获取财务概览信息
   */
  getFinancialOverview: () => {
    return getRequest('/merchant/financial/overview');
  },

  /**
   * 商户报备查询
   */
  getMerchantReportStatus: (storeId) => {
    return postRequest(`/store/${storeId}/baofooJuHeReport/1`);
  },

  /**
   * 商户报备
   */
  merchantReport: (storeId) => {
    return postRequest(`/store/${storeId}/baofooJuHeReport/0`);
  },

  /**
   * 获取特约商户状态
   */
  getSpecialMerchantStatus: (storeId) => {
    return postRequest(`/store/${storeId}/baofooAccountUpgrade/1`);
  },

  /**
   * 申请开通特约商户
   */
  openSpecialMerchant: (storeId) => {
    return postRequest(`/store/${storeId}/baofooAccountUpgrade/0`);
  },

  /**
   * 更新银行开户许可证
   */
  applySpecialMerchant: (data) => {
    return postRequest(`/store/${data.storeId}/bankAccountLicensePic`, {
      bankAccountLicensePic: data.bankAccountLicensePic
    });
  }

}; 