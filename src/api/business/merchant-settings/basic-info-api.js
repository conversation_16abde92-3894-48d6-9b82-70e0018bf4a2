import { postRequest, getRequest } from '/@/lib/axios';

export const basicInfoApi = {
  /**
   * 获取门店基本信息
   */
  getStoreBasicInfo: (storeId) => {
    return getRequest(`/store/${storeId}`);
  },

  /**
   * 获取商户基本信息 (兼容旧版本)
   */
  getMerchantBasicInfo: () => {
    return getRequest('/merchant-settings/basic-info');
  },

  /**
   * 更新商户基本信息
   * API: /store/update
   */
  updateMerchantBasicInfo: (data) => {
    return postRequest('/store/update', data);
  },

  /**
   * 发送短信验证码
   * API: /store/smsCode
   */
  sendSmsCode: (corporateMobile) => {
    return postRequest('/store/smsCode', { corporateMobile });
  },

  /**
   * 获取图片预览链接
   */
  getImagePreviewUrl: (imageType) => {
    return getRequest(`/merchant-settings/image-preview/${imageType}`);
  },

  /**
   * 上传图片
   */
  uploadImage: (formData) => {
    return postRequest('/merchant-settings/upload-image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  /**
   * 更新门店小程序 APPID
   * API: /store/appId
   */
  updateStoreAppId: (data) => {
    return postRequest('/store/appId', data);
  }
}; 