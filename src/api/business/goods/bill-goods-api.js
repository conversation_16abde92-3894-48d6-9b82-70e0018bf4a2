/**
 * 账单付商品 api
 *
 * @Author:    SmartAdmin
 * @Date:      2024-12-19 16:30:00
 * @Copyright  SmartAdmin
 */
import { postRequest, getRequest, getDownload, request } from '/@/lib/axios';

export const billGoodsApi = {

  /**
   * 查询账单付商品列表 <AUTHOR>
   * API: /product/queryPage
   */
  queryPage: (params) => {
    return postRequest('/product/queryPage', params);
  },

  /**
   * 添加账单付商品 <AUTHOR>
   * API: /product/add
   */
  add: (params) => {
    return postRequest('/product/add', params);
  },

  /**
   * 更新账单付商品 <AUTHOR>
   * API: /product/update
   */
  update: (params) => {
    return postRequest('/product/update', params);
  },

  /**
   * 删除账单付商品 <AUTHOR>
   * API: /product/delete/{productId}
   */
  delete: (productId) => {
    return getRequest(`/product/delete/${productId}`);
  },

  /**
   * 批量删除账单付商品 <AUTHOR>
   */
  batchDelete: (goodsIdList) => {
    return postRequest('/billGoods/batchDelete', { goodsIdList });
  },

  /**
   * 获取账单付商品详情 <AUTHOR>
   * API: /product/get/{productId}
   */
  getDetail: (productId) => {
    return getRequest(`/product/get/${productId}`);
  },

  /**
   * 上架/下架账单付商品 <AUTHOR>
   */
  updateStatus: (params) => {
    return postRequest('/billGoods/updateStatus', params);
  },

  /**
   * 更新商品排序 <AUTHOR>
   */
  updateSort: (params) => {
    return postRequest('/billGoods/updateSort', params);
  },

  /**
   * 导出账单付商品 <AUTHOR>
   */
  exportExcel: (params) => {
    return getDownload('/billGoods/exportExcel', params);
  },

  /**
   * 生成商品二维码 <AUTHOR>
   */
  generateQrCode: (productId) => {
    // 构建支付宝小程序scheme链接
    const appId = '2021005160613950';
    const path = 'pages/store/product-detail';
    const query = `productId=${productId}`;
    
    // 拼接scheme地址
    const scheme = `alipays://platformapi/startapp?appId=${appId}&page=${path}&query=${encodeURIComponent(query)}`;
    
    // 生成最终可访问的链接
    const finalUrl = `https://ds.alipay.com/?scheme=${encodeURIComponent(scheme)}`;
    
    return Promise.resolve({
      data: {
        qrCodeUrl: finalUrl,
        scheme: scheme,
        productId: productId
      }
    });
  },

  /**
   * 获取商品二维码图片数据 <AUTHOR>
   * API: /product/{productId}/qrcode
   */
  getQrCodeImage: (productId) => {
    return request({
      url: `/product/${productId}/qrcode`,
      method: 'get',
      responseType: 'blob',
    });
  },

  /**
   * 获取商品统计信息 <AUTHOR>
   */
  getStatistics: (params) => {
    return postRequest('/billGoods/statistics', params);
  },

  /**
   * 获取可复制的商品列表 <AUTHOR>
   * API: /product/copy
   */
  getCopyProductList: (params) => {
    return postRequest('/product/copy', params);
  },

  /**
   * 上传文件 <AUTHOR>
   * API: /support/file/upload
   */
  uploadFile: (file) => {
    const formData = new FormData();
    formData.append('file', file);
    return request({
      url: '/support/file/upload?folder=1',
      method: 'post',
      data: formData,
      headers: {
        // 让浏览器自动设置Content-Type，包括boundary
      },
    });
  },

  /**
   * 获取合同模版列表 <AUTHOR>
   * API: /store/contractTemplate
   */
  getContractTemplates: (storeId) => {
    const params = storeId ? { storeId } : {};
    return getRequest('/store/contractTemplate', params);
  },

  /**
   * 商品金额计算 <AUTHOR>
   * API: /product/calculate
   */
  calculateAmount: (params) => {
    return postRequest('/product/calculate', params);
  }

}; 