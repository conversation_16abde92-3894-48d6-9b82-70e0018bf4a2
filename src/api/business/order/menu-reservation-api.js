/**
 * 预定菜单 api
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2024-01-01 00:00:00
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { postRequest, getRequest, request } from '/@/lib/axios';

export const menuReservationApi = {
  /**
   * 查询预定菜单列表 <AUTHOR>
   * API: /order/reserve/queryPage
   */
  queryMenuReservationList: (params) => {
    return postRequest('/order/reserve/queryPage', params);
  },

  /**
   * 查询预定菜单统计信息 <AUTHOR>
   * API: /order/reserve/queryPageTotal
   */
  queryMenuReservationStatistics: (params) => {
    return postRequest('/order/reserve/queryPageTotal', params);
  },

  /**
   * 获取预定菜单详情 <AUTHOR>
   * API: /order/reserve/{orderId}/detail
   */
  getMenuReservationDetail: (orderId) => {
    return postRequest(`/order/reserve/${orderId}/detail`);
  },

  /**
   * 退款 <AUTHOR>
   * API: /order/reserve/refund
   */
  refund: (params) => {
    return postRequest('/order/reserve/refund', params);
  },

  /**
   * 导出预定菜单 <AUTHOR>
   */
  exportMenuReservation: (params) => {
    return postRequest('/menuReservation/export', params);
  },

  /**
   * 获取收款二维码 <AUTHOR>
   * API: /order/reserve/paymentQrcode/{appType}
   * @param {string} appType - 小程序类型：MERCHANT-商家独立小程序, HUANLEFU-欢乐付小程序
   */
  getPaymentQrCode: (appType) => {
    return request({
      url: `/order/reserve/paymentQrcode/${appType}`,
      method: 'get',
      responseType: 'blob',
    });
  },

  /**
   * 更新预定菜单备注 <AUTHOR>
   */
  updateMenuReservationRemark: (params) => {
    return postRequest('/menuReservation/updateRemark', params);
  },

  /**
   * 关联订单 <AUTHOR>
   */
  associateOrder: (params) => {
    return postRequest('/order/reserve/associate', params);
  },

  /**
   * 获取解约收款二维码 <AUTHOR>
   * API: /orderInstallment/terminated/paymentQrcode
   * @param {object} params - 包含installmentOrderId、penaltyFee、sessionFee、receiptType等参数
   */
  getTerminationPaymentQrCode: (params) => {
    return request({
      url: '/orderInstallment/terminated/paymentQrcode',
      method: 'post',
      data: params,
      responseType: 'blob',
    });
  },
}; 