/*
 * 分段订单 API
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2025-01-27 10:00:00
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { postRequest, getRequest, postDownload } from '/@/lib/axios';

export const segmentOrderApi = {
  /**
   * 查询分期订单分页列表 <AUTHOR>
   * API: /orderInstallment/queryPage
   */
  querySegmentOrderList: (params) => {
    return postRequest('/orderInstallment/queryPage', params);
  },

  /**
   * 查询分期订单统计数据 <AUTHOR>
   * API: /orderInstallment/queryPageTotal
   */
  querySegmentOrderStatistics: (params) => {
    return postRequest('/orderInstallment/queryPageTotal', params);
  },

  /**
   * 查询分段订单列表 <AUTHOR>
   */
  queryList: (params) => {
    return postRequest('/admin-api/business/segment-order/query', params);
  },

  /**
   * 获取分段订单详情 <AUTHOR>
   * API: /orderInstallment/get/{orderId}
   */
  getDetail: (orderId) => {
    return getRequest(`/orderInstallment/get/${orderId}`);
  },

  /**
   * 添加分段订单 <AUTHOR>
   */
  add: (params) => {
    return postRequest('/admin-api/business/segment-order/add', params);
  },

  /**
   * 更新分段订单 <AUTHOR>
   */
  update: (params) => {
    return postRequest('/admin-api/business/segment-order/update', params);
  },

  /**
   * 删除分段订单 <AUTHOR>
   */
  delete: (orderId) => {
    return postRequest(`/admin-api/business/segment-order/delete/${orderId}`);
  },

  /**
   * 批量删除分段订单 <AUTHOR>
   */
  batchDelete: (orderIdList) => {
    return postRequest('/admin-api/business/segment-order/batchDelete', orderIdList);
  },

  /**
   * 查询收款计划 <AUTHOR>
   */
  queryPaymentPlan: (params) => {
    return postRequest('/orderInstallmentDeduct/queryPage', params);
  },

  /**
   * 获取可收款期数 <AUTHOR>
   */
  getAvailablePeriods: (orderId) => {
    return getRequest(`/admin-api/business/segment-order/available-periods/${orderId}`);
  },

  /**
   * 线下收款 <AUTHOR>
   */
  offlinePayment: (params) => {
    return postRequest('/admin-api/business/segment-order/offline-payment', params);
  },

  /**
   * 导出分段订单 <AUTHOR>
   */
  export: (params) => {
    return postDownload('/admin-api/business/segment-order/export', params);
  },

  /**
   * 导出分段订单 <AUTHOR>
   */
  exportSegmentOrders: (params) => {
    return postDownload('/admin-api/business/segment-order/export', params);
  },

  /**
   * 更新订单状态 <AUTHOR>
   */
  updateStatus: (params) => {
    return postRequest('/admin-api/business/segment-order/update-status', params);
  },

  /**
   * 执行扣款 <AUTHOR>
   */
  executeDeduction: (params) => {
    return postRequest('/admin-api/business/segment-order/execute-deduction', params);
  },

  /**
   * 获取扣款记录 <AUTHOR>
   */
  getDeductionRecords: (orderId) => {
    return getRequest(`/admin-api/business/segment-order/deduction-records/${orderId}`);
  },

  /**
   * 商家解约订单 <AUTHOR>
   * API: /orderInstallment/{orderId}/merchantTerminated
   */
  merchantTerminated: (orderId, params) => {
    return postRequest(`/orderInstallment/${orderId}/merchantTerminated`, params);
  },

  /**
   * 添加订单备注 <AUTHOR>
   * API: /orderInstallment/{orderId}/remark
   */
  addRemark: (orderId, params) => {
    return postRequest(`/orderInstallment/${orderId}/remark`, params);
  },

  /**
   * 获取订单近期扣款日期 <AUTHOR>
   * API: /orderInstallment/{orderId}/deductDay
   */
  getDeductDay: (orderId) => {
    return getRequest(`/orderInstallment/${orderId}/deductDay`);
  },

  /**
   * 延期扣款 <AUTHOR>
   * API: /orderInstallment/{orderId}/delayDeduct
   */
  delayDeduct: (orderId, params) => {
    return postRequest(`/orderInstallment/${orderId}/delayDeduct`, params);
  },

  /**
   * 获取线下收款可选期数 <AUTHOR>
   * API: /orderInstallment/{orderId}/offlineInstallmentNo
   */
  getOfflineInstallmentOptions: (orderId) => {
    return getRequest(`/orderInstallment/${orderId}/offlineInstallmentNo`);
  },

  /**
   * 线下收款 <AUTHOR>
   * API: /orderInstallment/{orderId}/offlinePayment
   */
  offlinePayment: (orderId, params) => {
    return postRequest(`/orderInstallment/${orderId}/offlinePayment`, params);
  },

  /**
   * 获取合同预览链接 <AUTHOR>
   * API: /contract/getContractDownloadUrl
   */
  getContractPreviewUrl: (orderId) => {
    return getRequest(`/contract/getContractDownloadUrl?orderId=${orderId}`);
  },

  /**
   * 下载合同 <AUTHOR>
   */
  downloadContract: (orderId) => {
    return postDownload('/admin-api/business/segment-order/download-contract', { orderId });
  },
}; 