/*
 * 解约申请 API
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2025-01-27 15:00:00
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { postRequest, getRequest, postDownload } from '/@/lib/axios';

export const cancellationRequestApi = {
  /**
   * 查询解约申请列表 <AUTHOR>
   */
  queryList: (params) => {
    return postRequest('/afterSales/queryPage', params);
  },

  /**
   * 获取解约申请详情 <AUTHOR>
   */
  getDetail: (afterSalesId) => {
    return getRequest(`/afterSales/${afterSalesId}`);
  },

  /**
   * 审核解约申请 <AUTHOR>
   */
  handle: (params) => {
    return postRequest('/afterSales/handle', params);
  },

  /**
   * 获取审核历史 <AUTHOR>
   */
  getApprovalHistory: (requestId) => {
    return getRequest(`/admin-api/business/cancellation-request/approval-history/${requestId}`);
  },

  /**
   * 导出解约申请 <AUTHOR>
   */
  export: (params) => {
    return postDownload('/afterSales/export', params);
  },

  /**
   * 批量审核解约申请 <AUTHOR>
   */
  batchApprove: (params) => {
    return postRequest('/admin-api/business/cancellation-request/batch-approve', params);
  },

  /**
   * 获取统计信息 <AUTHOR>
   */
  getStatistics: () => {
    return getRequest('/admin-api/business/cancellation-request/statistics');
  },
}; 