/*
 * 银行卡授权 API
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2025-01-27 16:00:00
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import { postRequest, getRequest, postDownload } from '/@/lib/axios';

export const bankCardAuthorizationApi = {
  /**
   * 银行卡授权分页查询 <AUTHOR>
   * API: /orderInstallment/bankcardPage
   */
  queryList: (params) => {
    return postRequest('/orderInstallment/bankcardPage', params);
  },

  /**
   * 获取银行卡授权详情 <AUTHOR>
   */
  getDetail: (authorizationId) => {
    return getRequest(`/admin-api/business/bank-card-authorization/detail/${authorizationId}`);
  },

  /**
   * 解绑银行卡 <AUTHOR>
   * API: /orderInstallment/cancelBind/{customerBankcardId}
   */
  unbind: (customerBankcardId) => {
    return postRequest(`/orderInstallment/cancelBind/${customerBankcardId}`);
  },

  /**
   * 批量解绑银行卡 <AUTHOR>
   */
  batchUnbind: (authorizationIdList) => {
    return postRequest('/admin-api/business/bank-card-authorization/batch-unbind', authorizationIdList);
  },

  /**
   * 获取用户订单列表 <AUTHOR>
   */
  getUserOrders: (userId) => {
    return getRequest(`/admin-api/business/bank-card-authorization/user-orders/${userId}`);
  },

  /**
   * 导出银行卡授权 <AUTHOR>
   */
  export: (params) => {
    return postDownload('/admin-api/business/bank-card-authorization/export', params);
  },

  /**
   * 获取统计信息 <AUTHOR>
   */
  getStatistics: () => {
    return getRequest('/admin-api/business/bank-card-authorization/statistics');
  },

  /**
   * 强制解绑银行卡 <AUTHOR>
   */
  forceUnbind: (params) => {
    return postRequest('/admin-api/business/bank-card-authorization/force-unbind', params);
  },

  /**
   * 获取授权历史 <AUTHOR>
   */
  getAuthorizationHistory: (userId) => {
    return getRequest(`/admin-api/business/bank-card-authorization/history/${userId}`);
  },

  /**
   * 查询关联订单列表 <AUTHOR>
   */
  queryOrderList: (params) => {
    return postRequest('/admin-api/business/bank-card-authorization/order-list', params);
  },

  /**
   * 查询扣款记录列表 <AUTHOR>
   */
  queryDeductionList: (params) => {
    return postRequest('/admin-api/business/bank-card-authorization/deduction-list', params);
  },
}; 