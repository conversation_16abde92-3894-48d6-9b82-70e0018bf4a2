import { postRequest, getRequest, getDownload } from '/@/lib/axios'

export const complaintApi = {
  // 查询投诉列表
  queryComplaintList: (params) => {
    return postRequest('/complaint/queryPage', params)
  },

  // 处理投诉
  handleComplaint: (data) => {
    return postRequest('/complaint/handle', data)
  },

  // 批量处理投诉
  batchHandleComplaint: (data) => {
    return postRequest('/complaint/batchHandle', data)
  },

  // 获取投诉详情
  getComplaintDetail: (id) => {
    return getRequest(`/complaint/${id}`)
  },

  // 删除投诉记录
  deleteComplaint: (id) => {
    return getRequest(`/complaint/delete/${id}`)
  },

  // 批量删除投诉
  batchDeleteComplaint: (idList) => {
    return postRequest('/complaint/batchDelete', idList)
  },

  // 导出投诉数据
  exportComplaint: (params) => {
    return getDownload('/complaint/export', params)
  }
} 