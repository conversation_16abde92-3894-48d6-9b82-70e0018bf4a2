/*
 * 英文国际化
 *
 * @Author:    1024创新实验室-主任：卓大
 * @Date:      2022-09-06 20:00:57
 * @Wechat:    zhuda1024
 * @Email:     <EMAIL>
 * @Copyright  1024创新实验室 （ https://1024lab.net ），Since 2012
 */
import antd from 'ant-design-vue/es/locale/en_US';
import dayjs from 'dayjs/locale/eu';

export default {
  antdLocale: antd,
  dayjsLocale: dayjs,
  'setting.title': 'Setting',
  'setting.table.yHeight': 'Table Height',
  'setting.pagetag.location': 'TagPage Position',
  'setting.color': 'Theme Color',
  'setting.menu.layout': 'Menu Layout',
  'setting.menu.width': 'Menu Width',
  'setting.menu.theme': 'Menu Theme',
  'setting.page.width': 'Page Width',
  'setting.border.radius': 'Border Radius',
  'setting.compact': 'Page Compact',
  'setting.bread': 'Show Bread',
  'setting.flatPattern': 'Flat Pattern',
  'setting.pagetag': 'Show PageTag',
  'setting.pagetag.style': 'PageTag Style',
  'setting.footer': 'Show Footer',
  'setting.helpdoc': 'Show Helpdoc',
  'setting.helpdoc.expand': 'Helpdoc Expand',
  'setting.watermark': 'Show Watermark',
};
